<?php
namespace TC_Aluminium\customizer;

use WP_Customize_Image_Control;

Class Customizer{
    function __construct(){
        add_action( 'customize_register', [$this,'bio_oli_customizer'] );
    }

    function bio_oli_customizer($wp_customize){
        /* Create Panel Footer Option*/
        $wp_customize->add_panel(
        // $id
            'theme_option',
            // $args
            array(
                'priority' 			=> 11,
                'capability' 		=> 'edit_theme_options',
                'theme_supports'	=> '',
                'title' 			=> __( 'Theme Opitons', 'bio-oli' ),
                'description' 		=> __( 'Theme option', 'bio-oli' ),
            )
        );
        $wp_customize->add_section(
            'footer_option',
            array(
                'title' => esc_html__( 'Footer Option', 'bio-oli' ),
                'panel'   =>  'theme_option',
                'priority' => 150
            )
        );
		$wp_customize->add_section(
            'header_option',
            array(
                'title' => esc_html__( 'Header Option', 'bio-oli' ),
                'panel'   =>  'theme_option',
                'priority' => 150
            )
        );
        $wp_customize->add_section(
            'list_woman_option',
            array(
                'title' => esc_html__( 'List Women Option', 'bio-oli' ),
                'panel'   =>  'theme_option',
                'priority' => 150
            )
        );
        $wp_customize->add_section(
            'about_us_option',
            array(
                'title' => esc_html__( 'About Us Option', 'bio-oli' ),
                'panel'   =>  'theme_option',
                'priority' => 150
            )
        );
        $wp_customize->add_section(
            'career_option',
            array(
                'title' => esc_html__( 'Career Option', 'bio-oli' ),
                'panel'   =>  'theme_option',
                'priority' => 150
            )
        );
        /* Create field for email website */
        $wp_customize->add_setting( 'email_footer1', array(
            'capability' => 'edit_theme_options',
            'default' => '<EMAIL>',
            'sanitize_callback' => 'wp_kses_post',
        ) );
        $wp_customize->add_control( 'email_footer1', array(
            'type' => 'text',
            'section' => 'footer_option', // // Add a default or your own section
            'label' => __( 'eMail General' ),
        ) );
        $wp_customize->add_control( 'email_footer2', array(
            'type' => 'text',
            'section' => 'footer_option', // // Add a default or your own section
            'label' => __( 'eMail Sales' ),
        ) );
        /* Create field for phone number */
        $wp_customize->add_setting( 'phone_footer', array(
            'capability' => 'edit_theme_options',
            'default' => '(65) 8123-4567',
            'sanitize_callback' => 'sanitize_text_field',
        ) );
        $wp_customize->add_control( 'phone_footer', array(
            'type' => 'text',
            'section' => 'footer_option', // // Add a default or your own section
            'label' => __( 'Phone Footer ' ),
        ) );
        /* Create field for address */
        $wp_customize->add_setting( 'address_footer', array(
            'capability' => 'edit_theme_options',
            'default' => 'Singapore Marina, S 98380',
            'sanitize_callback' => '',
        ) );
        $wp_customize->add_control( 'address_footer', array(
            'type' => 'text',
            'section' => 'footer_option', // // Add a default or your own section
            'label' => __( 'Address Footer ' ),
        ) );
	    /* Create field for Magazine Link */
	    $wp_customize->add_setting( 'footer_magazine_one', array(
		    'sanitize_callback' => 'esc_url_raw'
	    ));
	    $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'footer_magazine_one', array(
		    'label' => 'Ảnh bì Magazine 1',
		    'priority' => 20,
		    'section' => 'footer_option',
		    'settings' => 'footer_magazine_one',
		    'button_labels' => array(// All These labels are optional
			    'select' => 'Select Magazine Cover',
			    'remove' => 'Remove Magazine Cover',
			    'change' => 'Change Magazine Cover',
		    )
	    )));
	    $wp_customize->add_setting( 'footer_magazine_two', array(
		    'sanitize_callback' => 'esc_url_raw'
	    ));
	    $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'footer_magazine_two', array(
		    'label' => 'Ảnh bì Magazine 2',
		    'priority' => 20,
		    'section' => 'footer_option',
		    'settings' => 'footer_magazine_two',
		    'button_labels' => array(// All These labels are optional
			    'select' => 'Select Magazine Cover',
			    'remove' => 'Remove Magazine Cover',
			    'change' => 'Change Magazine Cover',
		    )
	    )));
	    $wp_customize->add_setting( 'footer_magazine_three', array(
		    'sanitize_callback' => 'esc_url_raw'
	    ));
	    $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'footer_magazine_three', array(
		    'label' => 'Ảnh bì Magazine 3',
		    'priority' => 20,
		    'section' => 'footer_option',
		    'settings' => 'footer_magazine_three',
		    'button_labels' => array(// All These labels are optional
			    'select' => 'Select Magazine Cover',
			    'remove' => 'Remove Magazine Cover',
			    'change' => 'Change Magazine Cover',
		    )
	    )));
		$wp_customize->add_setting( 'header_newspaper_image_desktop', array(
		    'sanitize_callback' => 'esc_url_raw'
	    ));
	    $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'header_newspaper_image_desktop', array(
		    'label' => 'Ảnh đặt báo Desktop',
		    'priority' => 20,
		    'section' => 'header_option',
		    'settings' => 'header_newspaper_image_desktop',
		    'button_labels' => array(// All These labels are optional
			    'select' => 'Select Image Newspaper Desktop',
			    'remove' => 'Remove Image Newspaper Desktop',
			    'change' => 'Change Image Newspaper Desktop',
		    )
	    )));

		$wp_customize->add_setting( 'header_newspaper_image_mb', array(
		    'sanitize_callback' => 'esc_url_raw'
	    ));
	    $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'header_newspaper_image_mb', array(
		    'label' => 'Ảnh đặt báo Mobile',
		    'priority' => 20,
		    'section' => 'header_option',
		    'settings' => 'header_newspaper_image_mb',
		    'button_labels' => array(// All These labels are optional
			    'select' => 'Select Image Newspaper Mobile',
			    'remove' => 'Remove Image Newspaper Mobile',
			    'change' => 'Change Image Newspaper Mobile',
		    )
	    )));

        $wp_customize->add_setting( 'logo_list_women_page_detail', array(
            'sanitize_callback' => 'esc_url_raw'
        ));
        $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'logo_list_women_page_detail', array(
            'label' => 'Logo Top 20 Người Phụ Nữ',
            'priority' => 20,
            'section' => 'list_woman_option',
            'settings' => 'logo_list_women_page_detail',
            'button_labels' => array(// All These labels are optional
                'select' => 'Select Logo',
                'remove' => 'Remove Logo',
                'change' => 'Change Logo',
            )
        )));

        $wp_customize->add_setting( 'image_team_member_default_avatar', array(
            'sanitize_callback' => 'esc_url_raw'
        ));
        $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'image_team_member_default_avatar', array(
            'label' => 'Default Profile Avatar',
            'priority' => 20,
            'section' => 'about_us_option',
            'settings' => 'image_team_member_default_avatar',
            'button_labels' => array(// All These labels are optional
                'select' => 'Select Default Profile Image',
                'remove' => 'Remove Default Profile Image',
                'change' => 'Change Default Profile Image',
            )
        )));

        /* Create field for Career email */
        $wp_customize->add_setting( 'career_email', array(
            'capability' => 'edit_theme_options',
            'default' => '<EMAIL>',
            'sanitize_callback' => 'wp_kses_post',
        ) );
        $wp_customize->add_control( 'career_email', array(
            'type' => 'text',
            'section' => 'career_option', // // Add a default or your own section
            'label' => __( 'To' ),
        ) );
        /* Create field for Career subject */
        $wp_customize->add_setting( 'career_subject', array(
            'capability' => 'edit_theme_options',
            'default' => 'Apply for {title} - bio-oli.vn',
            'sanitize_callback' => '',
        ) );
        $wp_customize->add_control( 'career_subject', array(
            'type' => 'text',
            'section' => 'career_option', // // Add a default or your own section
            'label' => __( 'Subject ' ),
        ) );
    }
}