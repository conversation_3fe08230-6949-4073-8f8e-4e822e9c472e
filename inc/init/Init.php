<?php
namespace TC_Aluminium\init;

use WP_Customize_Image_Control;

class Init
{
    public function __construct()
    {
        add_action('after_setup_theme', array($this, 'theme_support'));
        add_action('widgets_init', array($this, 'unregister_widgets'));
        remove_action('wp_head', 'wp_shortlink_wp_head', 10, 0);
        add_filter('xmlrpc_enabled', '__return_false');
        add_action('wp_before_admin_bar_render', array($this, 'remove_admin_bar_links'));
        remove_action('wp_head', 'wp_resource_hints', 2);
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'rest_output_link_wp_head');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'wp_generator');
        add_theme_support('post-thumbnails');
        add_theme_support('title-tag');
        add_theme_support('align-wide');
        add_theme_support('html5', array('search-form', 'gallery', 'caption'));
        add_theme_support('custom-logo', array(
            'height' => 100,
            'width' => 400,
            'flex-height' => true,
            'flex-width' => true,
            'header-text' => array('site-title', 'site-description'),
        )
        );
        add_action('wp_dashboard_setup', [$this, 'remove_dashboard_widgets']);
        add_filter('upload_mimes', [$this, 'add_upload_mimes']);
        add_action('customize_register', [$this, 'tc_add_second_logo']);
        add_action('after_setup_theme', [$this, 'wp_graphql_theme_setup']);
        add_action('wp_enqueue_scripts', [$this, 'wp_graphql_enqueue_scripts']);
        add_filter('script_loader_tag', [$this, 'wp_graphql_defer_scripts'], 10, 3);
        add_action('acf/init',[$this,'init_options_page']);
    }

    /**
     * Sets up supports and registers menus
     */
    function wp_graphql_theme_setup()
    {
        global $theme_name;
        /*
         * Make theme available for translation.
         * Translations can be filed in the /languages/ directory.
         */
        load_theme_textdomain($theme_name, get_template_directory() . '/languages');

        // Add default posts and comments RSS feed links to head.
        add_theme_support('automatic-feed-links');

        /*
         * Enable support for Post Thumbnails on posts and pages.
         *
         * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
         */
        add_theme_support('post-thumbnails');

        /**
         * Add support for core custom logo.
         *
         * @link https://codex.wordpress.org/Theme_Logo
         */
        add_theme_support('custom-logo');

        /**
         * Register Menu
         */
        register_nav_menus(
            [
                'main' => __('Main Menu'),
                'social' => __('Social Links'),
            ]
        );

        // Adding support for core block visual styles.
        add_theme_support('wp-block-styles');

        // Add support for custom color scheme.
        add_theme_support('editor-color-palette', array(
            array(
                'name' => __('Strong Blue', 'gutenbergtheme'),
                'slug' => 'strong-blue',
                'color' => '#0073aa',
            ),
            array(
                'name' => __('Lighter Blue', 'gutenbergtheme'),
                'slug' => 'lighter-blue',
                'color' => '#229fd8',
            ),
            array(
                'name' => __('Very Light Gray', 'gutenbergtheme'),
                'slug' => 'very-light-gray',
                'color' => '#eee',
            ),
            array(
                'name' => __('Very Dark Gray', 'gutenbergtheme'),
                'slug' => 'very-dark-gray',
                'color' => '#444',
            ),
        ));

        $GLOBALS['content_width'] = apply_filters('gutenbergtheme_content_width', 640);

    }

    static function wp_graphql_get_context($server = false)
    {
        global $app_context;
        $request_uri = $_SERVER['REQUEST_URI'];

        if ($app_context === null) {
            $app_context = [
                'HOME_URL' => home_url(),
                'APP_TITLE' => get_bloginfo('name'),
                'LOCATION' => $request_uri,
                'SERVER_SCRIPT_PATH' => get_template_directory() . '/build/js/server.js',
                'CLIENT_SCRIPT_URI' => get_template_directory_uri() . '/build/js/client.js',
                //			'VENDOR_SCRIPT_URI' 	=> get_template_directory_uri() . '/build/vendors~main.client.js',
                'THEME_DIR' => get_template_directory_uri(),
                "SLUG" => get_permalink()
            ];
        }

        return array_merge(
            $app_context,
            [
                // in a local dev environment change home_url('/') to http://127.0.0.1/ and vice-versa
                'ENDPOINT' => home_url('/') . apply_filters('graphql_endpoint', 'graphql'),
            ]
        );
    }

    function wp_graphql_enqueue_scripts()
    {
        global $theme_version;

        // Get request context and enqueue required styles/scripts
        $context = self::wp_graphql_get_context();
        wp_enqueue_script(
            'wp-graphql-client',
            $context['CLIENT_SCRIPT_URI'],
            [],
            $theme_version,
            true
        );
        wp_localize_script('wp-graphql-client', 'context', $context);

        //Dequeue unnecessary scripts
        wp_dequeue_script('react');
        wp_dequeue_script('react-dom');
    }

    function wp_graphql_defer_scripts($tag, $handle, $src)
    {
        // the handles of the enqueued scripts we want to async
        $async_scripts = array('wp-graphql-client');

        if (in_array($handle, $async_scripts)) {
            return preg_replace('/(\'>)/', '\' defer>', $tag);
        }

        return $tag;
    }

    function tc_add_second_logo($wp_customize)
    {
        $wp_customize->add_setting('footer_logo');
        $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'footer_logo', array(
            'label' => __('Footer Logo', 'tc'),
            'section' => 'title_tagline',
            'settings' => 'footer_logo',
            'priority' => 9,
        )
        ));
    }

    function add_upload_mimes($mimes)
    {
        $mimes['svg'] = 'image/svg+xml';
        $mimes['svgz'] = 'image/svg+xml';
        return $mimes;
    }

    public function remove_dashboard_widgets()
    {
        global $wp_meta_boxes;

        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_quick_press']);
        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_incoming_links']);
        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_right_now']);
        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_plugins']);
        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_drafts']);
        unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_comments']);
        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_primary']);
        unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_secondary']);
        remove_meta_box('dashboard_site_health', 'dashboard', 'normal');
        remove_meta_box('dashboard_activity', 'dashboard', 'normal');
        remove_meta_box('wpseo-dashboard-overview', 'dashboard', 'normal');
    }

    public function unregister_widgets()
    {
        unregister_widget('WP_Widget_Pages');
        unregister_widget('WP_Widget_Calendar');
        unregister_widget('WP_Widget_Archives');
        unregister_widget('WP_Widget_Links');
        unregister_widget('WP_Widget_Meta');
        unregister_widget('WP_Widget_Search');
        unregister_widget('WP_Widget_Categories');
        unregister_widget('WP_Widget_Recent_Posts');
        unregister_widget('WP_Widget_Recent_Comments');
        unregister_widget('WP_Widget_RSS');
        unregister_widget('WP_Widget_Tag_Cloud');
        unregister_widget('WP_Nav_Menu_Widget');
    }

    public function theme_support()
    {
        register_nav_menus(
            array(
                'main_nav' => 'Menu header',
                'footer_nav' => 'Menu Footer',
            )
        );
        add_theme_support('title-tag');
    }

    public function remove_admin_bar_links()
    {
        global $wp_admin_bar;
        $wp_admin_bar->remove_menu('wp-logo'); /** Remove the WordPress logo **/
        $wp_admin_bar->remove_menu('wporg'); /** Remove the WordPress.org link **/
        $wp_admin_bar->remove_menu('documentation'); /** Remove the WordPress documentation link **/
        $wp_admin_bar->remove_menu('support-forums'); /** Remove the support forums link **/
        $wp_admin_bar->remove_menu('feedback'); /** Remove the feedback link **/
        $wp_admin_bar->remove_menu('comments'); /** Remove the comments link **/
    }

    function init_options_page()
    {
        if (function_exists('acf_add_options_page')) {
            acf_add_options_page(array(
                'page_title'    => 'Theme General Settings',
                'menu_title'    => 'Theme Settings',
                'menu_slug'     => 'theme-general-settings',
                'capability'    => 'edit_posts',
                'redirect'      => true,
                'icon_url'      => 'dashicons-screenoptions',
                'position'      => 22,
                'show_in_graphql'    => true,
            ));
        }
    }
}