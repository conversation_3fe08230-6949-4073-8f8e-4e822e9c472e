<?php

namespace TC_Aluminium\admin;


class Admin{

    function __construct(){

        add_action( 'admin_menu', [$this,'import_json_admin'] );
        add_action("wp_ajax_import_data", [$this,"ajax_import_data"]);
        add_action("wp_ajax_nopriv_import_data", [$this,"ajax_import_data"]);
    }


    function ajax_import_data(){
        if ( !wp_verify_nonce( $_REQUEST['nonce'], "import_file_nonce")) {
            exit("You not allow to do this");
        }

        $DATA = $_REQUEST['data'];
        $cats = $_REQUEST['cats'];

        $res = [];

        foreach ($DATA[0] as $data){
            if($data['title']){
                $id = wp_insert_post([
                    'post_title'    => wp_strip_all_tags( $data['title'] ),
                    'post_status'   => 'publish',
                    'post_type'     => 'product',
                    'tax_input'    => [
                        'categories-product' => array_map('intval', $cats)
                    ],
                    'meta_input'   => array(
                        'width' => $data['width'],
                        'height' => $data['height'],
                        'radius' => $data['radius'],
                        'thickness' => $data['thickness'],
                        'weight' => $data['weight'],
                        'length' => $data['length'],
                        'uom' => $data['uom'],
                    ),
                ]);
                $res[] = [
                    'title' => $data['title'],
                    'width' => $data['width'],
                    'height' => $data['height'],
                    'radius' => $data['radius'],
                    'thickness' => $data['thickness'],
                    'weight' => $data['weight'],
                    'length' => $data['length'],
                    'uom' => $data['uom'],
                    'id' => $id
                ];
            }
        }

        $result = json_encode([
           'code' => 200,
           'res' => $res

        ]);
        echo $result;
        die();
    }

    function import_json_admin() {
        add_menu_page( 'Import JSON from DATA', 'Import JSON', 'manage_options','import-data-from-json' ,  [$this,'render_admin_page'], 'dashicons-tickets', 6  );
    }

    function render_admin_page(){ ?>
      <div>
          <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/styles/ocean.min.css">
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/izitoast/1.4.0/css/iziToast.min.css" />
          <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/highlight.min.js"></script>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/izitoast/1.4.0/js/iziToast.min.js"></script>
          <h1>
              Import JSON to populate data
          </h1>
          <style>

              ul.form-content, ul.form-content ul {
                  list-style: none;
                  margin: 0;
                  padding: 0;
              }
              ul.form-content ul {
                  margin-left: 10px;
              }
              ul.form-content li {
                  margin: 0;
                  padding: 0 7px;
                  line-height: 20px;
                  color: #369;
                  font-weight: bold;
                  border-left:1px solid rgb(100,100,100);

              }
              ul.form-content li:last-child {
                  border-left:none;
              }
              ul.form-content li:before {
                  position:relative;
                  top:-0.3em;
                  height:1em;
                  width:12px;
                  color:white;
                  border-bottom:1px solid rgb(100,100,100);
                  content:"";
                  display:inline-block;
                  left:-7px;
              }
              ul.form-content li:last-child:before {
                  border-left:1px solid rgb(100,100,100);
              }
              pre {
                  background-color: ghostwhite;
                  border: 1px solid silver;
                  padding: 10px 20px;
                  border-radius: 4px;
                  width: 100%;
                  margin: 20px 0;
                  height: 500px;
                  overflow-y: auto;
                  box-sizing: border-box;
              }
              code{
                  background: transparent;
                  direction: rtl;
                  unicode-bidi: plaintext;
              }
          </style>
          <div style="display: flex">
              <div class="form-categories" style="width: 300px">
                  <h3 style="padding-top: 30px">
                      Choose Categories of list product
                  </h3>
                  <ul class="form-content">
                      <?php
                      wp_terms_checklist(0,[
                          'taxonomy' => 'categories-product'
                      ])
                      ?>
                  </ul>
              </div>
              <div class="form-upload-file-json" style="width: 500px">
                  <h3 style="padding-top: 30px;">
                      Choose JSON want import
                  </h3>
                  <label for="json-file">
                      <input id="json-file" type="file" accept="application/ld+json">
                  </label>
                  <pre><code id="code-block">
                        // Your data
                    </code></pre>
              </div>
              <div class="form-submit">
                  <h3 style="padding-top: 30px;margin-left: 20px">
                      Import your data
                  </h3>
                  <button class="button-primary" style="margin-left: 20px">
                      Import
                  </button>
              </div>
          </div>
      </div>
      <script type="application/javascript">
          let fileInput = document.querySelector('#json-file')
          let codeBlock = document.querySelector('#code-block')
          let submitBtn = document.querySelector('button[class="button-primary"]')
          let dataImport = []
          fileInput.addEventListener('change',async function () {
              const BLOB = URL.createObjectURL(this.files[0])
              const DATA = await fetch(BLOB).then(res => res.text())
              const OBJECT = JSON.parse(DATA)
              dataImport = [...dataImport,OBJECT]
              codeBlock.innerHTML = JSON.stringify(OBJECT,null," ")
              hljs.highlightBlock(document.querySelector('code'))
          })

          submitBtn.addEventListener('click',function (event) {
              event.preventDefault()
              let catInput = Array.prototype.slice.call(
                  document.querySelectorAll('[name="tax_input[categories-product][]"]:checked')
              )
              if(!catInput.length){
                  iziToast.error({
                      title: 'Error',
                      message: 'Please choose Categories of data',
                  })
              }

              if(!dataImport.length){
                  iziToast.error({
                      title: 'Error',
                      message: 'Please upload data before submit',
                  })
              }

              if(dataImport.length && catInput.length){
                jQuery.ajax({
                    type : "post",
                    dataType : "json",
                    url : '<?php echo admin_url('admin-ajax.php') ?>',
                    data : {
                        action: "import_data",
                        data: dataImport,
                        cats: catInput.map(item => parseInt(item.value)),
                        nonce: "<?php  echo wp_create_nonce("import_file_nonce"); ?>"
                    },
                    success: function(response) {
                        console.log(response)
                        iziToast.success({
                            title: 'OK',
                            message: 'Import success',
                        })
                    }
                })
              }
          })
      </script>
    <?php
    }
}
