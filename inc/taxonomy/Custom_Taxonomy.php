<?php
namespace TC_Aluminium\taxonomy;

class Custom_Taxonomy{
    public $title;
    public $name;
    public $slug;
    public $config;
    public $post_type;

    function __construct($title,$name,$slug,$config,$post_type){
        $this->setTitle($title);
        $this->setSlug($slug);
        $this->setConfig($config);
        $this->setName($name);
        $this->setPostType($post_type);
        register_taxonomy( $this->getName(),$this->getPostType(),$this->getArgs());
    }

    /**
     * @param mixed $title
     */
    public function setTitle($title): void
    {
        $this->title = $title;
    }

    /**
     * @return mixed
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @return mixed
     */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
     * @param mixed $slug
     */
    public function setSlug($slug): void
    {
        $this->slug = $slug;
    }
    /**
     * @return array
     */
    public function getLabel(): array
    {
        return [
            'name' => _x( $this->getTitle(), 'bio-oli' ),
            'singular_name' => _x( $this->getTitle(), 'bio-oli' ),
            'search_items' =>  __( 'Search '.$this->getTitle() ),
            'all_items' => __( 'All '.$this->getTitle() ),
            'parent_item' => __( 'Parent '.$this->getTitle() ),
            'parent_item_colon' => __( 'Parent '.$this->getTitle().':' ),
            'edit_item' => __( 'Edit '.$this->getTitle() ),
            'update_item' => __( 'Update '.$this->getTitle() ),
            'add_new_item' => __( 'Add New '.$this->getTitle() ),
            'new_item_name' => __( 'New '.$this->getTitle().' Name' ),
            'menu_name' => __( $this->getTitle() ),
        ];
    }

    /**
     * @param mixed $config
     */
    public function setConfig($config): void
    {
        $this->config = $config;
    }

    /**
     * @return mixed
     */
    public function getConfig()
    {
        return $this->config;
    }

    public function  getArgs(): array
    {
        return array_merge(
            [
                'labels'                => $this->getLabel(),
                'hierarchical'          => true,
                'show_ui'               => true,
                'query_var'             => true,
                'show_in_rest' =>       true,
                'update_count_callback' => '_update_post_term_count',
                'show_admin_column'     => true,
                'rewrite'               => [
                        'slug'          => $this->getSlug()
                ],
            ],
            $this->getConfig()
        );
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getPostType()
    {
        return $this->post_type;
    }

    /**
     * @param mixed $post_type
     */
    public function setPostType($post_type): void
    {
        $this->post_type = $post_type;
    }
}