<?php
namespace TC_Aluminium\taxonomy;
use TC_Aluminium\taxonomy\Categories\Categories_Taxonomy;

class Taxonomy{
    function __construct()
    {
        add_action( 'init', [$this,'init_taxonomy'], 0 );
    }

    public function init_taxonomy(){
       new Categories_Taxonomy('Category','categories-slider','categories-slider',[
        'show_in_graphql' => true,
        'graphql_single_name' => 'categoriesSlider',
        'graphql_plural_name' => 'categoriesSlider',
       ],'carousel');
       new Categories_Taxonomy('Category','categories-product','products',[
        'hierarchical'      => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'categoriesProduct',
        'graphql_plural_name' => 'categoriesProduct',
           'rewrite'           => array(
               'slug'         => 'products',
               'with_front'   => false, // Remove the base slug from the URL
               'hierarchical' => true, // Include parent and child terms in the URL
           ),
       ],'product');
       new Categories_Taxonomy('Category','categories-faq-list','categories-faq-list',[
        'show_in_graphql' => true,
        'graphql_single_name' => 'categoriesFaqList',
        'graphql_plural_name' => 'categoriesFaqList',
       ],'faq');
    //    new Categories_Taxonomy('Category','categories-grades','categories-grades',[
    //     'show_in_graphql' => true,
    //     'graphql_single_name' => 'categoriesGrades',
    //     'graphql_plural_name' => 'categoriesGrades',
    //    ],'product_grades');

    }

}