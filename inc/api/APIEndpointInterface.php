<?php

namespace TC_Aluminium\api;

/**
 * A WordPress REST API endpoint.
 */
interface APIEndpointInterface
{



    /**
     * Get the callback used by the REST API endpoint.
     *
     */

    public function callback();

    /**
     * Get the callback used by the REST API endpoint.
     *
     */

    public function prefix_callback();

    /**
     * Get the callback used to validate a request to the REST API endpoint.
     *
     */
    public function get_permission_callback();

    /**
     * Get the HTTP methods that the REST API endpoint responds to.
     *
     * @return mixed
     */
    public function get_methods();

}
