<?php

namespace TC_Aluminium\api\contact_entries;

use TC_Aluminium\api\APIEndpointInterface;
use TC_Aluminium\api\Entry_Route;

final class Save_Newsletter_Entries extends Entry_Route implements APIEndpointInterface {

    function __construct( $entry, $version, $route, $method, $permission_callback ) {
        parent::__construct( $entry, $version, $route, $method, $permission_callback );
    }

    public function prefix_callback() {
        $contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';
        $result = [
            'message' => "Token isn't right",
            'data' => [],
            'error' => 'Missing nonce key',
            'code' => 400,
        ];
        if ($contentType === "application/json") {
            //Receive the RAW post data.
            $content = trim(file_get_contents("php://input"));
            $decoded = json_decode($content, true);
            $data = $decoded['value'];
            // setup default result data
            if (wp_verify_nonce($decoded['wp_rest'], 'wp_rest')) {
                $first_name = esc_html($data['first_name']);
                $last_name = esc_html($data['last_name']);
                $email = esc_html($data['email']);
                $phone = esc_html($data['phone']);
                $message = esc_html($data['message']);

                $id = wp_insert_post([
                    'post_title'   => 'Contact Entries - '.$first_name.' '.$last_name.' - Date: '.date('d M Y h:i:s:a'),
                    'post_type'    => 'contact_entries',
                    'meta_input'   => [
                        'first_name'   => $first_name,
                        'last_name'   => $last_name,
                        'email'       => $email,
                        'phone_number' => $phone,
                        'message'   => $message,
                        'post_status'   => 'publish',
                    ]
                ]);

                if($id){
                    $result['error'] =  null;
                    $result['message'] =  'Saved';
                    $result['code'] =  200;
                }
            }
        }
        $this->api_return_json($result);
    }

}
