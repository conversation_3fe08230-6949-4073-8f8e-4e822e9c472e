<?php
namespace TC_Aluminium\api\contact_entries;

use TC_Aluminium\api\APIEndpointInterface;
use TC_Aluminium\api\Entry_Route;
use WP_Query;


final class Save_Contact_Entries extends Entry_Route implements APIEndpointInterface {

	function __construct( $entry, $version, $route, $method, $permission_callback ) {
		parent::__construct( $entry, $version, $route, $method, $permission_callback );
	}

	public function prefix_callback() {
		$contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';
		$result = [
			'message' => "Token isn't right",
			'data' => [],
			'error' => 'Missing nonce key',
			'code' => 400,
		];
		if ($contentType === "application/json") {
			//Receive the RAW post data.
			$content = trim(file_get_contents("php://input"));
			$decoded = json_decode($content, true);
			$data = $decoded['value'];
			// setup default result data
			if (wp_verify_nonce($decoded['wp_rest'], 'wp_rest')) {
                $name = esc_html($data['name']);
                $email = esc_html($data['email']);

                $id = wp_insert_post([
                    'post_title'   => 'Newsletter Entries - '.$name.' - Date: '.date('d M Y h:i:s:a'),
                    'post_type'    => 'newsletter_entries',
                    'meta_input'   => [
                        'email'       => $email,
                        'post_status'   => 'publish',
                    ]
                ]);

                if($id){
                    $result['error'] =  null;
                    $result['message'] =  'Saved';
                    $result['code'] =  200;
                }
			}
		}
		$this->api_return_json($result);
	}

}
