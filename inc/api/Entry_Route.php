<?php

namespace TC_Aluminium\api;

class Entry_Route extends Entry_Point{

    public $route;
    public $method;
    public $permission_callback;

    public function __construct($entry,$version,$route,$method,$permission_callback) {
        /* Get data input */
        parent::__construct($entry,$version);
        $this->method = $method;
        $this->permission_callback = $permission_callback;
        $this->route = $route;
        /* Register Api method*/
        add_action('rest_api_init', array($this, 'init_api'));
    }

    public function init_api(){
        register_rest_route($this->entry.'/v'.$this->version, '/'.$this->route, array(
            'methods' => $this->get_methods(),
            'callback' => array($this, 'callback'),
            'permission_callback' => $this->get_permission_callback()
        ));
    }

    public function callback()
    {
        $request = $this->prefix_callback();
        return rest_ensure_response($request);
    }


    public function get_permission_callback(): string
    {
        $result = '__return_false';
        if($this->permission_callback){
            $result = '__return_true';
        }
        return $result;
    }

    public function get_methods()
    {
        return $this->method;
    }
}