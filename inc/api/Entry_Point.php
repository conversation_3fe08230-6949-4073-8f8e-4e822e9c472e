<?php
namespace TC_Aluminium\api;

class Entry_Point{

    public $entry;
    public $version;

    function __construct($entry,$version){
        /* Get data input */
        $this->entry = $entry;
        $this->version = (int)$version;
        /* Register Api Entry Point*/
    }

    function api_return_json( $php_array ) {
        // encode result as json string
        $json_result = json_encode( $php_array );
        // return result
        die( $json_result );
        // stop all other processing
        exit;
    }

}