<?php
namespace TC_Aluminium\misc;

class Form_Field_Settings
{
    function __construct()
    {
        $this->register_form_graphql();
        $this->deregister_form_styles();
    }

    function register_form_graphql()
    {
        add_filter('register_post_type_args', function ($args, $post_type) {
            if ('formello_form' === $post_type) {
                $args['show_in_graphql'] = true;

                // Optionally customize the rest_base or rest_controller_class
                $args['graphql_single_name'] = 'form';
                $args['graphql_plural_name'] = 'forms';
            }

            return $args;
        }, 10, 2);
    }

    function deregister_form_styles(){
        add_action( 'wp_enqueue_scripts', function(){
            wp_dequeue_style('formello-form-style');
            wp_dequeue_style( 'wp-block-library' );
            wp_dequeue_style( 'wp-block-library-theme' );
        }, 100 );
    }
}