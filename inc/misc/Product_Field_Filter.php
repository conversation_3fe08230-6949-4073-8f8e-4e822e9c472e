<?php
namespace TC_Aluminium\misc;

class Product_Field_Filter{
    function __construct() {    
        $this->product_field_filter();
    }

    function product_field_filter(){
        add_action('graphql_register_types', function () {

            $customposttype_graphql_single_name = "Product";
        
            register_graphql_field('RootQueryTo' . $customposttype_graphql_single_name . 'ConnectionWhereArgs', 'height', [
                'type' => 'Float',
                'description' => __('The height of the product object to filter by', 'your-textdomain'),
            ]);
        });
        add_action('graphql_register_types', function () {
        
            $customposttype_graphql_single_name = "Product";
        
            register_graphql_field('RootQueryTo' . $customposttype_graphql_single_name . 'ConnectionWhereArgs', 'width', [
                'type' => 'Float',
                'description' => __('The width of the product object to filter by', 'your-textdomain'),
            ]);
        });
        add_action('graphql_register_types', function () {
        
            $customposttype_graphql_single_name = "Product";
        
            register_graphql_field('RootQueryTo' . $customposttype_graphql_single_name . 'ConnectionWhereArgs', 'thickness', [
                'type' => 'Float',
                'description' => __('The thickness of the product object to filter by', 'your-textdomain'),
            ]);
        });
        
        add_filter(
            'graphql_post_object_connection_query_args',
            function ($query_args, $source, $args, $context, $info) {
        
                $post_object_id = $args['where']['height'];
                $post_object_width_id = $args['where']['width'];
        
                if (isset($post_object_id)) {
                    $query_args['meta_query'] = [
                        'relation' => 'OR',
                        [
                            'key' => 'height',
                            'value' => $post_object_id,
                            'compare' => 'LIKE'
                        ],
                        [
                            'key' => 'width',
                            'value' => $post_object_width_id,
                            'compare' => 'LIKE'
                        ]
                    ];
                }
        
                return $query_args;
            }
            ,
            10,
            5
        );
        add_filter(
            'graphql_post_object_connection_query_args',
            function ($query_args, $source, $args, $context, $info) {
        
                $post_object_id = $args['where']['thickness'];
        
                if (isset($post_object_id)) {
                    $query_args['meta_query'] = [
                        [
                            'key' => 'thickness',
                            'value' => $post_object_id,
                            'compare' => 'LIKE'
                        ],
                    ];
                }
        
                return $query_args;
            }
            ,
            10,
            5
        );
    }
}