<?php

namespace TC_Aluminium\post_types;

use TC_Aluminium\post_types\contact_form\Contact_Post_Type;
use TC_Aluminium\post_types\faq\Faq_Post_Type;
use TC_Aluminium\post_types\grades\Grades_Post_Type;
use TC_Aluminium\post_types\product\Product_Post_Type;
use TC_Aluminium\post_types\shop\Shop_Post_Type;
use TC_Aluminium\post_types\solution\Solution_Post_Type;
use TC_Aluminium\post_types\modal\Modal_Post_Type;
use TC_Aluminium\post_types\carousel\Carousel_Post_Type;


class Post_Types
{

    function __construct()
    {
        add_action('init', [$this, 'init_post_type']);
        add_action('init', [$this, 'register_default_blocks_post'], 20);
        add_action('template_redirect', [$this, 'redirect_to_correct_shop_url']);
        add_action('wp_head', [$this, 'shop_custom_metadata']);
        add_filter('wpseo_sitemap_exclude_post_type', [$this, 'sitemap_exclude_shop_post_type'], 10, 2);
    }

    function init_post_type()
    {
        new Faq_Post_Type('FAQ', 'faq', 'faq', [
            'description' => 'Faq custom post type.',
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'capability_type' => 'post',
            'has_archive' => false,
            'hierarchical' => false,
            'menu_icon' => 'dashicons-editor-ol',
            'menu_position' => 4,
            'supports' => array('title','editor'),
            'show_in_rest' => true,
            'show_in_graphql' => true,
            'graphql_single_name' => 'faq',
            'graphql_plural_name' => 'faqs',

        ]);

        new Modal_Post_Type('Modal', 'modal', 'modal', [
            'description' => 'Modal custom post type.',
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'capability_type' => 'post',
            'has_archive' => false,
            'hierarchical' => false,
            'menu_icon' => 'dashicons-media-document',
            'menu_position' => 4,
            'supports' => array('title', 'editor'),
            'show_in_rest' => true,
            'show_in_graphql' => true,
            'graphql_single_name' => 'modal',
            'graphql_plural_name' => 'modals',

        ]);

        new Product_Post_Type('Product','product','products',[
            'description'        => 'Product custom post type.',
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => true,
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_icon'          => 'dashicons-products',
            'menu_position'      => 4,
            'supports'           => array( 'title'),
            'show_in_rest'       => true,
            'show_in_graphql'    => true,
            'graphql_single_name' => 'product',
            'graphql_plural_name' => 'products',
        ]);
        new Carousel_Post_Type('Carousels', 'carousel', 'carousel', [
            'description' => 'carousel custom post type.',
            'public' => false,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'capability_type' => 'post',
            'has_archive' => false,
            'hierarchical' => false,
            'menu_icon' => 'dashicons-slides',
            'menu_position' => 5,
            'supports' => array('title', 'custom-field'),
            'show_in_rest' => false,
            'show_in_graphql' => true,
            'graphql_single_name' => 'carousel',
            'graphql_plural_name' => 'carousels',
        ]);

        new Grades_Post_Type('Product Grades', 'product_grades', 'product_grades', [
            'description' => 'Grades custom post type.',
            'public' => false,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'capability_type' => 'post',
            'has_archive' => false,
            'hierarchical' => false,
            'menu_icon' => 'dashicons-info-outline',
            'menu_position' => 4,
            'supports' => array('title', 'editor'),
            'show_in_rest' => true, // to use Gutenberg
            'show_in_graphql' => true,
            'graphql_single_name' => 'grade',
            'graphql_plural_name' => 'grades',
        ]);
    }

    function disable_comments()
    {
        $post_types = get_post_types();
        foreach ($post_types as $post_type) {
            if (post_type_supports($post_type, 'comments')) {
                remove_post_type_support($post_type, 'comments');
                remove_post_type_support($post_type, 'trackbacks');
            }
        }
    }

    static function post_read_time($post): string
    {

        // get the post content
        $content = get_post_field('post_content', $post->ID);

        // count the words
        $word_count = str_word_count(strip_tags($content));

        // reading time itself
        $readingtime = ceil($word_count / 200);

        if ($readingtime == 1) {
            $timer = " min read";
        } else {
            $timer = " min read"; // or your version :) I use the same wordings for 1 minute of reading or more
        }

        // I'm going to print 'X minute read' above my post
        $totalreadingtime = $readingtime . $timer;

        return $totalreadingtime;
    }

    function register_default_blocks_post()
    {
        $template = [
            [
                'core/columns',
                ["columns" => 2],
                [
                    [
                        'core/column',
                        ["width" => "30%", "className" => "bio-oil-post-individual__social"],
                        [
                            [
                                'core/columns',
                                ["columns" => 1, "className" => "bio-oil-post-individual__social-sticky"],
                                [
                                    [
                                        'core/column',
                                        [],
                                        [
                                            ['ht/block-toc', ["allowUsertoggle" => false]],
                                            [
                                                'core/separator',
                                                [
                                                    "customColor" => "#e4e7ec",
                                                    "className" => "is-style-wide"
                                                ]
                                            ],
                                            [
                                                'bssb/social-share',
                                                [
                                                    'socials' => [
                                                        0 => [
                                                            'networkType' => 'socialNetworks',
                                                            'network' => 'twitter',
                                                            'isUpIcon' => false,
                                                            'icon' => [
                                                                'class' => 'fab fa-twitter',
                                                                'color' => 'rgba(152, 162, 179, 1)',
                                                                'styles' => 'color: rgba(152, 162, 179, 1);',
                                                                'name' => '',
                                                                'fontSize' => 16,
                                                                'colorType' => 'solid',
                                                                'gradient' => 'linear-gradient(135deg, #4527a4, #8344c5)',
                                                            ],
                                                            'upIcon' => [
                                                                'id' => NULL,
                                                                'url' => '',
                                                                'alt' => '',
                                                                'title' => '',
                                                            ],
                                                        ],
                                                        1 => [
                                                            'networkType' => 'socialNetworks',
                                                            'network' => 'facebook',
                                                            'isUpIcon' => false,
                                                            'icon' => [
                                                                'class' => 'fab fa-facebook',
                                                                'color' => 'rgba(152, 162, 179, 1)',
                                                                'styles' => 'color: rgba(152, 162, 179, 1);',
                                                                'name' => 'facebook',
                                                                'fontSize' => 16,
                                                                'colorType' => 'solid',
                                                                'gradient' => 'linear-gradient(135deg, #4527a4, #8344c5)',
                                                            ],
                                                            'upIcon' => [
                                                                'id' => NULL,
                                                                'url' => '',
                                                                'alt' => '',
                                                                'title' => '',
                                                            ],
                                                        ],
                                                        2 => [
                                                            'networkType' => 'socialNetworks',
                                                            'network' => 'linkedin',
                                                            'isUpIcon' => false,
                                                            'icon' => [
                                                                'class' => 'fab fa-linkedin',
                                                                'color' => 'rgba(152, 162, 179, 1)',
                                                                'styles' => 'color: rgba(152, 162, 179, 1);',
                                                                'name' => 'linkedin',
                                                                'fontSize' => 16,
                                                                'colorType' => 'solid',
                                                                'gradient' => 'linear-gradient(135deg, #4527a4, #8344c5)',
                                                            ],
                                                            'upIcon' => [
                                                                'id' => NULL,
                                                                'url' => '',
                                                                'alt' => '',
                                                                'title' => '',
                                                            ],
                                                        ],
                                                    ],
                                                    'background' => [
                                                        'type' => 'solid',
                                                        'color' => '#0000',
                                                        'gradient' => 'linear-gradient(135deg, #4527a4, #8344c5)',
                                                        'image' => [
                                                        ],
                                                        'position' => 'center center',
                                                        'attachment' => 'initial',
                                                        'repeat' => 'no-repeat',
                                                        'size' => 'cover',
                                                        'overlayColor' => '#000000b3',
                                                        'styles' => 'background-color: #0000;',
                                                    ],
                                                    'padding' => '12px',
                                                    'margin' => '12px',
                                                    'border' => [
                                                        'width' => '1px',
                                                        'color' => 'rgba(208, 213, 221, 1)',
                                                        'style' => 'solid',
                                                        'side' => 'all',
                                                        'radius' => '8px',
                                                        'styles' => 'border-top: 1px solid rgba(208, 213, 221, 1); border-right: 1px solid rgba(208, 213, 221, 1); border-bottom: 1px solid rgba(208, 213, 221, 1); border-left: 1px solid rgba(208, 213, 221, 1); border-radius: 8px;',
                                                    ],
                                                    'shadow' => [
                                                        'type' => 'box',
                                                        'hOffset' => '0px',
                                                        'vOffset' => '1px',
                                                        'blur' => '2px',
                                                        'spreed' => '0px',
                                                        'color' => 'rgba(16, 24, 40, 0.85)',
                                                        'isInset' => false,
                                                        'styles' => '0px 1px 2px 0px rgba(16, 24, 40, 0.85)',
                                                    ],
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                    [
                        'core/column',
                        ["width" => "70%", "className" => "bio-oil-blog-individual__content"],
                        [
                            ['core/paragraph'],
                            [
                                'core/separator',
                                [
                                    "customColor" => "#e4e7ec",
                                    "className" => "is-style-wide"
                                ]
                            ],
                            ['core/heading'],
                            ['core/paragraph'],
                            [
                                'core/separator',
                                [
                                    "customColor" => "#e4e7ec",
                                    "className" => "is-style-wide"
                                ]
                            ],
                        ],
                    ],
                ]
            ]
        ];
        $post_type_object = get_post_type_object('post');
        $post_type_object->template = $template;
    }

    function redirect_to_correct_shop_url()
    {
        if (is_singular('shop')) {

            wp_redirect(get_field('url'), 301);

            exit;
        }
    }

    function shop_custom_metadata()
    {
        if (is_singular('shop')) {
            ?>

            <meta name="robots" content="noindex, nofollow" />

        <?php
        }
    }

    /**
     * @param $value
     * @param $post_type
     * @return bool|void
     */
    function sitemap_exclude_shop_post_type($value, $post_type)
    {
        if ($post_type == 'post_type_slug')
            return true;
    }
}