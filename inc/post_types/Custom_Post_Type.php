<?php
namespace TC_Aluminium\post_types;

class Custom_Post_Type{

    public $title;
    public $name;
    public $slug;
    public $config;

    function __construct($title,$name,$slug,$config){
        $this->setTitle($title);
        $this->setSlug($slug);
        $this->setConfig($config);
        $this->setName($name);
        register_post_type( $this->getName(),$this->getArgs());
    }

    /**
     * @param mixed $title
     */
    public function setTitle($title): void
    {
        $this->title = $title;
    }

    /**
     * @return mixed
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @return mixed
     */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
     * @param mixed $slug
     */
    public function setSlug($slug): void
    {
        $this->slug = $slug;
    }

    /**
     * @return array
     */
    public function getLabel(): array
    {
        return [
            'name'              => _x( $this->getTitle(), 'taxonomy general name', 'bio-oli' ),
            'singular_name'     => _x( $this->getTitle(), 'taxonomy singular name', 'bio-oli' ),
            'search_items'      => __( 'Search '.$this->getTitle(), 'bio-oli' ),
            'all_items'         => __( 'All '.$this->getTitle(), 'bio-oli' ),
            'parent_item'       => __( 'Parent '.$this->getTitle(), 'bio-oli' ),
            'parent_item_colon' => __( 'Parent '.$this->getTitle().' :', 'bio-oli' ),
            'edit_item'         => __( 'Edit '.$this->getTitle(), 'bio-oli' ),
            'update_item'       => __( 'Update '.$this->getTitle(), 'bio-oli' ),
            'add_new_item'      => __( 'Add New '.$this->getTitle(), 'bio-oli' ),
            'new_item_name'     => __( 'New '.$this->getTitle().' Name', 'bio-oli' ),
            'menu_name'         => __( $this->getTitle(), 'bio-oli' ),
        ];
    }

    /**
     * @param mixed $config
     */
    public function setConfig($config): void
    {
        $this->config = $config;
    }

    /**
     * @return mixed
     */
    public function getConfig()
    {
        return $this->config;
    }

    public function  getArgs(): array
    {
        return array_merge(
            [
                'labels'            => $this->getLabel(),
                'capability_type'   => 'post',
                'rewrite'           => [
                    'slug'          => $this->getSlug(),
                ],
            ],
            $this->getConfig()
        );
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

}