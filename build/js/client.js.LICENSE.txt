/*!*************************************!*\
  !*** ./node_modules/react/index.js ***!
  \*************************************/

/*!**************************************!*\
  !*** ./src/client.jsx + 387 modules ***!
  \**************************************/

/*!***************************************!*\
  !*** ./node_modules/lodash/lodash.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./node_modules/rehackt/index.js ***!
  \***************************************/

/*!****************************************!*\
  !*** ./node_modules/react-is/index.js ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./node_modules/react-dom/index.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./node_modules/scheduler/index.js ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./node_modules/prop-types/index.js ***!
  \******************************************/

/*!******************************************!*\
  !*** ./node_modules/react-dom/client.js ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./node_modules/react/jsx-runtime.js ***!
  \*******************************************/

/*!********************************************!*\
  !*** ./node_modules/shallowequal/index.js ***!
  \********************************************/

/*!**********************************************!*\
  !*** ./node_modules/path-to-regexp/index.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./node_modules/zen-observable/index.js ***!
  \**********************************************/

/*!*******************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/zen-observable/lib/Observable.js ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./node_modules/react/cjs/react.production.min.js ***!
  \********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/@babel/runtime/regenerator/index.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/fast-json-stable-stringify/index.js ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/cross-fetch/dist/browser-polyfill.js ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./node_modules/use-sync-external-store/shim/index.js ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/prop-types/factoryWithThrowingShims.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/prop-types/lib/ReactPropTypesSecret.js ***!
  \*************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/react-is/cjs/react-is.production.min.js ***!
  \**************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/react-dom/cjs/react-dom.production.min.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/scheduler/cjs/scheduler.production.min.js ***!
  \****************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/regeneratorRuntime.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/path-to-regexp/node_modules/isarray/index.js ***!
  \*******************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/react/cjs/react-jsx-runtime.production.min.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/use-sync-external-store/shim/with-selector.js ***!
  \********************************************************************/

/*!**********************************************************************************!*\
  !*** ./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js ***!
  \**********************************************************************************/

/*!*************************************************************************************************!*\
  !*** ./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js ***!
  \*************************************************************************************************/

/*!***************************************************************************************************************!*\
  !*** ./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js ***!
  \***************************************************************************************************************/
