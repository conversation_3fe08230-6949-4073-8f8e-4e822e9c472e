{"version": 3, "sources": ["../src/getDataFromTree.ts", "../src/renderToStringWithData.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;SAIgB,e,CACd,I,EACA,O,EAAoC;AAApC,MAAA,OAAA,KAAA,KAAA,CAAA,EAAA;AAAA,IAAA,OAAA,GAAA,EAAA;AAAoC;;AAEpC,SAAO,iBAAiB,CAAC;AACvB,IAAA,IAAI,EAAA,IADmB;AAEvB,IAAA,OAAO,EAAA,OAFgB;AAKvB,IAAA,cAAc,EAAE,OAAO,CAAC,kBAAD,CAAP,CAA4B;AALrB,GAAD,CAAxB;AAOD;;AAQD,SAAgB,iBAAhB,CAAkC,EAAlC,EAO2B;MANzB,IAAA,GAAA,EAAA,CAAA,I;MACA,EAAA,GAAA,EAAA,CAAA,O;MAAA,OAAA,GAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,E;MAIA,EAAA,GAAA,EAAA,CAAA,c;MAAA,cAAA,GAAA,EAAA,KAAA,KAAA,CAAA,GAAA,OAAA,CAAA,kBAAA,CAAA,CAAA,oBAAA,GAAA,E;AAEA,MAAM,cAAc,GAAG,IAAI,0BAAJ,EAAvB;;AAEA,WAAS,OAAT,GAAgB;AAMd,QAAM,aAAa,GAAG,oCAAtB;AACA,QAAM,IAAI,GAAG,cAAc,CACzB,eAAM,aAAN,CACE,aAAa,CAAC,QADhB,EAEE;AAAE,MAAA,KAAK,EAAA,qBAAA,qBAAA,EAAA,EAAO,OAAP,CAAA,EAAc;AAAE,QAAA,cAAc,EAAA;AAAhB,OAAd;AAAP,KAFF,EAGE,IAHF,CADyB,CAA3B;AAQA,WAAO,cAAc,CAAC,WAAf,KACH,cAAc,CAAC,uBAAf,GAAyC,IAAzC,CAA8C,OAA9C,CADG,GAEH,IAFJ;AAGD;;AAED,SAAO,OAAO,CAAC,OAAR,GAAkB,IAAlB,CAAuB,OAAvB,CAAP;AACD;;SCnDe,sB,CACd,S,EAA4B;AAE5B,SAAO,iBAAiB,CAAC;AACvB,IAAA,IAAI,EAAE,SADiB;AAEvB,IAAA,cAAc,EAAE,OAAO,CAAC,kBAAD,CAAP,CAA4B;AAFrB,GAAD,CAAxB;AAID,C", "sourcesContent": ["import React from 'react';\nimport { getApolloContext } from '@apollo/react-common';\nimport { RenderPromises } from '@apollo/react-hooks';\n\nexport function getDataFromTree(\n  tree: React.ReactNode,\n  context: { [key: string]: any } = {}\n) {\n  return getMarkupFromTree({\n    tree,\n    context,\n    // If you need to configure this renderFunction, call getMarkupFromTree\n    // directly instead of getDataFromTree.\n    renderFunction: require('react-dom/server').renderToStaticMarkup\n  });\n}\n\nexport type GetMarkupFromTreeOptions = {\n  tree: React.ReactNode;\n  context?: { [key: string]: any };\n  renderFunction?: (tree: React.ReactElement<any>) => string;\n};\n\nexport function getMarkupFromTree({\n  tree,\n  context = {},\n  // The rendering function is configurable! We use renderToStaticMarkup as\n  // the default, because it's a little less expensive than renderToString,\n  // and legacy usage of getDataFromTree ignores the return value anyway.\n  renderFunction = require('react-dom/server').renderToStaticMarkup\n}: GetMarkupFromTreeOptions): Promise<string> {\n  const renderPromises = new RenderPromises();\n\n  function process(): Promise<string> | string {\n    // Always re-render from the rootElement, even though it might seem\n    // better to render the children of the component responsible for the\n    // promise, because it is not possible to reconstruct the full context\n    // of the original rendering (including all unknown context provider\n    // elements) for a subtree of the original component tree.\n    const ApolloContext = getApolloContext();\n    const html = renderFunction(\n      React.createElement(\n        ApolloContext.Provider,\n        { value: { ...context, renderPromises } },\n        tree\n      )\n    );\n\n    return renderPromises.hasPromises()\n      ? renderPromises.consumeAndAwaitPromises().then(process)\n      : html;\n  }\n\n  return Promise.resolve().then(process);\n}\n", "import { ReactElement } from 'react';\nimport { getMarkupFromTree } from './getDataFromTree';\n\nexport function renderToStringWithData(\n  component: ReactElement<any>\n): Promise<string> {\n  return getMarkupFromTree({\n    tree: component,\n    renderFunction: require('react-dom/server').renderToString\n  });\n}\n"]}