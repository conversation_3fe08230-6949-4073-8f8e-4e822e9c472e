exports.__esModule=!0,exports.getDataFromTree=function(e,r){void 0===r&&(r={});return i({tree:e,context:r,renderFunction:require("react-dom/server").renderToStaticMarkup})},exports.getMarkupFromTree=i,exports.renderToStringWithData=function(e){return i({tree:e,renderFunction:require("react-dom/server").renderToString})};var e,r=require("tslib"),t=(e=require("react"))&&e.__esModule?e:{default:e},o=require("@apollo/react-common"),n=require("@apollo/react-hooks");function i(e){var i=e.tree,a=e.context,s=void 0===a?{}:a,u=e.renderFunction,d=void 0===u?require("react-dom/server").renderToStaticMarkup:u,c=new n.RenderPromises;return Promise.resolve().then((function e(){var n=(0,o.getApolloContext)(),a=d(t.default.createElement(n.Provider,{value:(0,r.__assign)((0,r.__assign)({},s),{renderPromises:c})},i));return c.hasPromises()?c.consumeAndAwaitPromises().then(e):a}))}
