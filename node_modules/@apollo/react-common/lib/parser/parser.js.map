{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../src/parser/parser.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,MAAM,CAAN,IAAY,YAIX;AAJD,WAAY,YAAY;IACtB,iDAAK,CAAA;IACL,uDAAQ,CAAA;IACR,+DAAY,CAAA;AACd,CAAC,EAJW,YAAY,KAAZ,YAAY,QAIvB;AAQD,IAAM,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AAExB,MAAM,UAAU,aAAa,CAAC,IAAkB;IAC9C,IAAI,IAAI,CAAC;IACT,QAAQ,IAAI,EAAE;QACZ,KAAK,YAAY,CAAC,KAAK;YACrB,IAAI,GAAG,OAAO,CAAC;YACf,MAAM;QACR,KAAK,YAAY,CAAC,QAAQ;YACxB,IAAI,GAAG,UAAU,CAAC;YAClB,MAAM;QACR,KAAK,YAAY,CAAC,YAAY;YAC5B,IAAI,GAAG,cAAc,CAAC;YACtB,MAAM;KACT;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAGD,MAAM,UAAU,MAAM,CAAC,QAAsB;IAC3C,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,MAAM;QAAE,OAAO,MAAM,CAAC;IAE1B,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;IAE1B,SAAS,CACP,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,EAC7B,iBAAe,QAAQ,+CAA4C;QACjE,oEAAoE;QACpE,2CAA2C,CAC9C,CAAC;IAEF,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC3C,UAAC,CAAiB,IAAK,OAAA,CAAC,CAAC,IAAI,KAAK,oBAAoB,EAA/B,CAA+B,CACvD,CAAC;IAEF,IAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CACzC,UAAC,CAAiB;QAChB,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO;IAA3D,CAA2D,CAC9D,CAAC;IAEF,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC3C,UAAC,CAAiB;QAChB,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,UAAU;IAA9D,CAA8D,CACjE,CAAC;IAEF,IAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC/C,UAAC,CAAiB;QAChB,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,cAAc;IAAlE,CAAkE,CACrE,CAAC;IAEF,SAAS,CACP,CAAC,SAAS,CAAC,MAAM;QACf,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,EAC9D,6DAA6D;QAC3D,4DAA4D,CAC/D,CAAC;IAEF,SAAS,CACP,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,IAAI,CAAC,EAC7D,2EAA2E;SACtE,QAAQ,aAAQ,OAAO,CAAC,MAAM,kBAAa,aAAa,CAAC,MAAM,MAAG,CAAA;SACrE,uBAAqB,SAAS,CAAC,MAAM,iBAAc,CAAA;QACnD,uEAAuE,CAC1E,CAAC;IAEF,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC;IACnE,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM;QAAE,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC;IAE3E,IAAM,WAAW,GAAG,OAAO,CAAC,MAAM;QAChC,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,SAAS,CAAC,MAAM;YAClB,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,aAAa,CAAC;IAElB,SAAS,CACP,WAAW,CAAC,MAAM,KAAK,CAAC,EACxB,wDAAsD,QAAQ,UAAO;SAChE,WAAW,CAAC,MAAM,mBAAgB,CAAA;QACrC,uEAAuE,CAC1E,CAAC;IAEF,IAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAA4B,CAAC;IAC7D,SAAS,GAAG,UAAU,CAAC,mBAAmB,IAAI,EAAE,CAAC;IAEjD,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;QACtD,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;KAC9B;SAAM;QACL,IAAI,GAAG,MAAM,CAAC;KACf;IAED,IAAM,OAAO,GAAG,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,SAAS,WAAA,EAAE,CAAC;IAC1C,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC7B,OAAO,OAAO,CAAC;AACjB,CAAC"}