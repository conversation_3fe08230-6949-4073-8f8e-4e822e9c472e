{"version": 3, "sources": ["../src/context/ApolloContext.ts", "../src/context/ApolloProvider.tsx", "../src/context/ApolloConsumer.tsx", "../src/parser/parser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAI,aAAJ;;AAEA,WAAgB,gBAAhB,GAAgC;AAC9B,QAAI,CAAC,aAAL,EAAoB;AAClB,MAAA,aAAa,GAAG,eAAM,aAAN,CAAwC,EAAxC,CAAhB;AACD;;AACD,WAAO,aAAP;AACD;;AAED,WAAgB,kBAAhB,GAAkC;AAChC,IAAA,aAAa,GAAG,eAAM,aAAN,CAAwC,EAAxC,CAAhB;AACD;;MCRY,cAAc,GAAuC,UAAC,EAAD,EAGjE;QAFC,MAAA,GAAA,EAAA,CAAA,M;QACA,QAAA,GAAA,EAAA,CAAA,Q;AAEA,QAAM,aAAa,GAAG,gBAAgB,EAAtC;AACA,WAAO,eAAA,aAAA,CAAA,aAAA,CACS,QADT,EACS,IADT,EACS,UAAA,OAAA,EAAA;AACV,UAAA,OAAA,KAAA,KAAA,CAAA,EAAA;AAAA,QAAA,OAAA,GAAA,EAAA;AAAY;;AACZ,UAAI,MAAM,IAAI,OAAO,CAAC,MAAR,KAAmB,MAAjC,EAAyC;AACvC,QAAA,OAAO,GAAG,MAAM,CAAC,MAAP,CAAc,EAAd,EAAkB,OAAlB,EAA2B;AAAE,UAAA,MAAM,EAAA;AAAR,SAA3B,CAAV;AACD;;AAED,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAAA,4BAAA,OAAA,CAAA,MAAA,EAAA,CAAA,CAAA,GAAA,4BAAA,OAAA,CAAA,MAAA,EAAA,2DAGI,qDAHJ,CAAA;AAMA,aACE,eAAA,aAAA,CAAC,aAAa,CAAC,QAAf,EAAuB;AAAC,QAAA,KAAK,EAAE;AAAR,OAAvB,EACG,QADH,CADF;AAKD,KAlBE,CAAP;AAqBD,G;;;;MC3BY,cAAc,GAAkC,UAAA,KAAA,EAAK;AAChE,QAAM,aAAa,GAAG,gBAAgB,EAAtC;AACA,WAAO,eAAA,aAAA,CAAA,aAAA,CACS,QADT,EACS,IADT,EACS,UAAA,OAAA,EAAA;AAEV,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,GAEE,4BAAA,OAAA,IAAA,OAAA,CAAA,MAAA,EAAA,CAAA,CAFF,GAEE,4BAAA,OAAA,IAAA,OAAA,CAAA,MAAA,EAAA,+DACE,iDADF,CAFF;AAKA,aAAO,KAAK,CAAC,QAAN,CAAe,OAAO,CAAC,MAAvB,CAAP;AACD,KATE,CAAP;AAYD,G;;;MChBW,Y;;;AAAZ,GAAA,UAAY,YAAZ,EAAwB;AACtB,IAAA,YAAA,CAAA,YAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA;AACA,IAAA,YAAA,CAAA,YAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAA;AACA,IAAA,YAAA,CAAA,YAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAA;AACD,GAJD,EAAY,YAAY,6BAAZ,YAAY,GAAA,EAAA,CAAxB;;AAYA,MAAM,KAAK,GAAG,IAAI,GAAJ,EAAd;;AAEA,WAAgB,aAAhB,CAA8B,IAA9B,EAAgD;AAC9C,QAAI,IAAJ;;AACA,YAAQ,IAAR;AACE,WAAK,YAAY,CAAC,KAAlB;AACE,QAAA,IAAI,GAAG,OAAP;AACA;;AACF,WAAK,YAAY,CAAC,QAAlB;AACE,QAAA,IAAI,GAAG,UAAP;AACA;;AACF,WAAK,YAAY,CAAC,YAAlB;AACE,QAAA,IAAI,GAAG,cAAP;AACA;AATJ;;AAWA,WAAO,IAAP;AACD;;AAGD,WAAgB,MAAhB,CAAuB,QAAvB,EAA6C;AAC3C,QAAM,MAAM,GAAG,KAAK,CAAC,GAAN,CAAU,QAAV,CAAf;AACA,QAAI,MAAJ,EAAY,OAAO,MAAP;AAEZ,QAAI,SAAJ,EAAe,IAAf,EAAqB,IAArB;AAEA,IAAA,OAAA,CAAA,GAAA,CACI,QADJ,KACiB,YADjB,GACiB,4BAAA,CAAA,CAAA,QAAA,IAAA,CAAA,CACQ,QAAA,CAAA,IADR,EACQ,CADR,CADjB,GAEyB,4BAAA,CAAA,CAAA,QAAA,IAAA,CAAA,CAAA,QAAA,CAAA,IAAA,EAAA,iBAAA,QAAA,GAAA,4CAAA,GACrB,oEADqB,GAErB,2CAFqB,CAFzB;AAOA,QAAM,SAAS,GAAG,QAAQ,CAAC,WAAT,CAAqB,MAArB,CAChB,UAAC,CAAD,EAAkB;AAAK,aAAA,CAAC,CAAC,IAAF,KAAW,oBAAX;AAA+B,KADtC,CAAlB;AAIA,QAAM,OAAO,GAAG,QAAQ,CAAC,WAAT,CAAqB,MAArB,CACd,UAAC,CAAD,EAAkB;AAChB,aAAA,CAAC,CAAC,IAAF,KAAW,qBAAX,IAAoC,CAAC,CAAC,SAAF,KAAgB,OAApD;AAA2D,KAF/C,CAAhB;AAKA,QAAM,SAAS,GAAG,QAAQ,CAAC,WAAT,CAAqB,MAArB,CAChB,UAAC,CAAD,EAAkB;AAChB,aAAA,CAAC,CAAC,IAAF,KAAW,qBAAX,IAAoC,CAAC,CAAC,SAAF,KAAgB,UAApD;AAA8D,KAFhD,CAAlB;AAKA,QAAM,aAAa,GAAG,QAAQ,CAAC,WAAT,CAAqB,MAArB,CACpB,UAAC,CAAD,EAAkB;AAChB,aAAA,CAAC,CAAC,IAAF,KAAW,qBAAX,IAAoC,CAAC,CAAC,SAAF,KAAgB,cAApD;AAAkE,KAFhD,CAAtB;AAKA,IAAA,OAAA,CAAA,GAAA,CACG,QADH,KACY,YADZ,GACY,4BAAA,CAAA,SAAA,CAAA,MAAA,IACP,OAAO,CAAC,MAAR,IAAkB,SAAS,CAAC,MAA5B,IAAsC,aAAa,CAAC,MAD7C,EAEV,CAFU,CADZ,GAGE,4BAAA,CAAA,SAAA,CAAA,MAAA,I,oDAC8D,MAD9D,EAEA,gEAGwD,4DALxD,CAHF;4CAUqC,4BAAA,OAAA,CAAa,MAAb,GAAa,SAAA,CAAA,MAAb,GAAa,aAAA,CAAA,MAAb,IAAa,CAAb,EAAa,CAAb,C,GAAa,4BAAA,OAAA,CAAA,MAAA,GAAA,SAAA,CAAA,MAAA,GAAA,aAAA,CAAA,MAAA,IAAA,CAAA,EAAA,+EAC9C,QAAA,GAAA,OAAA,GAAA,OAAA,CAAA,MAAA,GAAA,YAAA,GAAA,aAAA,CAAA,MAAA,GAAA,GAD8C,K,wDAAA,IAK9C,uEAL8C,C;AAMlD,IAAA,IAAI,GAAA,OAAA,CAAA,MAAA,GAAoB,YAAA,CAAA,KAApB,GAAoB,YAAA,CAAA,QAAxB;QAA0C,CAAA,OAAA,CAAA,MAAA,IAAmB,CAAC,SAAA,CAAA,M,EAExD,IAAA,GAAA,YAAA,CAAA,YAAA;QACJ,WAAA,GAAA,OAAA,CAAA,MAAA,GACE,OADF,G,mBAGE,S,GAGF,a;oRAMI,uE;AACN,QAAA,UAAA,GAAA,WAAA,CAAA,CAAA,CAAA;AAEA,IAAA,SAAA,GAAA,UAAA,CAAuB,mBAAvB,IAAuB,EAAvB;;QACE,UAAA,CAAA,IAAA,IAAA,UAA6B,CAAA,IAA7B,CAA6B,IAA7B,KAA6B,M,EAAA;;;;;;AAM/B,QAAA,OAAA,GAAA;AAAA,MAAA,IAAoB,EAAA,IAApB;AAA4B,MAAA,IAAA,EAAA,IAA5B;AAA4B,MAAA,SAAA,EAAA;AAA5B,KAAA;AACA,IAAA,KAAA,CAAA,GAAA,CAAA,QAAA,EAAA,OAAA", "sourcesContent": ["import React from 'react';\nimport ApolloClient from 'apollo-client';\n\nexport interface ApolloContextValue {\n  client?: ApolloClient<object>;\n  renderPromises?: Record<any, any>;\n}\n\nlet apolloContext: React.Context<ApolloContextValue>;\n\nexport function getApolloContext() {\n  if (!apolloContext) {\n    apolloContext = React.createContext<ApolloContextValue>({});\n  }\n  return apolloContext;\n}\n\nexport function resetApolloContext() {\n  apolloContext = React.createContext<ApolloContextValue>({});\n}\n", "import React from 'react';\nimport ApolloClient from 'apollo-client';\nimport { invariant } from 'ts-invariant';\n\nimport { getApolloContext } from './ApolloContext';\n\nexport interface ApolloProviderProps<TCache> {\n  client: ApolloClient<TCache>;\n  children: React.ReactNode | React.ReactNode[] | null;\n}\n\nexport const ApolloProvider: React.FC<ApolloProviderProps<any>> = ({\n  client,\n  children\n}) => {\n  const ApolloContext = getApolloContext();\n  return (\n    <ApolloContext.Consumer>\n      {(context = {}) => {\n        if (client && context.client !== client) {\n          context = Object.assign({}, context, { client });\n        }\n\n        invariant(\n          context.client,\n          'ApolloProvider was not passed a client instance. Make ' +\n            'sure you pass in your client via the \"client\" prop.'\n        );\n\n        return (\n          <ApolloContext.Provider value={context}>\n            {children}\n          </ApolloContext.Provider>\n        );\n      }}\n    </ApolloContext.Consumer>\n  );\n};\n", "import React from 'react';\nimport ApolloClient from 'apollo-client';\nimport { invariant } from 'ts-invariant';\n\nimport { getApolloContext } from './ApolloContext';\n\nexport interface ApolloConsumerProps {\n  children: (client: ApolloClient<object>) => React.ReactChild | null;\n}\n\nexport const ApolloConsumer: React.FC<ApolloConsumerProps> = props => {\n  const ApolloContext = getApolloContext();\n  return (\n    <ApolloContext.Consumer>\n      {(context: any) => {\n        invariant(\n          context && context.client,\n          'Could not find \"client\" in the context of ApolloConsumer. ' +\n            'Wrap the root component in an <ApolloProvider>.'\n        );\n        return props.children(context.client);\n      }}\n    </ApolloContext.Consumer>\n  );\n};\n", "import {\n  DocumentNode,\n  DefinitionNode,\n  VariableDefinitionNode,\n  OperationDefinitionNode\n} from 'graphql';\nimport { invariant } from 'ts-invariant';\n\nexport enum DocumentType {\n  Query,\n  Mutation,\n  Subscription\n}\n\nexport interface IDocumentDefinition {\n  type: DocumentType;\n  name: string;\n  variables: ReadonlyArray<VariableDefinitionNode>;\n}\n\nconst cache = new Map();\n\nexport function operationName(type: DocumentType) {\n  let name;\n  switch (type) {\n    case DocumentType.Query:\n      name = 'Query';\n      break;\n    case DocumentType.Mutation:\n      name = 'Mutation';\n      break;\n    case DocumentType.Subscription:\n      name = 'Subscription';\n      break;\n  }\n  return name;\n}\n\n// This parser is mostly used to saftey check incoming documents.\nexport function parser(document: DocumentNode): IDocumentDefinition {\n  const cached = cache.get(document);\n  if (cached) return cached;\n\n  let variables, type, name;\n\n  invariant(\n    !!document && !!document.kind,\n    `Argument of ${document} passed to parser was not a valid GraphQL ` +\n      `DocumentNode. You may need to use 'graphql-tag' or another method ` +\n      `to convert your operation into a document`\n  );\n\n  const fragments = document.definitions.filter(\n    (x: DefinitionNode) => x.kind === 'FragmentDefinition'\n  );\n\n  const queries = document.definitions.filter(\n    (x: DefinitionNode) =>\n      x.kind === 'OperationDefinition' && x.operation === 'query'\n  );\n\n  const mutations = document.definitions.filter(\n    (x: DefinitionNode) =>\n      x.kind === 'OperationDefinition' && x.operation === 'mutation'\n  );\n\n  const subscriptions = document.definitions.filter(\n    (x: DefinitionNode) =>\n      x.kind === 'OperationDefinition' && x.operation === 'subscription'\n  );\n\n  invariant(\n    !fragments.length ||\n      (queries.length || mutations.length || subscriptions.length),\n    `Passing only a fragment to 'graphql' is not yet supported. ` +\n      `You must include a query, subscription or mutation as well`\n  );\n\n  invariant(\n    queries.length + mutations.length + subscriptions.length <= 1,\n    `react-apollo only supports a query, subscription, or a mutation per HOC. ` +\n      `${document} had ${queries.length} queries, ${subscriptions.length} ` +\n      `subscriptions and ${mutations.length} mutations. ` +\n      `You can use 'compose' to join multiple operation types to a component`\n  );\n\n  type = queries.length ? DocumentType.Query : DocumentType.Mutation;\n  if (!queries.length && !mutations.length) type = DocumentType.Subscription;\n\n  const definitions = queries.length\n    ? queries\n    : mutations.length\n    ? mutations\n    : subscriptions;\n\n  invariant(\n    definitions.length === 1,\n    `react-apollo only supports one definition per HOC. ${document} had ` +\n      `${definitions.length} definitions. ` +\n      `You can use 'compose' to join multiple operation types to a component`\n  );\n\n  const definition = definitions[0] as OperationDefinitionNode;\n  variables = definition.variableDefinitions || [];\n\n  if (definition.name && definition.name.kind === 'Name') {\n    name = definition.name.value;\n  } else {\n    name = 'data'; // fallback to using data if no name\n  }\n\n  const payload = { name, type, variables };\n  cache.set(document, payload);\n  return payload;\n}\n"]}