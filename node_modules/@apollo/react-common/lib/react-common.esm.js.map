{"version": 3, "file": "react-common.esm.js", "sources": ["../src/context/ApolloContext.ts", "../src/context/ApolloProvider.tsx", "../src/context/ApolloConsumer.tsx", "../src/parser/parser.ts"], "sourcesContent": ["import React from 'react';\nimport ApolloClient from 'apollo-client';\n\nexport interface ApolloContextValue {\n  client?: ApolloClient<object>;\n  renderPromises?: Record<any, any>;\n}\n\nlet apolloContext: React.Context<ApolloContextValue>;\n\nexport function getApolloContext() {\n  if (!apolloContext) {\n    apolloContext = React.createContext<ApolloContextValue>({});\n  }\n  return apolloContext;\n}\n\nexport function resetApolloContext() {\n  apolloContext = React.createContext<ApolloContextValue>({});\n}\n", "import React from 'react';\nimport ApolloClient from 'apollo-client';\nimport { invariant } from 'ts-invariant';\n\nimport { getApolloContext } from './ApolloContext';\n\nexport interface ApolloProviderProps<TCache> {\n  client: ApolloClient<TCache>;\n  children: React.ReactNode | React.ReactNode[] | null;\n}\n\nexport const ApolloProvider: React.FC<ApolloProviderProps<any>> = ({\n  client,\n  children\n}) => {\n  const ApolloContext = getApolloContext();\n  return (\n    <ApolloContext.Consumer>\n      {(context = {}) => {\n        if (client && context.client !== client) {\n          context = Object.assign({}, context, { client });\n        }\n\n        invariant(\n          context.client,\n          'ApolloProvider was not passed a client instance. Make ' +\n            'sure you pass in your client via the \"client\" prop.'\n        );\n\n        return (\n          <ApolloContext.Provider value={context}>\n            {children}\n          </ApolloContext.Provider>\n        );\n      }}\n    </ApolloContext.Consumer>\n  );\n};\n", "import React from 'react';\nimport ApolloClient from 'apollo-client';\nimport { invariant } from 'ts-invariant';\n\nimport { getApolloContext } from './ApolloContext';\n\nexport interface ApolloConsumerProps {\n  children: (client: ApolloClient<object>) => React.ReactChild | null;\n}\n\nexport const ApolloConsumer: React.FC<ApolloConsumerProps> = props => {\n  const ApolloContext = getApolloContext();\n  return (\n    <ApolloContext.Consumer>\n      {(context: any) => {\n        invariant(\n          context && context.client,\n          'Could not find \"client\" in the context of ApolloConsumer. ' +\n            'Wrap the root component in an <ApolloProvider>.'\n        );\n        return props.children(context.client);\n      }}\n    </ApolloContext.Consumer>\n  );\n};\n", "import {\n  DocumentNode,\n  DefinitionNode,\n  VariableDefinitionNode,\n  OperationDefinitionNode\n} from 'graphql';\nimport { invariant } from 'ts-invariant';\n\nexport enum DocumentType {\n  Query,\n  Mutation,\n  Subscription\n}\n\nexport interface IDocumentDefinition {\n  type: DocumentType;\n  name: string;\n  variables: ReadonlyArray<VariableDefinitionNode>;\n}\n\nconst cache = new Map();\n\nexport function operationName(type: DocumentType) {\n  let name;\n  switch (type) {\n    case DocumentType.Query:\n      name = 'Query';\n      break;\n    case DocumentType.Mutation:\n      name = 'Mutation';\n      break;\n    case DocumentType.Subscription:\n      name = 'Subscription';\n      break;\n  }\n  return name;\n}\n\n// This parser is mostly used to saftey check incoming documents.\nexport function parser(document: DocumentNode): IDocumentDefinition {\n  const cached = cache.get(document);\n  if (cached) return cached;\n\n  let variables, type, name;\n\n  invariant(\n    !!document && !!document.kind,\n    `Argument of ${document} passed to parser was not a valid GraphQL ` +\n      `DocumentNode. You may need to use 'graphql-tag' or another method ` +\n      `to convert your operation into a document`\n  );\n\n  const fragments = document.definitions.filter(\n    (x: DefinitionNode) => x.kind === 'FragmentDefinition'\n  );\n\n  const queries = document.definitions.filter(\n    (x: DefinitionNode) =>\n      x.kind === 'OperationDefinition' && x.operation === 'query'\n  );\n\n  const mutations = document.definitions.filter(\n    (x: DefinitionNode) =>\n      x.kind === 'OperationDefinition' && x.operation === 'mutation'\n  );\n\n  const subscriptions = document.definitions.filter(\n    (x: DefinitionNode) =>\n      x.kind === 'OperationDefinition' && x.operation === 'subscription'\n  );\n\n  invariant(\n    !fragments.length ||\n      (queries.length || mutations.length || subscriptions.length),\n    `Passing only a fragment to 'graphql' is not yet supported. ` +\n      `You must include a query, subscription or mutation as well`\n  );\n\n  invariant(\n    queries.length + mutations.length + subscriptions.length <= 1,\n    `react-apollo only supports a query, subscription, or a mutation per HOC. ` +\n      `${document} had ${queries.length} queries, ${subscriptions.length} ` +\n      `subscriptions and ${mutations.length} mutations. ` +\n      `You can use 'compose' to join multiple operation types to a component`\n  );\n\n  type = queries.length ? DocumentType.Query : DocumentType.Mutation;\n  if (!queries.length && !mutations.length) type = DocumentType.Subscription;\n\n  const definitions = queries.length\n    ? queries\n    : mutations.length\n    ? mutations\n    : subscriptions;\n\n  invariant(\n    definitions.length === 1,\n    `react-apollo only supports one definition per HOC. ${document} had ` +\n      `${definitions.length} definitions. ` +\n      `You can use 'compose' to join multiple operation types to a component`\n  );\n\n  const definition = definitions[0] as OperationDefinitionNode;\n  variables = definition.variableDefinitions || [];\n\n  if (definition.name && definition.name.kind === 'Name') {\n    name = definition.name.value;\n  } else {\n    name = 'data'; // fallback to using data if no name\n  }\n\n  const payload = { name, type, variables };\n  cache.set(document, payload);\n  return payload;\n}\n"], "names": [], "mappings": ";;;AAQA,IAAI,aAAgD,CAAC;AAErD,SAAgB,gBAAgB;IAC9B,IAAI,CAAC,aAAa,EAAE;QAClB,aAAa,GAAG,KAAK,CAAC,aAAa,CAAqB,EAAE,CAAC,CAAC;KAC7D;IACD,OAAO,aAAa,CAAC;CACtB;AAED,SAAgB,kBAAkB;IAChC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAqB,EAAE,CAAC,CAAC;CAC7D;;ICRY,cAAc,GAAuC,UAAC,EAGlE;QAFC,kBAAM,EACN,sBAAQ;IAER,IAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,OAAO,kCACS;QACV,wBAAA,EAAA,YAAY;QACZ,IAAI,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;YACvC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;SAClD;QAED;YAGI,qDAAqD,CACxD,CAAC;QAEF,QACE,oBAAC,aAAa,CAAC,QAAQ,IAAC,KAAK,EAAE,OAAO,IACnC,QAAQ,CACc,EACzB;KACH,CACsB,CAC1B;CACF;;IC3BY,cAAc,GAAkC,UAAA,KAAK;IAChE,IAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,OAAO,kCACS;QAEV,qCAEE;YACE,iDAAiD,CACpD,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KACvC,CACsB,CAC1B;CACF;;IChBW,YAIX;AAJD,WAAY,YAAY;IACtB,iDAAK,CAAA;IACL,uDAAQ,CAAA;IACR,+DAAY,CAAA;CACb,EAJW,YAAY,KAAZ,YAAY,QAIvB;AAQD,IAAM,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AAExB,SAAgB,aAAa,CAAC,IAAkB;IAC9C,IAAI,IAAI,CAAC;IACT,QAAQ,IAAI;QACV,KAAK,YAAY,CAAC,KAAK;YACrB,IAAI,GAAG,OAAO,CAAC;YACf,MAAM;QACR,KAAK,YAAY,CAAC,QAAQ;YACxB,IAAI,GAAG,UAAU,CAAC;YAClB,MAAM;QACR,KAAK,YAAY,CAAC,YAAY;YAC5B,IAAI,GAAG,cAAc,CAAC;YACtB,MAAM;KACT;IACD,OAAO,IAAI,CAAC;CACb;AAGD,SAAgB,MAAM,CAAC,QAAsB;IAC3C,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,MAAM;QAAE,OAAO,MAAM,CAAC;IAE1B,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;IAE1B,WACG,CAAC,QAAQ,KAAK,yCACQ;QACrB,oEAAoE;QACpE,2CAA2C,CAC9C,CAAC;IAEF,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC3C,UAAC,CAAiB,IAAK,OAAA,CAAC,CAAC,IAAI,KAAK,oBAAoB,GAAA,CACvD,CAAC;IAEF,IAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CACzC,UAAC,CAAiB;QAChB,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO;KAAA,CAC9D,CAAC;IAEF,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC3C,UAAC,CAAiB;QAChB,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,UAAU;KAAA,CACjE,CAAC;IAEF,IAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC/C,UAAC,CAAiB;QAChB,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,cAAc;KAAA,CACrE,CAAC;IAEF,WACG,SAAS;SACP,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,EAC9D;oEAC8D,EAC9D;sEAGwD;4CAErB,kBAAa;SAC9C;;QAIA;IACJ,IAAI,oBAAoB;QAAkB,mBAAmB,CAAC;QAExD;QACJ;UACE;;cAEA;cAGF;;;QAMI;IACN;IAEA,uBAAuB,yBAAyB;QAC9C,6BAA6B;;;;;;IAM/B,oBAAoB,QAAQ;IAC5B;;;;;;"}