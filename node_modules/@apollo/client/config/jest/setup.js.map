{"version": 3, "file": "setup.js", "sourceRoot": "", "sources": ["../../../src/config/jest/setup.ts"], "names": [], "mappings": ";AAAA,OAAO,GAAG,MAAM,aAAa,CAAC;AAC9B,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,MAAM,CAAC;AAChD,MAAA,MAAM,CAAC,WAAW,oCAAlB,MAAM,CAAC,WAAW,GAAK,WAAW,EAAC;AACnC,aAAa;AACb,MAAA,MAAM,CAAC,WAAW,oCAAlB,MAAM,CAAC,WAAW,GAAK,WAAW,EAAC;AACnC,OAAO,2BAA2B,CAAC;AACnC,OAAO,EAAE,uBAAuB,EAAE,MAAM,sCAAsC,CAAC;AAC/E,OAAO,iCAAiC,CAAC;AACzC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AAEnE,gDAAgD;AAChD,GAAG,CAAC,uBAAuB,EAAE,CAAC;AAE9B,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,cAAO,CAAC,CAAC,CAAC;AAE3C,uBAAuB,EAAE,CAAC;AAE1B,SAAS,IAAI,CAAC,MAAqC;IAArC,uBAAA,EAAA,qCAAqC;IACjD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjC,CAAC;AAED,aAAa;AACb,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAEvB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IACpB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE;QACvC,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC;KACzB,CAAC,CAAC;AACL,CAAC;AACD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;IACzB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE;QAC5C,KAAK,EAAE,MAAM,CAAC,cAAc,CAAC;KAC9B,CAAC,CAAC;AACL,CAAC;AAED,aAAa;AACb,MAAM,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,EAAE,qBAAqB,CAAC,CAAC,CAAC", "sourcesContent": ["import gql from \"graphql-tag\";\nimport { TextEncoder, TextDecoder } from \"util\";\nglobal.TextEncoder ??= TextEncoder;\n// @ts-ignore\nglobal.TextDecoder ??= TextDecoder;\nimport \"@testing-library/jest-dom\";\nimport { loadErrorMessageHandler } from \"../../dev/loadErrorMessageHandler.js\";\nimport \"../../testing/matchers/index.js\";\nimport { areApolloErrorsEqual } from \"./areApolloErrorsEqual.js\";\nimport { areGraphQLErrorsEqual } from \"./areGraphQlErrorsEqual.js\";\n\n// Turn off warnings for repeated fragment names\ngql.disableFragmentWarnings();\n\nprocess.on(\"unhandledRejection\", () => {});\n\nloadErrorMessageHandler();\n\nfunction fail(reason = \"fail was called in a test.\") {\n  expect(reason).toBe(undefined);\n}\n\n// @ts-ignore\nglobalThis.fail = fail;\n\nif (!Symbol.dispose) {\n  Object.defineProperty(Symbol, \"dispose\", {\n    value: Symbol(\"dispose\"),\n  });\n}\nif (!Symbol.asyncDispose) {\n  Object.defineProperty(Symbol, \"asyncDispose\", {\n    value: Symbol(\"asyncDispose\"),\n  });\n}\n\n// @ts-ignore\nexpect.addEqualityTesters([areApolloErrorsEqual, areGraphQLErrorsEqual]);\n"]}