{"version": 3, "file": "retryLink.js", "sourceRoot": "", "sources": ["../../../src/link/retry/retryLink.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAExD,OAAO,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAiBxD;;GAEG;AACH;IAKE,4BACU,QAA2C,EAC3C,SAAoB,EACpB,OAAiB,EACjB,QAAuB,EACvB,OAAsB;QALhC,iBAQC;QAPS,aAAQ,GAAR,QAAQ,CAAmC;QAC3C,cAAS,GAAT,SAAS,CAAW;QACpB,YAAO,GAAP,OAAO,CAAU;QACjB,aAAQ,GAAR,QAAQ,CAAe;QACvB,YAAO,GAAP,OAAO,CAAe;QATxB,eAAU,GAAW,CAAC,CAAC;QACvB,wBAAmB,GAAkC,IAAI,CAAC;QAiC1D,YAAO,GAAG,UAAO,KAAU;;;;;wBACjC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;wBAGD,qBAAM,IAAI,CAAC,OAAO,CACpC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,EACd,KAAK,CACN,EAAA;;wBAJK,WAAW,GAAG,SAInB;wBACD,IAAI,WAAW,EAAE,CAAC;4BAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;4BAC1E,sBAAO;wBACT,CAAC;wBAED,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;;;aAC5B,CAAC;QAtCA,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAED;;OAEG;IACI,mCAAM,GAAb;QACE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC;QACD,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAEO,gCAAG,GAAX;QACE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;YAChE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE,IAAI,CAAC,OAAO;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;SACrD,CAAC,CAAC;IACL,CAAC;IAmBO,0CAAa,GAArB,UAAsB,KAAa;QAAnC,iBASC;QARC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;YACxB,KAAI,CAAC,OAAO,GAAG,SAAS,CAAC;YACzB,KAAI,CAAC,GAAG,EAAE,CAAC;QACb,CAAC,EAAE,KAAK,CAAkB,CAAC;IAC7B,CAAC;IACH,yBAAC;AAAD,CAAC,AA9DD,IA8DC;AAED;IAA+B,6BAAU;IAIvC,mBAAY,OAA2B;QACrC,YAAA,MAAK,WAAE,SAAC;QACF,IAAA,KAAsB,OAAO,IAAK,EAAwB,EAAxD,QAAQ,cAAA,EAAE,KAAK,WAAyC,CAAC;QACjE,KAAI,CAAC,QAAQ;YACX,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAClE,KAAI,CAAC,OAAO;YACV,OAAO,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;;IAC7E,CAAC;IAEM,2BAAO,GAAd,UACE,SAAoB,EACpB,QAAkB;QAFpB,iBAgBC;QAZC,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,IAAM,SAAS,GAAG,IAAI,kBAAkB,CACtC,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,KAAI,CAAC,QAAQ,EACb,KAAI,CAAC,OAAO,CACb,CAAC;YACF,OAAO;gBACL,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IACH,gBAAC;AAAD,CAAC,AA9BD,CAA+B,UAAU,GA8BxC", "sourcesContent": ["import type { Operation, FetchResult, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type { ObservableSubscription } from \"../../utilities/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type { DelayFunction, DelayFunctionOptions } from \"./delayFunction.js\";\nimport { buildDelayFunction } from \"./delayFunction.js\";\nimport type { RetryFunction, RetryFunctionOptions } from \"./retryFunction.js\";\nimport { buildRetryFunction } from \"./retryFunction.js\";\nimport type { SubscriptionObserver } from \"zen-observable-ts\";\n\nexport namespace RetryLink {\n  export interface Options {\n    /**\n     * Configuration for the delay strategy to use, or a custom delay strategy.\n     */\n    delay?: DelayFunctionOptions | DelayFunction;\n\n    /**\n     * Configuration for the retry strategy to use, or a custom retry strategy.\n     */\n    attempts?: RetryFunctionOptions | RetryFunction;\n  }\n}\n\n/**\n * Tracking and management of operations that may be (or currently are) retried.\n */\nclass RetryableOperation {\n  private retryCount: number = 0;\n  private currentSubscription: ObservableSubscription | null = null;\n  private timerId: number | undefined;\n\n  constructor(\n    private observer: SubscriptionObserver<FetchResult>,\n    private operation: Operation,\n    private forward: NextLink,\n    private delayFor: DelayFunction,\n    private retryIf: RetryFunction\n  ) {\n    this.try();\n  }\n\n  /**\n   * Stop retrying for the operation, and cancel any in-progress requests.\n   */\n  public cancel() {\n    if (this.currentSubscription) {\n      this.currentSubscription.unsubscribe();\n    }\n    clearTimeout(this.timerId);\n    this.timerId = undefined;\n    this.currentSubscription = null;\n  }\n\n  private try() {\n    this.currentSubscription = this.forward(this.operation).subscribe({\n      next: this.observer.next.bind(this.observer),\n      error: this.onError,\n      complete: this.observer.complete.bind(this.observer),\n    });\n  }\n\n  private onError = async (error: any) => {\n    this.retryCount += 1;\n\n    // Should we retry?\n    const shouldRetry = await this.retryIf(\n      this.retryCount,\n      this.operation,\n      error\n    );\n    if (shouldRetry) {\n      this.scheduleRetry(this.delayFor(this.retryCount, this.operation, error));\n      return;\n    }\n\n    this.observer.error(error);\n  };\n\n  private scheduleRetry(delay: number) {\n    if (this.timerId) {\n      throw new Error(`RetryLink BUG! Encountered overlapping retries`);\n    }\n\n    this.timerId = setTimeout(() => {\n      this.timerId = undefined;\n      this.try();\n    }, delay) as any as number;\n  }\n}\n\nexport class RetryLink extends ApolloLink {\n  private delayFor: DelayFunction;\n  private retryIf: RetryFunction;\n\n  constructor(options?: RetryLink.Options) {\n    super();\n    const { attempts, delay } = options || ({} as RetryLink.Options);\n    this.delayFor =\n      typeof delay === \"function\" ? delay : buildDelayFunction(delay);\n    this.retryIf =\n      typeof attempts === \"function\" ? attempts : buildRetryFunction(attempts);\n  }\n\n  public request(\n    operation: Operation,\n    nextLink: NextLink\n  ): Observable<FetchResult> {\n    return new Observable((observer) => {\n      const retryable = new RetryableOperation(\n        observer,\n        operation,\n        nextLink,\n        this.delayFor,\n        this.retryIf\n      );\n      return () => {\n        retryable.cancel();\n      };\n    });\n  }\n}\n"]}