{"version": 3, "file": "useQueryRefHandlers.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useQueryRefHandlers.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,EACL,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,EACrB,YAAY,GACb,MAAM,sBAAsB,CAAC;AAS9B,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAc/C;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,mBAAmB,CAIjC,QAAqC;IAErC,IAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAE3C,OAAO,QAAQ,CACb,qBAAqB,EACrB,oBAAoB,EACpB,SAAS,CAAC,CAAC;QACT,SAAS,CAAC,YAAY,CAAC;QACvB,qEAAqE;QACrE,0EAA0E;QAC1E,4EAA4E;QAC5E,yBAAyB;QACzB,oEAAoE;QACpE,mEAAmE;QACnE,yDAAyD;QACzD,sDAAsD;QACxD,CAAC,CAAC,eAAe,EAAE,CACpB,CAAC,QAAQ,CAAC,CAAC;AACd,CAAC;AAED,SAAS,oBAAoB,CAI3B,QAAqC;IAErC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAC1B,IAAA,KAA0C,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAjE,gBAAgB,QAAA,EAAE,mBAAmB,QAA4B,CAAC;IACnE,IAAA,KAAwC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAA/D,eAAe,QAAA,EAAE,kBAAkB,QAA4B,CAAC;IACvE,IAAM,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAElD,4EAA4E;IAC5E,2EAA2E;IAC3E,gEAAgE;IAChE,IAAI,gBAAgB,KAAK,QAAQ,EAAE,CAAC;QAClC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC9B,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,qBAAqB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,IAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,UAAC,SAAS;QACR,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEpD,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAEnD,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF,IAAM,SAAS,GAAyC,KAAK,CAAC,WAAW,CACvE,UAAC,OAAO;QACN,IAAM,OAAO,GAAG,gBAAgB,CAAC,SAAS,CACxC,OAA0C,CAC3C,CAAC;QAEF,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAEnD,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF,OAAO;QACL,OAAO,SAAA;QACP,SAAS,WAAA;QACT,eAAe,EAAE,gBAAgB,CAAC,UAAU,CAAC,eAAe;KAC7D,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport {\n  assertWrappedQueryRef,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"../internal/index.js\";\nimport type { QueryRef } from \"../internal/index.js\";\nimport type { OperationVariables } from \"../../core/types.js\";\nimport type {\n  RefetchFunction,\n  FetchMoreFunction,\n  SubscribeToMoreFunction,\n} from \"./useSuspenseQuery.js\";\nimport type { FetchMoreQueryOptions } from \"../../core/watchQueryOptions.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { wrapHook } from \"./internal/index.js\";\n\nexport interface UseQueryRefHandlersResult<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n> {\n  /** {@inheritDoc @apollo/client!ObservableQuery#refetch:member(1)} */\n  refetch: RefetchFunction<TData, TVariables>;\n  /** {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)} */\n  fetchMore: FetchMoreFunction<TData, TVariables>;\n  /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n  subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n}\n\n/**\n * A React hook that returns a `refetch` and `fetchMore` function for a given\n * `queryRef`.\n *\n * This is useful to get access to handlers for a `queryRef` that was created by\n * `createQueryPreloader` or when the handlers for a `queryRef` produced in\n * a different component are inaccessible.\n *\n * @example\n * ```tsx\n * const MyComponent({ queryRef }) {\n *   const { refetch, fetchMore } = useQueryRefHandlers(queryRef);\n *\n *   // ...\n * }\n * ```\n * @since 3.9.0\n * @param queryRef - A `QueryRef` returned from `useBackgroundQuery`, `useLoadableQuery`, or `createQueryPreloader`.\n */\nexport function useQueryRefHandlers<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  queryRef: QueryRef<TData, TVariables>\n): UseQueryRefHandlersResult<TData, TVariables> {\n  const unwrapped = unwrapQueryRef(queryRef);\n\n  return wrapHook(\n    \"useQueryRefHandlers\",\n    useQueryRefHandlers_,\n    unwrapped ?\n      unwrapped[\"observable\"]\n      // in the case of a \"transported\" queryRef object, we need to use the\n      // client that's available to us at the current position in the React tree\n      // that ApolloClient will then have the job to recreate a real queryRef from\n      // the transported object\n      // This is just a context read - it's fine to do this conditionally.\n      // This hook wrapper also shouldn't be optimized by React Compiler.\n      // eslint-disable-next-line react-compiler/react-compiler\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n    : useApolloClient()\n  )(queryRef);\n}\n\nfunction useQueryRefHandlers_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  queryRef: QueryRef<TData, TVariables>\n): UseQueryRefHandlersResult<TData, TVariables> {\n  assertWrappedQueryRef(queryRef);\n  const [previousQueryRef, setPreviousQueryRef] = React.useState(queryRef);\n  const [wrappedQueryRef, setWrappedQueryRef] = React.useState(queryRef);\n  const internalQueryRef = unwrapQueryRef(queryRef);\n\n  // To ensure we can support React transitions, this hook needs to manage the\n  // queryRef state and apply React's state value immediately to the existing\n  // queryRef since this hook doesn't return the queryRef directly\n  if (previousQueryRef !== queryRef) {\n    setPreviousQueryRef(queryRef);\n    setWrappedQueryRef(queryRef);\n  } else {\n    updateWrappedQueryRef(queryRef, getWrappedPromise(wrappedQueryRef));\n  }\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = internalQueryRef.refetch(variables);\n\n      setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      const promise = internalQueryRef.fetchMore(\n        options as FetchMoreQueryOptions<any, any>\n      );\n\n      setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  return {\n    refetch,\n    fetchMore,\n    subscribeToMore: internalQueryRef.observable.subscribeToMore,\n  };\n}\n"]}