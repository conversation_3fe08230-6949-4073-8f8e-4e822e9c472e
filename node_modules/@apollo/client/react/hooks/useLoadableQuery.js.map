{"version": 3, "file": "useLoadableQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useLoadableQuery.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAQjC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EACL,qBAAqB,EACrB,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACrB,YAAY,GACb,MAAM,sBAAsB,CAAC;AAG9B,OAAO,EAAS,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAM7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAK1D,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAuI7D,MAAM,UAAU,gBAAgB,CAI9B,KAA0D,EAC1D,OAAuD;IAAvD,wBAAA,EAAA,UAAoC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEvD,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAM,iBAAiB,GAAG,oBAAoB,CAAC,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;IACnE,IAAA,KAAkB,OAAO,SAAZ,EAAb,QAAQ,mBAAG,EAAE,KAAA,CAAa;IAE5B,IAAA,KAA0B,KAAK,CAAC,QAAQ,CAGpC,IAAI,CAAC,EAHR,QAAQ,QAAA,EAAE,WAAW,QAGb,CAAC;IAEhB,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAEhC,IAAM,gBAAgB,GAAG,QAAQ,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC;IAE9D,IAAI,QAAQ,KAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,gBAAgB,CAAC,iBAAiB,CAAC,CAAA,EAAE,CAAC;QACtE,IAAM,OAAO,GAAG,gBAAgB,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACjE,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,IAAM,kBAAkB,GAAG,cAAc,EAAE,CAAC;IAE5C,IAAM,SAAS,GAAyC,KAAK,CAAC,WAAW,CACvE,UAAC,OAAO;QACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,CACxD,CAAC;QACJ,CAAC;QAED,IAAM,OAAO,GAAG,gBAAgB,CAAC,SAAS,CACxC,OAAmD,CACpD,CAAC;QAEF,WAAW,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAE5C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF,IAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,UAAC,OAAO;QACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,CACxD,CAAC;QACJ,CAAC;QAED,IAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAElD,WAAW,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAE5C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF,IAAM,SAAS,GAAkC,KAAK,CAAC,WAAW,CAChE;QAAC,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACN,SAAS,CACP,CAAC,kBAAkB,EAAE,EACrB,sIAAsI,CACvI,CAAC;QAEK,IAAA,SAAS,GAAI,IAAI,GAAR,CAAS;QAEzB,IAAM,QAAQ;YACZ,KAAK;YACL,kBAAkB,CAAC,SAAS,CAAC;WACzB,EAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,OAClC,CAAC;QAEF,IAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE;YACnD,OAAA,MAAM,CAAC,UAAU,CAAC,sBACb,iBAAiB,KACpB,SAAS,WAAA,GACqB,CAAC;QAHjC,CAGiC,CAClC,CAAC;QAEF,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtC,CAAC,EACD;QACE,KAAK;QACL,QAAQ;QACR,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,MAAM;KACP,CACF,CAAC;IAEF,IAAM,eAAe,GACnB,KAAK,CAAC,WAAW,CACf,UAAC,OAAO;QACN,SAAS,CACP,gBAAgB,EAChB,uDAAuD,CACxD,CAAC;QAEF,OAAO,gBAAgB,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEJ,IAAM,KAAK,GAAkB,KAAK,CAAC,WAAW,CAAC;QAC7C,WAAW,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,SAAS,WAAA,EAAE,OAAO,SAAA,EAAE,KAAK,OAAA,EAAE,eAAe,iBAAA,EAAE,CAAC,CAAC;AAC/E,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type {\n  DocumentNode,\n  FetchMoreQueryOptions,\n  OperationVariables,\n  TypedDocumentNode,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport {\n  assertWrappedQueryRef,\n  getSuspenseCache,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"../internal/index.js\";\nimport type { <PERSON><PERSON><PERSON>ey, QueryRef } from \"../internal/index.js\";\nimport type { LoadableQueryHookOptions } from \"../types/types.js\";\nimport { __use, useRenderGuard } from \"./internal/index.js\";\nimport { useWatchQueryOptions } from \"./useSuspenseQuery.js\";\nimport type {\n  FetchMoreFunction,\n  RefetchFunction,\n  SubscribeToMoreFunction,\n} from \"./useSuspenseQuery.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport type {\n  DeepPartial,\n  OnlyRequiredProperties,\n} from \"../../utilities/index.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n\nexport type LoadQueryFunction<TVariables extends OperationVariables> = (\n  // Use variadic args to handle cases where TVariables is type `never`, in\n  // which case we don't want to allow a variables argument. In other\n  // words, we don't want to allow variables to be passed as an argument to this\n  // function if the query does not expect variables in the document.\n  ...args: [TVariables] extends [never] ? []\n  : {} extends OnlyRequiredProperties<TVariables> ? [variables?: TVariables]\n  : [variables: TVariables]\n) => void;\n\ntype ResetFunction = () => void;\n\nexport type UseLoadableQueryResult<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n> = [\n  loadQuery: LoadQueryFunction<TVariables>,\n  queryRef: QueryRef<TData, TVariables> | null,\n  handlers: {\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member} */\n    fetchMore: FetchMoreFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member} */\n    refetch: RefetchFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n    subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n    /**\n     * A function that resets the `queryRef` back to `null`.\n     */\n    reset: ResetFunction;\n  },\n];\n\nexport function useLoadableQuery<\n  TData,\n  TVariables extends OperationVariables,\n  TOptions extends LoadableQueryHookOptions,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: LoadableQueryHookOptions & TOptions\n): UseLoadableQueryResult<\n  TOptions[\"errorPolicy\"] extends \"ignore\" | \"all\" ?\n    TOptions[\"returnPartialData\"] extends true ?\n      DeepPartial<TData> | undefined\n    : TData | undefined\n  : TOptions[\"returnPartialData\"] extends true ? DeepPartial<TData>\n  : TData,\n  TVariables\n>;\n\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: LoadableQueryHookOptions & {\n    returnPartialData: true;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): UseLoadableQueryResult<DeepPartial<TData> | undefined, TVariables>;\n\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: LoadableQueryHookOptions & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): UseLoadableQueryResult<TData | undefined, TVariables>;\n\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: LoadableQueryHookOptions & {\n    returnPartialData: true;\n  }\n): UseLoadableQueryResult<DeepPartial<TData>, TVariables>;\n\n/**\n * A hook for imperatively loading a query, such as responding to a user\n * interaction.\n *\n * > Refer to the [Suspense - Fetching in response to user interaction](https://www.apollographql.com/docs/react/data/suspense#fetching-in-response-to-user-interaction) section for a more in-depth overview of `useLoadableQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useLoadableQuery } from \"@apollo/client\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function App() {\n *   const [loadGreeting, queryRef] = useLoadableQuery(GET_GREETING);\n *\n *   return (\n *     <>\n *       <button onClick={() => loadGreeting({ language: \"english\" })}>\n *         Load greeting\n *       </button>\n *       <Suspense fallback={<div>Loading...</div>}>\n *         {queryRef && <Hello queryRef={queryRef} />}\n *       </Suspense>\n *     </>\n *   );\n * }\n *\n * function Hello({ queryRef }) {\n *   const { data } = useReadQuery(queryRef);\n *\n *   return <div>{data.greeting.message}</div>;\n * }\n * ```\n *\n * @since 3.9.0\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns A tuple in the form of `[loadQuery, queryRef, handlers]`\n */\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: LoadableQueryHookOptions\n): UseLoadableQueryResult<TData, TVariables>;\n\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: LoadableQueryHookOptions = Object.create(null)\n): UseLoadableQueryResult<TData, TVariables> {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions({ client, query, options });\n  const { queryKey = [] } = options;\n\n  const [queryRef, setQueryRef] = React.useState<QueryRef<\n    TData,\n    TVariables\n  > | null>(null);\n\n  assertWrappedQueryRef(queryRef);\n\n  const internalQueryRef = queryRef && unwrapQueryRef(queryRef);\n\n  if (queryRef && internalQueryRef?.didChangeOptions(watchQueryOptions)) {\n    const promise = internalQueryRef.applyOptions(watchQueryOptions);\n    updateWrappedQueryRef(queryRef, promise);\n  }\n\n  const calledDuringRender = useRenderGuard();\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      if (!internalQueryRef) {\n        throw new Error(\n          \"The query has not been loaded. Please load the query.\"\n        );\n      }\n\n      const promise = internalQueryRef.fetchMore(\n        options as FetchMoreQueryOptions<TVariables, TData>\n      );\n\n      setQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      if (!internalQueryRef) {\n        throw new Error(\n          \"The query has not been loaded. Please load the query.\"\n        );\n      }\n\n      const promise = internalQueryRef.refetch(options);\n\n      setQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const loadQuery: LoadQueryFunction<TVariables> = React.useCallback(\n    (...args) => {\n      invariant(\n        !calledDuringRender(),\n        \"useLoadableQuery: 'loadQuery' should not be called during render. To start a query during render, use the 'useBackgroundQuery' hook.\"\n      );\n\n      const [variables] = args;\n\n      const cacheKey: CacheKey = [\n        query,\n        canonicalStringify(variables),\n        ...([] as any[]).concat(queryKey),\n      ];\n\n      const queryRef = suspenseCache.getQueryRef(cacheKey, () =>\n        client.watchQuery({\n          ...watchQueryOptions,\n          variables,\n        } as WatchQueryOptions<any, any>)\n      );\n\n      setQueryRef(wrapQueryRef(queryRef));\n    },\n    [\n      query,\n      queryKey,\n      suspenseCache,\n      watchQueryOptions,\n      calledDuringRender,\n      client,\n    ]\n  );\n\n  const subscribeToMore: SubscribeToMoreFunction<TData, TVariables> =\n    React.useCallback(\n      (options) => {\n        invariant(\n          internalQueryRef,\n          \"The query has not been loaded. Please load the query.\"\n        );\n\n        return internalQueryRef.observable.subscribeToMore(options);\n      },\n      [internalQueryRef]\n    );\n\n  const reset: ResetFunction = React.useCallback(() => {\n    setQueryRef(null);\n  }, []);\n\n  return [loadQuery, queryRef, { fetchMore, refetch, reset, subscribeToMore }];\n}\n"]}