{"version": 3, "file": "useSuspenseQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useSuspenseQuery.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAW7D,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEjE,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAMtE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAgJ3C,MAAM,UAAU,gBAAgB,CAI9B,KAA0D,EAC1D,OAEqE;IAFrE,wBAAA,EAAA,UAEkD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAErE,OAAO,QAAQ,CACb,kBAAkB;IAClB,yDAAyD;IACzD,iBAAiB,EACjB,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,iBAAiB,CAIxB,KAA0D,EAC1D,OAE+C;IAE/C,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAM,iBAAiB,GAAG,oBAAoB,CAAW;QACvD,MAAM,QAAA;QACN,KAAK,OAAA;QACL,OAAO,SAAA;KACR,CAAC,CAAC;IACK,IAAA,WAAW,GAAgB,iBAAiB,YAAjC,EAAE,SAAS,GAAK,iBAAiB,UAAtB,CAAuB;IAC7C,IAAA,KAAkB,OAAO,SAAZ,EAAb,QAAQ,mBAAG,EAAE,KAAA,CAAa;IAElC,IAAM,QAAQ;QACZ,KAAK;QACL,kBAAkB,CAAC,SAAS,CAAC;OACzB,EAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,OAClC,CAAC;IAEF,IAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE;QACnD,OAAA,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAApC,CAAoC,CACrC,CAAC;IAEE,IAAA,KAAwB,KAAK,CAAC,QAAQ,CAExC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,EAF9B,OAAO,QAAA,EAAE,UAAU,QAEW,CAAC;IAEpC,+EAA+E;IAC/E,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE,CAAC;QAChC,yDAAyD;QACzD,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;QAC1B,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;IAChC,CAAC;IACD,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACjD,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC;QACd,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAElC,IAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,OAAO;YAC7C,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,cAAc,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,IAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC;QAC/B,IAAM,KAAK,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE7C,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC1B,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK;YAChE,KAAK,OAAA;SACN,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEtB,IAAM,MAAM,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAEvE,IAAM,SAAS,GAAG,KAAK,CAAC,WAAW,CAGjC,UAAC,OAAO;QACN,IAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5C,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAE7C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACyC,CAAC;IAEtD,IAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,UAAC,SAAS;QACR,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5C,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAE7C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,IAAM,eAAe,GAAG,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC;IAE5D,OAAO,KAAK,CAAC,OAAO,CAElB;QACA,OAAO;YACL,MAAM,QAAA;YACN,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC;YAC5B,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,SAAS,WAAA;YACT,OAAO,SAAA;YACP,eAAe,iBAAA;SAChB,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,eAAe,CAAC,OAA0B;IACzC,IAAA,KAAK,GAAqC,OAAO,MAA5C,EAAE,WAAW,GAAwB,OAAO,YAA/B,EAAE,iBAAiB,GAAK,OAAO,kBAAZ,CAAa;IAE1D,kBAAkB,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;IAC9C,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACjC,yBAAyB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,mBAAmB,CAC1B,WAAkD;IAAlD,4BAAA,EAAA,2BAAkD;IAElD,IAAM,sBAAsB,GAA4B;QACtD,aAAa;QACb,cAAc;QACd,UAAU;QACV,mBAAmB;KACpB,CAAC;IAEF,SAAS,CACP,sBAAsB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAC5C,uDAAyD,EACzD,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAChC,WAA8C,EAC9C,iBAAsC;IAEtC,IAAI,WAAW,KAAK,UAAU,IAAI,iBAAiB,EAAE,CAAC;QACpD,SAAS,CAAC,IAAI,CACZ,wJAAwJ,CACzJ,CAAC;IACJ,CAAC;AACH,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,MAA8B;IAC1D,OAAO,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACnC,IAAI,WAAW,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;QACnD,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AACnB,CAAC;AAWD,MAAM,UAAU,oBAAoB,CAGlC,EAImD;QAHnD,MAAM,YAAA,EACN,KAAK,WAAA,EACL,OAAO,aAAA;IAKP,OAAO,WAAW,CAAuC;;QACvD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,EAAE,KAAK,OAAA,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;QAC3C,CAAC;QAED,IAAM,WAAW,GACf,OAAO,CAAC,WAAW;aACnB,MAAA,MAAM,CAAC,cAAc,CAAC,UAAU,0CAAE,WAAW,CAAA;YAC7C,aAAa,CAAC;QAEhB,IAAM,iBAAiB,yBAClB,OAAO,KACV,WAAW,aAAA,EACX,KAAK,OAAA,EACL,2BAA2B,EAAE,KAAK,EAClC,eAAe,EAAE,KAAK,CAAC,GACxB,CAAC;QAEF,IAAI,OAAO,EAAE,CAAC;YACZ,eAAe,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,0EAA0E;QAC1E,qEAAqE;QACrE,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;QAC5C,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport type {\n  ApolloClient,\n  ApolloQueryResult,\n  DocumentNode,\n  OperationVariables,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n  FetchMoreQueryOptions,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport { ApolloError, NetworkStatus } from \"../../core/index.js\";\nimport type { DeepPartial } from \"../../utilities/index.js\";\nimport { isNonEmptyArray } from \"../../utilities/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport type {\n  SuspenseQueryHookOptions,\n  ObservableQueryFields,\n  NoInfer,\n} from \"../types/types.js\";\nimport { __use, useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport { getSuspenseCache } from \"../internal/index.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport { skipToken } from \"./constants.js\";\nimport type { SkipToken } from \"./constants.js\";\nimport type { CacheKey, QueryKey } from \"../internal/index.js\";\nimport type { MaybeMasked, Unmasked } from \"../../masking/index.js\";\n\nexport interface UseSuspenseQueryResult<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n> {\n  client: ApolloClient<any>;\n  data: MaybeMasked<TData>;\n  error: ApolloError | undefined;\n  fetchMore: FetchMoreFunction<TData, TVariables>;\n  networkStatus: NetworkStatus;\n  refetch: RefetchFunction<TData, TVariables>;\n  subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n}\n\nexport type FetchMoreFunction<TData, TVariables extends OperationVariables> = (\n  fetchMoreOptions: FetchMoreQueryOptions<TVariables, TData> & {\n    updateQuery?: (\n      previousQueryResult: Unmasked<TData>,\n      options: {\n        fetchMoreResult: Unmasked<TData>;\n        variables: TVariables;\n      }\n    ) => Unmasked<TData>;\n  }\n) => Promise<ApolloQueryResult<MaybeMasked<TData>>>;\n\nexport type RefetchFunction<\n  TData,\n  TVariables extends OperationVariables,\n> = ObservableQueryFields<TData, TVariables>[\"refetch\"];\n\nexport type SubscribeToMoreFunction<\n  TData,\n  TVariables extends OperationVariables,\n> = ObservableQueryFields<TData, TVariables>[\"subscribeToMore\"];\n\nexport function useSuspenseQuery<\n  TData,\n  TVariables extends OperationVariables,\n  TOptions extends Omit<SuspenseQueryHookOptions<TData>, \"variables\">,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> &\n    TOptions\n): UseSuspenseQueryResult<\n  TOptions[\"errorPolicy\"] extends \"ignore\" | \"all\" ?\n    TOptions[\"returnPartialData\"] extends true ?\n      DeepPartial<TData> | undefined\n    : TData | undefined\n  : TOptions[\"returnPartialData\"] extends true ?\n    TOptions[\"skip\"] extends boolean ?\n      DeepPartial<TData> | undefined\n    : DeepPartial<TData>\n  : TOptions[\"skip\"] extends boolean ? TData | undefined\n  : TData,\n  TVariables\n>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: true;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): UseSuspenseQueryResult<DeepPartial<TData> | undefined, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): UseSuspenseQueryResult<TData | undefined, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: true;\n  }\n): UseSuspenseQueryResult<DeepPartial<TData> | undefined, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: true;\n  }\n): UseSuspenseQueryResult<DeepPartial<TData>, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> & {\n    skip: boolean;\n  }\n): UseSuspenseQueryResult<TData | undefined, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n): UseSuspenseQueryResult<TData, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> & {\n        returnPartialData: true;\n      })\n): UseSuspenseQueryResult<DeepPartial<TData> | undefined, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?:\n    | SkipToken\n    | SuspenseQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n): UseSuspenseQueryResult<TData | undefined, TVariables>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken & Partial<SuspenseQueryHookOptions<TData, TVariables>>)\n    | SuspenseQueryHookOptions<TData, TVariables> = Object.create(null)\n): UseSuspenseQueryResult<TData | undefined, TVariables> {\n  return wrapHook(\n    \"useSuspenseQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseQuery_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(query, options);\n}\n\nfunction useSuspenseQuery_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken & Partial<SuspenseQueryHookOptions<TData, TVariables>>)\n    | SuspenseQueryHookOptions<TData, TVariables>\n): UseSuspenseQueryResult<TData | undefined, TVariables> {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions<any, any>({\n    client,\n    query,\n    options,\n  });\n  const { fetchPolicy, variables } = watchQueryOptions;\n  const { queryKey = [] } = options;\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n\n  const queryRef = suspenseCache.getQueryRef(cacheKey, () =>\n    client.watchQuery(watchQueryOptions)\n  );\n\n  let [current, setPromise] = React.useState<\n    [QueryKey, Promise<ApolloQueryResult<any>>]\n  >([queryRef.key, queryRef.promise]);\n\n  // This saves us a re-execution of the render function when a variable changed.\n  if (current[0] !== queryRef.key) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    current[0] = queryRef.key;\n    current[1] = queryRef.promise;\n  }\n  let promise = current[1];\n\n  if (queryRef.didChangeOptions(watchQueryOptions)) {\n    current[1] = promise = queryRef.applyOptions(watchQueryOptions);\n  }\n\n  React.useEffect(() => {\n    const dispose = queryRef.retain();\n\n    const removeListener = queryRef.listen((promise) => {\n      setPromise([queryRef.key, promise]);\n    });\n\n    return () => {\n      removeListener();\n      dispose();\n    };\n  }, [queryRef]);\n\n  const skipResult = React.useMemo(() => {\n    const error = toApolloError(queryRef.result);\n\n    return {\n      loading: false,\n      data: queryRef.result.data,\n      networkStatus: error ? NetworkStatus.error : NetworkStatus.ready,\n      error,\n    };\n  }, [queryRef.result]);\n\n  const result = fetchPolicy === \"standby\" ? skipResult : __use(promise);\n\n  const fetchMore = React.useCallback<\n    FetchMoreFunction<unknown, OperationVariables>\n  >(\n    (options) => {\n      const promise = queryRef.fetchMore(options);\n      setPromise([queryRef.key, queryRef.promise]);\n\n      return promise;\n    },\n    [queryRef]\n  ) as FetchMoreFunction<TData | undefined, TVariables>;\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = queryRef.refetch(variables);\n      setPromise([queryRef.key, queryRef.promise]);\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  const subscribeToMore = queryRef.observable.subscribeToMore;\n\n  return React.useMemo<\n    UseSuspenseQueryResult<TData | undefined, TVariables>\n  >(() => {\n    return {\n      client,\n      data: result.data,\n      error: toApolloError(result),\n      networkStatus: result.networkStatus,\n      fetchMore,\n      refetch,\n      subscribeToMore,\n    };\n  }, [client, fetchMore, refetch, result, subscribeToMore]);\n}\n\nfunction validateOptions(options: WatchQueryOptions) {\n  const { query, fetchPolicy, returnPartialData } = options;\n\n  verifyDocumentType(query, DocumentType.Query);\n  validateFetchPolicy(fetchPolicy);\n  validatePartialDataReturn(fetchPolicy, returnPartialData);\n}\n\nfunction validateFetchPolicy(\n  fetchPolicy: WatchQueryFetchPolicy = \"cache-first\"\n) {\n  const supportedFetchPolicies: WatchQueryFetchPolicy[] = [\n    \"cache-first\",\n    \"network-only\",\n    \"no-cache\",\n    \"cache-and-network\",\n  ];\n\n  invariant(\n    supportedFetchPolicies.includes(fetchPolicy),\n    `The fetch policy \\`%s\\` is not supported with suspense.`,\n    fetchPolicy\n  );\n}\n\nfunction validatePartialDataReturn(\n  fetchPolicy: WatchQueryFetchPolicy | undefined,\n  returnPartialData: boolean | undefined\n) {\n  if (fetchPolicy === \"no-cache\" && returnPartialData) {\n    invariant.warn(\n      \"Using `returnPartialData` with a `no-cache` fetch policy has no effect. To read partial data from the cache, consider using an alternate fetch policy.\"\n    );\n  }\n}\n\nexport function toApolloError(result: ApolloQueryResult<any>) {\n  return isNonEmptyArray(result.errors) ?\n      new ApolloError({ graphQLErrors: result.errors })\n    : result.error;\n}\n\ninterface UseWatchQueryOptionsHookOptions<\n  TData,\n  TVariables extends OperationVariables,\n> {\n  client: ApolloClient<unknown>;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  options: SkipToken | SuspenseQueryHookOptions<TData, TVariables>;\n}\n\nexport function useWatchQueryOptions<\n  TData,\n  TVariables extends OperationVariables,\n>({\n  client,\n  query,\n  options,\n}: UseWatchQueryOptionsHookOptions<TData, TVariables>): WatchQueryOptions<\n  TVariables,\n  TData\n> {\n  return useDeepMemo<WatchQueryOptions<TVariables, TData>>(() => {\n    if (options === skipToken) {\n      return { query, fetchPolicy: \"standby\" };\n    }\n\n    const fetchPolicy =\n      options.fetchPolicy ||\n      client.defaultOptions.watchQuery?.fetchPolicy ||\n      \"cache-first\";\n\n    const watchQueryOptions = {\n      ...options,\n      fetchPolicy,\n      query,\n      notifyOnNetworkStatusChange: false,\n      nextFetchPolicy: void 0,\n    };\n\n    if (__DEV__) {\n      validateOptions(watchQueryOptions);\n    }\n\n    // Assign the updated fetch policy after our validation since `standby` is\n    // not a supported fetch policy on its own without the use of `skip`.\n    if (options.skip) {\n      watchQueryOptions.fetchPolicy = \"standby\";\n    }\n\n    return watchQueryOptions;\n  }, [client, options, query]);\n}\n"]}