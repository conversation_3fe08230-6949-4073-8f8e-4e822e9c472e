{"version": 3, "file": "useLazyQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useLazyQuery.ts"], "names": [], "mappings": ";AAEA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAQjC,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAUxD,OAAO,EACL,2BAA2B,EAC3B,qBAAqB,EACrB,kBAAkB,EAClB,aAAa,EACb,iBAAiB,GAClB,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,yBAAyB,EAAE,MAAM,yCAAyC,CAAC;AAEpF,2EAA2E;AAC3E,+DAA+D;AAC/D,IAAM,aAAa,GAAG;IACpB,SAAS;IACT,WAAW;IACX,WAAW;IACX,aAAa;IACb,cAAc;IACd,aAAa;IACb,iBAAiB;CACT,CAAC;AAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAM,UAAU,YAAY,CAI1B,KAA0D,EAC1D,OAAmE;;IAEnE,IAAM,cAAc,GAClB,KAAK,CAAC,MAAM,CAAuD,KAAK,CAAC,CAAC,CAAC;IAC7E,IAAM,UAAU,GACd,KAAK,CAAC,MAAM,CAA0C,KAAK,CAAC,CAAC,CAAC;IAChE,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAE3B,KAAK,CAAC,CAAC,CAAC;IACV,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IACnE,IAAM,QAAQ,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,mCAAI,KAAK,CAAC;IAExC,uEAAuE;IACvE,yDAAyD;IACzD,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC;IAE5B,IAAM,gBAAgB,yBACjB,MAAM,KACT,IAAI,EAAE,CAAC,cAAc,CAAC,OAAO,GAC9B,CAAC;IACI,IAAA,KAOF,iBAAiB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAN/C,cAAc,oBAAA,EACN,cAAc,YAAA,EACtB,MAAM,YAAA,EACN,UAAU,gBAAA,EACV,UAAU,gBAAA,EACV,eAAe,qBACgC,CAAC;IAElD,IAAM,kBAAkB,GACtB,UAAU,CAAC,OAAO,CAAC,kBAAkB;QACrC,qBAAqB,CACnB,gBAAgB,CAAC,cAAc,EAC/B,MAAM,CAAC,cAAc,CACtB,CAAC;IAEJ,IAAM,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,GAAG,CAAC,EAAR,CAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,6EAA6E;IAC7E,IAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QACjC,IAAM,YAAY,GAAwB,EAAE,CAAC;gCAClC,GAAG;YACZ,IAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;YACnC,YAAY,CAAC,GAAG,CAAC,GAAG;gBAClB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;oBAC5B,cAAc,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC7C,sEAAsE;oBACtE,gBAAgB,EAAE,CAAC;gBACrB,CAAC;gBACD,oDAAoD;gBACpD,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACvC,CAAC,CAAC;;QAVJ,KAAkB,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa;YAA1B,IAAM,GAAG,sBAAA;oBAAH,GAAG;SAWb;QAED,OAAO,YAAqC,CAAC;IAC/C,CAAC,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC,CAAC;IAEvC,IAAM,MAAM,GAAG,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC;IACxC,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAC1B,cAAM,OAAA,gCACD,cAAc,GACd,YAAY,KACf,MAAM,QAAA,IACN,EAJI,CAIJ,EACF,CAAC,cAAc,EAAE,YAAY,EAAE,MAAM,CAAC,CACvC,CAAC;IAEF,IAAM,OAAO,GAAG,KAAK,CAAC,WAAW,CAC/B,UAAC,cAAc;QACb,cAAc,CAAC,OAAO;YACpB,cAAc,CAAC,CAAC,uBAET,cAAc,KACjB,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,kBAAkB,IAEjE,CAAC,CAAC;gBACE,WAAW,EAAE,kBAAkB;aAChC,CAAC;QAEN,IAAM,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,OAAO,aAC7C,KAAK,EAAE,QAAQ,CAAC,OAAO,IACpB,cAAc,CAAC,OAAO,EACzB,CAAC;QAEH,IAAM,OAAO,GAAG,YAAY,CAC1B,UAAU,EACV,UAAU,EACV,MAAM,EACN,QAAQ,wBACH,OAAO,KAAE,IAAI,EAAE,KAAK,KACzB,eAAe,CAChB,CAAC,IAAI,CAAC,UAAC,WAAW,IAAK,OAAA,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,EAAxC,CAAwC,CAAC,CAAC;QAElE,yEAAyE;QACzE,wDAAwD;QACxD,OAAO,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;QAExB,OAAO,OAAO,CAAC;IACjB,CAAC,EACD;QACE,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,kBAAkB;QAClB,UAAU;QACV,UAAU;QACV,eAAe;KAChB,CACF,CAAC;IAEF,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzC,yBAAyB,CAAC;QACxB,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,IAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CACrC;QAAC,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAAK,OAAA,UAAU,CAAC,OAAO,OAAlB,UAAU,EAAY,IAAI;IAA1B,CAA2B,EACxC,EAAE,CACH,CAAC;IACF,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,YAAY,CACnB,UAA6C,EAC7C,UAA+C,EAC/C,MAA4B,EAC5B,YAA0B,EAC1B,OAEC,EACD,eAAwE;IAExE,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC;IAC5C,IAAM,iBAAiB,GAAG,2BAA2B,CACnD,MAAM,EACN,KAAK,EACL,OAAO,EACP,KAAK,CACN,CAAC,UAAU,CAAC,CAAC;IAEd,IAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,CAC3C,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CACnE,CAAC;IACF,eAAe,CAAC,iBAAiB,CAAC,CAAC;IAEnC,OAAO,IAAI,OAAO,CAEhB,UAAC,OAAO;QACR,IAAI,MAAgC,CAAC;QAErC,wEAAwE;QACxE,0EAA0E;QAC1E,yEAAyE;QACzE,iDAAiD;QACjD,OAAO,CAAC,SAAS,CAAC;YAChB,IAAI,EAAE,UAAC,KAAK;gBACV,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YACD,KAAK,EAAE;gBACL,OAAO,CACL,aAAa,CACX,UAAU,CAAC,gBAAgB,EAAE,EAC7B,UAAU,CAAC,YAAY,EACvB,UAAU,EACV,MAAM,CACP,CACF,CAAC;YACJ,CAAC;YACD,QAAQ,EAAE;gBACR,OAAO,CACL,aAAa,CACX,UAAU,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAChC,UAAU,CAAC,YAAY,EACvB,UAAU,EACV,MAAM,CACP,CACF,CAAC;YACJ,CAAC;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport * as React from \"rehackt\";\n\nimport type {\n  ApolloClient,\n  ApolloQueryResult,\n  OperationVariables,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport type {\n  LazyQueryHookExecOptions,\n  LazyQueryHookOptions,\n  LazyQueryResultTuple,\n  NoInfer,\n  QueryHookOptions,\n  QueryResult,\n} from \"../types/types.js\";\nimport type { InternalResult, ObsQueryWithMeta } from \"./useQuery.js\";\nimport {\n  createMakeWatchQueryOptions,\n  getDefaultFetchPolicy,\n  getObsQueryOptions,\n  toQueryResult,\n  useQueryInternals,\n} from \"./useQuery.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\n\n// The following methods, when called will execute the query, regardless of\n// whether the useLazyQuery execute function was called before.\nconst EAGER_METHODS = [\n  \"refetch\",\n  \"reobserve\",\n  \"fetchMore\",\n  \"updateQuery\",\n  \"startPolling\",\n  \"stopPolling\",\n  \"subscribeToMore\",\n] as const;\n\n/**\n * A hook for imperatively executing queries in an Apollo application, e.g. in response to user interaction.\n *\n * > Refer to the [Queries - Manual execution with useLazyQuery](https://www.apollographql.com/docs/react/data/queries#manual-execution-with-uselazyquery) section for a more in-depth overview of `useLazyQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useLazyQuery } from \"@apollo/client\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const [loadGreeting, { called, loading, data }] = useLazyQuery(\n *     GET_GREETING,\n *     { variables: { language: \"english\" } }\n *   );\n *   if (called && loading) return <p>Loading ...</p>\n *   if (!called) {\n *     return <button onClick={() => loadGreeting()}>Load greeting</button>\n *   }\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n * @since 3.0.0\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Default options to control how the query is executed.\n * @returns A tuple in the form of `[execute, result]`\n */\nexport function useLazyQuery<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: LazyQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n): LazyQueryResultTuple<TData, TVariables> {\n  const execOptionsRef =\n    React.useRef<Partial<LazyQueryHookExecOptions<TData, TVariables>>>(void 0);\n  const optionsRef =\n    React.useRef<LazyQueryHookOptions<TData, TVariables>>(void 0);\n  const queryRef = React.useRef<\n    DocumentNode | TypedDocumentNode<TData, TVariables>\n  >(void 0);\n  const merged = mergeOptions(options, execOptionsRef.current || {});\n  const document = merged?.query ?? query;\n\n  // Use refs to track options and the used query to ensure the `execute`\n  // function remains referentially stable between renders.\n  optionsRef.current = options;\n  queryRef.current = document;\n\n  const queryHookOptions = {\n    ...merged,\n    skip: !execOptionsRef.current,\n  };\n  const {\n    obsQueryFields,\n    result: useQueryResult,\n    client,\n    resultData,\n    observable,\n    onQueryExecuted,\n  } = useQueryInternals(document, queryHookOptions);\n\n  const initialFetchPolicy =\n    observable.options.initialFetchPolicy ||\n    getDefaultFetchPolicy(\n      queryHookOptions.defaultOptions,\n      client.defaultOptions\n    );\n\n  const forceUpdateState = React.useReducer((tick) => tick + 1, 0)[1];\n  // We use useMemo here to make sure the eager methods have a stable identity.\n  const eagerMethods = React.useMemo(() => {\n    const eagerMethods: Record<string, any> = {};\n    for (const key of EAGER_METHODS) {\n      const method = obsQueryFields[key];\n      eagerMethods[key] = function () {\n        if (!execOptionsRef.current) {\n          execOptionsRef.current = Object.create(null);\n          // Only the first time populating execOptionsRef.current matters here.\n          forceUpdateState();\n        }\n        // @ts-expect-error this is just too generic to type\n        return method.apply(this, arguments);\n      };\n    }\n\n    return eagerMethods as typeof obsQueryFields;\n  }, [forceUpdateState, obsQueryFields]);\n\n  const called = !!execOptionsRef.current;\n  const result = React.useMemo(\n    () => ({\n      ...useQueryResult,\n      ...eagerMethods,\n      called,\n    }),\n    [useQueryResult, eagerMethods, called]\n  );\n\n  const execute = React.useCallback<LazyQueryResultTuple<TData, TVariables>[0]>(\n    (executeOptions) => {\n      execOptionsRef.current =\n        executeOptions ?\n          {\n            ...executeOptions,\n            fetchPolicy: executeOptions.fetchPolicy || initialFetchPolicy,\n          }\n        : {\n            fetchPolicy: initialFetchPolicy,\n          };\n\n      const options = mergeOptions(optionsRef.current, {\n        query: queryRef.current,\n        ...execOptionsRef.current,\n      });\n\n      const promise = executeQuery(\n        resultData,\n        observable,\n        client,\n        document,\n        { ...options, skip: false },\n        onQueryExecuted\n      ).then((queryResult) => Object.assign(queryResult, eagerMethods));\n\n      // Because the return value of `useLazyQuery` is usually floated, we need\n      // to catch the promise to prevent unhandled rejections.\n      promise.catch(() => {});\n\n      return promise;\n    },\n    [\n      client,\n      document,\n      eagerMethods,\n      initialFetchPolicy,\n      observable,\n      resultData,\n      onQueryExecuted,\n    ]\n  );\n\n  const executeRef = React.useRef(execute);\n  useIsomorphicLayoutEffect(() => {\n    executeRef.current = execute;\n  });\n\n  const stableExecute = React.useCallback<typeof execute>(\n    (...args) => executeRef.current(...args),\n    []\n  );\n  return [stableExecute, result];\n}\n\nfunction executeQuery<TData, TVariables extends OperationVariables>(\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  client: ApolloClient<object>,\n  currentQuery: DocumentNode,\n  options: QueryHookOptions<TData, TVariables> & {\n    query?: DocumentNode;\n  },\n  onQueryExecuted: (options: WatchQueryOptions<TVariables, TData>) => void\n) {\n  const query = options.query || currentQuery;\n  const watchQueryOptions = createMakeWatchQueryOptions(\n    client,\n    query,\n    options,\n    false\n  )(observable);\n\n  const concast = observable.reobserveAsConcast(\n    getObsQueryOptions(observable, client, options, watchQueryOptions)\n  );\n  onQueryExecuted(watchQueryOptions);\n\n  return new Promise<\n    Omit<QueryResult<TData, TVariables>, (typeof EAGER_METHODS)[number]>\n  >((resolve) => {\n    let result: ApolloQueryResult<TData>;\n\n    // Subscribe to the concast independently of the ObservableQuery in case\n    // the component gets unmounted before the promise resolves. This prevents\n    // the concast from terminating early and resolving with `undefined` when\n    // there are no more subscribers for the concast.\n    concast.subscribe({\n      next: (value) => {\n        result = value;\n      },\n      error: () => {\n        resolve(\n          toQueryResult(\n            observable.getCurrentResult(),\n            resultData.previousData,\n            observable,\n            client\n          )\n        );\n      },\n      complete: () => {\n        resolve(\n          toQueryResult(\n            observable[\"maskResult\"](result),\n            resultData.previousData,\n            observable,\n            client\n          )\n        );\n      },\n    });\n  });\n}\n"]}