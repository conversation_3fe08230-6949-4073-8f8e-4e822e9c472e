import * as React from "rehackt";
import { assertWrappedQueryRef, getWrappedPromise, unwrapQueryRef, updateWrappedQueryRef, wrapQueryRef, } from "../internal/index.js";
import { useApolloClient } from "./useApolloClient.js";
import { wrapHook } from "./internal/index.js";
/**
 * A React hook that returns a `refetch` and `fetchMore` function for a given
 * `queryRef`.
 *
 * This is useful to get access to handlers for a `queryRef` that was created by
 * `createQueryPreloader` or when the handlers for a `queryRef` produced in
 * a different component are inaccessible.
 *
 * @example
 * ```tsx
 * const MyComponent({ queryRef }) {
 *   const { refetch, fetchMore } = useQueryRefHandlers(queryRef);
 *
 *   // ...
 * }
 * ```
 * @since 3.9.0
 * @param queryRef - A `QueryRef` returned from `useBackgroundQuery`, `useLoadableQuery`, or `createQueryPreloader`.
 */
export function useQueryRefHandlers(queryRef) {
    var unwrapped = unwrapQueryRef(queryRef);
    return wrapHook("useQueryRefHandlers", useQueryRefHandlers_, unwrapped ?
        unwrapped["observable"]
        // in the case of a "transported" queryRef object, we need to use the
        // client that's available to us at the current position in the React tree
        // that ApolloClient will then have the job to recreate a real queryRef from
        // the transported object
        // This is just a context read - it's fine to do this conditionally.
        // This hook wrapper also shouldn't be optimized by React Compiler.
        // eslint-disable-next-line react-compiler/react-compiler
        // eslint-disable-next-line react-hooks/rules-of-hooks
        : useApolloClient())(queryRef);
}
function useQueryRefHandlers_(queryRef) {
    assertWrappedQueryRef(queryRef);
    var _a = React.useState(queryRef), previousQueryRef = _a[0], setPreviousQueryRef = _a[1];
    var _b = React.useState(queryRef), wrappedQueryRef = _b[0], setWrappedQueryRef = _b[1];
    var internalQueryRef = unwrapQueryRef(queryRef);
    // To ensure we can support React transitions, this hook needs to manage the
    // queryRef state and apply React's state value immediately to the existing
    // queryRef since this hook doesn't return the queryRef directly
    if (previousQueryRef !== queryRef) {
        setPreviousQueryRef(queryRef);
        setWrappedQueryRef(queryRef);
    }
    else {
        updateWrappedQueryRef(queryRef, getWrappedPromise(wrappedQueryRef));
    }
    var refetch = React.useCallback(function (variables) {
        var promise = internalQueryRef.refetch(variables);
        setWrappedQueryRef(wrapQueryRef(internalQueryRef));
        return promise;
    }, [internalQueryRef]);
    var fetchMore = React.useCallback(function (options) {
        var promise = internalQueryRef.fetchMore(options);
        setWrappedQueryRef(wrapQueryRef(internalQueryRef));
        return promise;
    }, [internalQueryRef]);
    return {
        refetch: refetch,
        fetchMore: fetchMore,
        subscribeToMore: internalQueryRef.observable.subscribeToMore,
    };
}
//# sourceMappingURL=useQueryRefHandlers.js.map