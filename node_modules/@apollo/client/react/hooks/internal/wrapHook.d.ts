import type { useQuery, useSuspenseQuery, useBackgroundQuery, useReadQuery, useFragment, useQueryRefHandlers } from "../index.js";
import type { ApolloClient } from "../../../core/ApolloClient.js";
import type { ObservableQuery } from "../../../core/ObservableQuery.js";
import type { createQueryPreloader } from "../../query-preloader/createQueryPreloader.js";
interface WrappableHooks {
    createQueryPreloader: typeof createQueryPreloader;
    useQuery: typeof useQuery;
    useSuspenseQuery: typeof useSuspenseQuery;
    useBackgroundQuery: typeof useBackgroundQuery;
    useReadQuery: typeof useReadQuery;
    useFragment: typeof useFragment;
    useQueryRefHandlers: typeof useQueryRefHandlers;
}
/**
 * @internal
 * Can be used to correctly type the [Symbol.for("apollo.hook.wrappers")] property of
 * `QueryManager`, to override/wrap hook functionality.
 */
export type HookWrappers = {
    [K in keyof WrappableHooks]?: (originalHook: WrappableHooks[K]) => WrappableHooks[K];
};
/**
 * @internal
 *
 * Makes an Apollo Client hook "wrappable".
 * That means that the Apollo Client instance can expose a "wrapper" that will be
 * used to wrap the original hook implementation with additional logic.
 * @example
 * ```tsx
 * // this is already done in `@apollo/client` for all wrappable hooks (see `WrappableHooks`)
 * // following this pattern
 * function useQuery() {
 *   return wrapHook('useQuery', _useQuery, options.client)(query, options);
 * }
 * function _useQuery(query, options) {
 *   // original implementation
 * }
 *
 * // this is what a library like `@apollo/client-react-streaming` would do
 * class ApolloClientWithStreaming extends ApolloClient {
 *   constructor(options) {
 *     super(options);
 *     this.queryManager[Symbol.for("apollo.hook.wrappers")] = {
 *       useQuery: (original) => (query, options) => {
 *         console.log("useQuery was called with options", options);
 *         return original(query, options);
 *       }
 *     }
 *   }
 * }
 *
 * // this will now log the options and then call the original `useQuery`
 * const client = new ApolloClientWithStreaming({ ... });
 * useQuery(query, { client });
 * ```
 */
export declare function wrapHook<Hook extends (...args: any[]) => any>(hookName: keyof WrappableHooks, useHook: Hook, clientOrObsQuery: ObservableQuery<any> | ApolloClient<any>): Hook;
export {};
//# sourceMappingURL=wrapHook.d.ts.map