{"version": 3, "file": "wrapHook.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/wrapHook.ts"], "names": [], "mappings": "AAaA,IAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AA2BzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAM,UAAU,QAAQ,CACtB,QAA8B,EAC9B,OAAa,EACb,gBAA0D;IAE1D,IAAM,YAAY,GAChB,gBAKD,CAAC,cAAc,CAAC,CAAC;IAClB,IAAM,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,aAAa,CAAC,CAAC;IAC7D,IAAM,OAAO,GACX,QAAQ,IAAK,QAAQ,CAAC,QAAQ,CAAS,CAAC;IAC1C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAC9C,CAAC", "sourcesContent": ["import type {\n  useQuery,\n  useSuspenseQuery,\n  useBackgroundQuery,\n  useReadQuery,\n  useFragment,\n  useQueryRefHandlers,\n} from \"../index.js\";\nimport type { QueryManager } from \"../../../core/QueryManager.js\";\nimport type { ApolloClient } from \"../../../core/ApolloClient.js\";\nimport type { ObservableQuery } from \"../../../core/ObservableQuery.js\";\nimport type { createQueryPreloader } from \"../../query-preloader/createQueryPreloader.js\";\n\nconst wrapperSymbol = Symbol.for(\"apollo.hook.wrappers\");\n\ninterface WrappableHooks {\n  createQueryPreloader: typeof createQueryPreloader;\n  useQuery: typeof useQuery;\n  useSuspenseQuery: typeof useSuspenseQuery;\n  useBackgroundQuery: typeof useBackgroundQuery;\n  useReadQuery: typeof useReadQuery;\n  useFragment: typeof useFragment;\n  useQueryRefHandlers: typeof useQueryRefHandlers;\n}\n\n/**\n * @internal\n * Can be used to correctly type the [Symbol.for(\"apollo.hook.wrappers\")] property of\n * `QueryManager`, to override/wrap hook functionality.\n */\nexport type HookWrappers = {\n  [K in keyof WrappableHooks]?: (\n    originalHook: WrappableHooks[K]\n  ) => WrappableHooks[K];\n};\n\ninterface QueryManagerWithWrappers<T> extends QueryManager<T> {\n  [wrapperSymbol]?: HookWrappers;\n}\n\n/**\n * @internal\n *\n * Makes an Apollo Client hook \"wrappable\".\n * That means that the Apollo Client instance can expose a \"wrapper\" that will be\n * used to wrap the original hook implementation with additional logic.\n * @example\n * ```tsx\n * // this is already done in `@apollo/client` for all wrappable hooks (see `WrappableHooks`)\n * // following this pattern\n * function useQuery() {\n *   return wrapHook('useQuery', _useQuery, options.client)(query, options);\n * }\n * function _useQuery(query, options) {\n *   // original implementation\n * }\n *\n * // this is what a library like `@apollo/client-react-streaming` would do\n * class ApolloClientWithStreaming extends ApolloClient {\n *   constructor(options) {\n *     super(options);\n *     this.queryManager[Symbol.for(\"apollo.hook.wrappers\")] = {\n *       useQuery: (original) => (query, options) => {\n *         console.log(\"useQuery was called with options\", options);\n *         return original(query, options);\n *       }\n *     }\n *   }\n * }\n *\n * // this will now log the options and then call the original `useQuery`\n * const client = new ApolloClientWithStreaming({ ... });\n * useQuery(query, { client });\n * ```\n */\nexport function wrapHook<Hook extends (...args: any[]) => any>(\n  hookName: keyof WrappableHooks,\n  useHook: Hook,\n  clientOrObsQuery: ObservableQuery<any> | ApolloClient<any>\n): Hook {\n  const queryManager = (\n    clientOrObsQuery as unknown as {\n      // both `ApolloClient` and `ObservableQuery` have a `queryManager` property\n      // but they're both `private`, so we have to cast around for a bit here.\n      queryManager: QueryManagerWithWrappers<any>;\n    }\n  )[\"queryManager\"];\n  const wrappers = queryManager && queryManager[wrapperSymbol];\n  const wrapper: undefined | ((wrap: Hook) => Hook) =\n    wrappers && (wrappers[hookName] as any);\n  return wrapper ? wrapper(useHook) : useHook;\n}\n"]}