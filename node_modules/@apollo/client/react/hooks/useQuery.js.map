{"version": 3, "file": "useQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useQuery.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM;AACN,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAE7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAQtC,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAQpD,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAQpD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EACL,OAAO,EACP,eAAe,EACf,eAAe,GAChB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAKhC,IAAA,cAAc,GACzB,MAAM,yBADmB,CAClB;AAOX,SAAS,IAAI,KAAI,CAAC;AAClB,MAAM,CAAC,IAAM,gBAAgB,GAAG,MAAM,EAAE,CAAC;AAkCzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,UAAU,QAAQ,CAItB,KAA0D,EAC1D,OAGuB;IAHvB,wBAAA,EAAA,UAGI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEvB,OAAO,QAAQ,CACb,UAAU;IACV,yDAAyD;IACzD,SAAS,EACT,eAAe,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAC3C,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,SAAS,CAIhB,KAA0D,EAC1D,OAA8D;IAExD,IAAA,KAA6B,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,EAA5D,MAAM,YAAA,EAAE,cAAc,oBAAsC,CAAC;IACrE,OAAO,KAAK,CAAC,OAAO,CAClB,cAAM,OAAA,uBAAM,MAAM,GAAK,cAAc,EAAG,EAAlC,CAAkC,EACxC,CAAC,MAAM,EAAE,cAAc,CAAC,CACzB,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAIvB,MAA4B,EAC5B,KAAiD,EACjD,OAA8D,EAC9D,cAA0C,EAC1C,qBAAiE;IAEjE,SAAS,mBAAmB,CAAC,QAA2C;;QACtE,kBAAkB,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAM,aAAa,GAAqC;YACtD,MAAM,QAAA;YACN,KAAK,OAAA;YACL,UAAU;YACR,yEAAyE;YACzE,0EAA0E;YAC1E,oDAAoD;YACpD,CAAC,cAAc;gBACb,cAAc,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CACf,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CACrE;YACH,UAAU,EAAE;gBACV,qEAAqE;gBACrE,uEAAuE;gBACvE,YAAY,EAAE,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,CAAC,OAAO,0CAAE,IAAI;aACjD;SACF,CAAC;QAEF,OAAO,aAAiD,CAAC;IAC3D,CAAC;IAEG,IAAA,KACF,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EADhC,aAAa,QAAA,EAAE,mBAAmB,QACF,CAAC;IAEtC;;;;OAIG;IACH,SAAS,eAAe,CACtB,iBAAuD;;;QAEvD,oEAAoE;QACpE,4CAA4C;QAC5C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU;YACpC,GAAC,gBAAgB,IAAG,iBAAiB;gBACrC,CAAC;QACH,IAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;QAC5C,mBAAmB,uBACd,aAAa;YAChB,6BAA6B;YAC7B,KAAK,EAAE,iBAAiB,CAAC,KAAK,EAC9B,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBACpC,uEAAuE;gBACvE,mCAAmC;gBACnC,YAAY,EAAE,CAAA,MAAA,UAAU,CAAC,OAAO,0CAAE,IAAI,KAAI,UAAU,CAAC,YAAY;gBACjE,OAAO,EAAE,SAAS;aACnB,CAAC,IACF,CAAC;IACL,CAAC;IAED,IAAI,MAAM,KAAK,aAAa,CAAC,MAAM,IAAI,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE,CAAC;QACrE,8EAA8E;QAC9E,8EAA8E;QAC9E,oDAAoD;QACpD,0EAA0E;QAC1E,2EAA2E;QAC3E,gCAAgC;QAChC,IAAM,gBAAgB,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAC5D,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACtC,OAAO,CAAC,gBAAgB,EAAE,eAAe,CAAU,CAAC;IACtD,CAAC;IAED,OAAO,CAAC,aAAa,EAAE,eAAe,CAAU,CAAC;AACnD,CAAC;AAED,MAAM,UAAU,iBAAiB,CAI/B,KAA0D,EAC1D,OAA8D;IAE9D,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE/C,IAAM,cAAc,GAAG,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,cAAc,CAAC;IAC3E,IAAM,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC;IACnC,IAAM,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,CAAC;IAC3D,IAAM,UAAU,GAAG,OAAO,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC1D,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAE9C,IAAM,qBAAqB,GAAG,2BAA2B,CACvD,MAAM,EACN,KAAK,EACL,OAAO,EACP,SAAS,CACV,CAAC;IAEI,IAAA,KAAgD,gBAAgB,CACpE,MAAM,EACN,KAAK,EACL,OAAO,EACP,cAAc,EACd,qBAAqB,CACtB,EANM,UAA0B,EAAxB,UAAU,gBAAA,EAAE,UAAU,gBAAA,EAAI,eAAe,QAMjD,CAAC;IAEF,IAAM,iBAAiB,GACrB,qBAAqB,CAAC,UAAU,CAAC,CAAC;IAEpC,yBAAyB,CACvB,UAAU,EAAE,kCAAkC;IAC9C,UAAU,EAAE,kCAAkC;IAC9C,MAAM,EACN,OAAO,EACP,iBAAiB,CAClB,CAAC;IAEF,IAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAElC,cAAM,OAAA,qBAAqB,CAAC,UAAU,CAAC,EAAjC,CAAiC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEzD,wBAAwB,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IAEjE,IAAM,MAAM,GAAG,+BAA+B,CAC5C,UAAU,EACV,UAAU,EACV,MAAM,EACN,OAAO,EACP,iBAAiB,EACjB,qBAAqB,EACrB,cAAc,EACd,SAAS,EACT;QACE,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI;QACxC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;KACjC,CACF,CAAC;IAEF,OAAO;QACL,MAAM,QAAA;QACN,cAAc,gBAAA;QACd,UAAU,YAAA;QACV,UAAU,YAAA;QACV,MAAM,QAAA;QACN,eAAe,iBAAA;KAChB,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CAItC,UAA6C,EAC7C,UAA8C,EAC9C,MAA4B,EAC5B,OAA8D,EAC9D,iBAAiE,EACjE,qBAA8B,EAC9B,cAAmC,EACnC,SAAkB,EAClB,SAGC;IAED,IAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAmB,SAAS,CAAC,CAAC;IAC9D,KAAK,CAAC,SAAS,CAAC;QACd,0EAA0E;QAC1E,0EAA0E;QAC1E,2EAA2E;QAC3E,4EAA4E;QAC5E,4EAA4E;QAC5E,uDAAuD;QACvD,yDAAyD;QACzD,WAAW,CAAC,OAAO,GAAG,SAAS,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,IAAM,cAAc,GAClB,CACE,CAAC,SAAS,IAAI,qBAAqB,CAAC;QACpC,OAAO,CAAC,GAAG,KAAK,KAAK;QACrB,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,CAAC;QACD,yEAAyE;QACzE,wDAAwD;QACxD,iBAAiB;QACnB,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC;YAC7D,2EAA2E;YAC3E,yEAAyE;YACzE,qDAAqD;YACrD,EAAE;YACF,yEAAyE;YACzE,qEAAqE;YACrE,2EAA2E;YAC3E,sEAAsE;YACtE,2EAA2E;YAC3E,mBAAmB;YACnB,iBAAiB;YACnB,CAAC,CAAC,KAAK,CAAC,CAAC;IAEX,IAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;IAC7C,IAAM,qBAAqB,GAAG,KAAK,CAAC,OAAO,CACzC;QACE,OAAA,cAAc;YACd,aAAa,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC;IAD/D,CAC+D,EACjE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,CAAC,CACnD,CAAC;IAEF,OAAO,oBAAoB,CACzB,KAAK,CAAC,WAAW,CACf,UAAC,iBAAiB;QAChB,2EAA2E;QAC3E,oEAAoE;QACpE,qBAAqB,CAAC;QAEtB,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,cAAO,CAAC,CAAC;QAClB,CAAC;QAED,IAAM,MAAM,GAAG;YACb,IAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;YAC1C,qEAAqE;YACrE,sEAAsE;YACtE,mEAAmE;YACnE,IAAM,MAAM,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAC7C,8DAA8D;YAC9D,IACE,cAAc;gBACd,cAAc,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO;gBACzC,cAAc,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa;gBACrD,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EACvC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,SAAS,CACP,MAAM,EACN,UAAU,EACV,UAAU,EACV,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,WAAW,CAAC,OAAO,CACpB,CAAC;QACJ,CAAC,CAAC;QAEF,IAAM,OAAO,GAAG,UAAC,KAAY;YAC3B,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACnC,YAAY,CAAC,OAAO,GAAG,UAAU,CAAC,qBAAqB,CACrD,MAAM,EACN,OAAO,CACR,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC;gBACjD,mCAAmC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;YAC1C,IACE,CAAC,cAAc;gBACf,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;gBAC1C,CAAC,KAAK,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,EACnC,CAAC;gBACD,SAAS,CACP;oBACE,IAAI,EAAE,CAAC,cAAc;wBACnB,cAAc,CAAC,IAAI,CAAuB;oBAC5C,KAAK,EAAE,KAAoB;oBAC3B,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,aAAa,CAAC,KAAK;iBACnC,EACD,UAAU,EACV,UAAU,EACV,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,WAAW,CAAC,OAAO,CACpB,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QAEF,mCAAmC;QACnC,yEAAyE;QACzE,qBAAqB;QACrB,OAAO;QACP,4DAA4D;QAC5D,IAAM,YAAY,GAAG,EAAE,OAAO,EAAE,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;QAExE,2CAA2C;QAC3C,yEAAyE;QACzE,0EAA0E;QAC1E,kCAAkC;QAClC,OAAO;YACL,UAAU,CAAC,cAAM,OAAA,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,EAAlC,CAAkC,CAAC,CAAC;QACvD,CAAC,CAAC;IACJ,CAAC,EAED;QACE,qBAAqB;QACrB,SAAS;QACT,UAAU;QACV,UAAU;QACV,cAAc;QACd,MAAM;KACP,CACF,EACD;QACE,OAAA,qBAAqB;YACrB,gBAAgB,CACd,UAAU,EACV,UAAU,EACV,WAAW,CAAC,OAAO,EACnB,cAAc,EACd,MAAM,CACP;IAPD,CAOC,EACH;QACE,OAAA,qBAAqB;YACrB,gBAAgB,CACd,UAAU,EACV,UAAU,EACV,WAAW,CAAC,OAAO,EACnB,cAAc,EACd,MAAM,CACP;IAPD,CAOC,CACJ,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAsC,EACtC,cAA0C,EAC1C,UAAmB;IAEnB,IAAI,cAAc,IAAI,UAAU,EAAE,CAAC;QACjC,cAAc,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAEjD,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,CAAC;YAC1C,gEAAgE;YAChE,cAAc,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;AACH,CAAC;AAED,8FAA8F;AAC9F,4EAA4E;AAC5E,SAAS,yBAAyB;AAIhC,uDAAuD;AACvD,UAA6C;AAC7C,uDAAuD;AACvD,UAA+C,EAC/C,MAA4B,EAC5B,OAA8D,EAC9D,iBAAiE;;IAEjE,IACE,UAAU,CAAC,gBAAgB,CAAC;QAC5B,CAAC,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,iBAAiB,CAAC,EACvD,CAAC;QACD,qEAAqE;QACrE,qEAAqE;QACrE,mEAAmE;QACnE,sEAAsE;QACtE,kEAAkE;QAClE,oEAAoE;QACpE,mEAAmE;QACnE,+DAA+D;QAC/D,UAAU,CAAC,SAAS,CAClB,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CACnE,CAAC;QAEF,uEAAuE;QACvE,sEAAsE;QACtE,gBAAgB;QAChB,UAAU,CAAC,YAAY;YACrB,CAAA,MAAA,UAAU,CAAC,OAAO,0CAAE,IAAI,KAAI,UAAU,CAAC,YAAY,CAAC;QACtD,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;IAC9B,CAAC;IACD,UAAU,CAAC,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;AACnD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,2BAA2B,CAIzC,MAA4B,EAC5B,KAA0D,EAC1D,EAU2C,EAC3C,SAAkB;IAXlB,mBAAA,EAAA,OAU2C;IATzC,IAAA,IAAI,UAAA,EACJ,GAAG,SAAA,EACH,WAAW,iBAAA,EACX,OAAO,aAAA,EACP,cAAc,oBAAA;IACd,0EAA0E;IAC1E,uEAAuE;IACvE,oCAAoC;IACjC,YAAY,cATjB,2DAUC,CADgB;IAIjB,OAAO,UACL,UAA+C;QAE/C,4EAA4E;QAC5E,yEAAyE;QACzE,IAAM,iBAAiB,GACrB,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC;QAEzC,IACE,SAAS;YACT,CAAC,iBAAiB,CAAC,WAAW,KAAK,cAAc;gBAC/C,iBAAiB,CAAC,WAAW,KAAK,mBAAmB,CAAC,EACxD,CAAC;YACD,yEAAyE;YACzE,0DAA0D;YAC1D,iBAAiB,CAAC,WAAW,GAAG,aAAa,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACjC,iBAAiB,CAAC,SAAS,GAAG,EAAgB,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,mEAAmE;YACnE,uEAAuE;YACvE,yDAAyD;YACzD,iBAAiB,CAAC,kBAAkB;gBAClC,iBAAiB,CAAC,kBAAkB;oBACpC,iBAAiB,CAAC,WAAW;oBAC7B,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;YAC/D,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;QAC5C,CAAC;aAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAC1C,iBAAiB,CAAC,WAAW;gBAC3B,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,CAAC,kBAAkB;oBACtC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAIhC,UAA0D,EAC1D,MAA4B,EAC5B,gBAAqD,EACrD,iBAAgE;IAEhE,IAAM,OAAO,GAAyD,EAAE,CAAC;IAEzE,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;IACxD,IAAI,cAAc;QAAE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAEjD,IAAI,gBAAgB,CAAC,cAAc,EAAE,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;IAChD,CAAC;IAED,sEAAsE;IACtE,sEAAsE;IACtE,mEAAmE;IACnE,kEAAkE;IAClE,sEAAsE;IACtE,iEAAiE;IACjE,wEAAwE;IACxE,iEAAiE;IACjE,4DAA4D;IAC5D,2CAA2C;IAC3C,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAE3E,OAAO,OAAO,CAAC,MAAM,CAAC,YAAY,CAAyC,CAAC;AAC9E,CAAC;AAED,SAAS,SAAS,CAChB,UAAiD,EACjD,UAA6C,EAC7C,UAA8C,EAC9C,MAA4B,EAC5B,cAAmC,EACnC,WAAuB,EACvB,SAA2B;IAE3B,IAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;IAC1C,IAAI,cAAc,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;QAC1C,UAAU,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;IAChD,CAAC;IAED,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5D,sEAAsE;QACtE,oEAAoE;QACpE,qEAAqE;QACrE,gBAAgB;QAChB,UAAU,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED,UAAU,CAAC,OAAO,GAAG,aAAa,CAChC,0BAA0B,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAClE,UAAU,CAAC,YAAY,EACvB,UAAU,EACV,MAAM,CACP,CAAC;IACF,4EAA4E;IAC5E,qEAAqE;IACrE,WAAW,EAAE,CAAC;IACd,sBAAsB,CAAC,UAAU,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,aAAa,EAAE,SAAS,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,sBAAsB,CAC7B,MAA6C,EAC7C,qBAAgD,EAChD,SAA2B;IAE3B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACpB,IAAM,OAAK,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAEpC,oEAAoE;QACpE,OAAO,CAAC,OAAO,EAAE;aACd,IAAI,CAAC;YACJ,IAAI,OAAK,EAAE,CAAC;gBACV,SAAS,CAAC,OAAO,CAAC,OAAK,CAAC,CAAC;YAC3B,CAAC;iBAAM,IACL,MAAM,CAAC,IAAI;gBACX,qBAAqB,KAAK,MAAM,CAAC,aAAa;gBAC9C,MAAM,CAAC,aAAa,KAAK,aAAa,CAAC,KAAK,EAC5C,CAAC;gBACD,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;QACH,CAAC,CAAC;aACD,KAAK,CAAC,UAAC,KAAK;YACX,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,UAA6C,EAC7C,UAA8C,EAC9C,SAA2B,EAC3B,cAAmC,EACnC,MAA4B;IAE5B,4EAA4E;IAC5E,2EAA2E;IAC3E,wEAAwE;IACxE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACxB,+CAA+C;QAC/C,6CAA6C;QAC7C,SAAS,CACP,UAAU,CAAC,gBAAgB,EAAE,EAC7B,UAAU,EACV,UAAU,EACV,MAAM,EACN,cAAc,EACd,cAAO,CAAC,EACR,SAAS,CACV,CAAC;IACJ,CAAC;IACD,OAAO,UAAU,CAAC,OAAQ,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,qBAAqB,CAInC,uBAAuE,EACvE,oBAAqC;;IAErC,OAAO,CACL,CAAA,uBAAuB,aAAvB,uBAAuB,uBAAvB,uBAAuB,CAAE,WAAW;SACpC,MAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,UAAU,0CAAE,WAAW,CAAA;QAC7C,aAAa,CACd,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,MAA0D;IAE1D,OAAO,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACnC,IAAI,WAAW,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;QACnD,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,MAA6C,EAC7C,YAA4C,EAC5C,UAA8C,EAC9C,MAA4B;IAEpB,IAAA,IAAI,GAAuC,MAAM,KAA7C,EAAE,OAAO,GAA8B,MAAM,QAApC,EAAK,oBAAoB,UAAK,MAAM,EAAnD,mBAA0C,CAAF,CAAY;IAC1D,IAAM,WAAW,uBACf,IAAI,MAAA,IACD,oBAAoB,KACvB,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,UAAU,EACtB,SAAS,EAAE,UAAU,CAAC,SAAS,EAC/B,MAAM,EAAE,MAAM,KAAK,iBAAiB,IAAI,MAAM,KAAK,iBAAiB,EACpE,YAAY,cAAA,GACb,CAAC;IACF,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,0BAA0B,CAIjC,MAA6C,EAC7C,UAA8C,EAC9C,cAAmC;IAEnC,sEAAsE;IACtE,yEAAyE;IACzE,mDAAmD;IACnD,IACE,MAAM,CAAC,OAAO;QACd,cAAc;QACd,CAAC,MAAM,CAAC,OAAO;QACf,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QACvD,UAAU,CAAC,OAAO,CAAC,WAAW,KAAK,YAAY,EAC/C,CAAC;QACD,UAAU,CAAC,OAAO,EAAE,CAAC;QACrB,6BACK,MAAM,KACT,OAAO,EAAE,IAAI,EACb,aAAa,EAAE,aAAa,CAAC,OAAO,IACpC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,IAAM,iBAAiB,GAAG,eAAe,CAAC;IACxC,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,KAAK,CAAQ;IACnB,KAAK,EAAE,KAAK,CAAC;IACb,aAAa,EAAE,aAAa,CAAC,OAAO;CACrC,CAAC,CAAC;AAEH,IAAM,iBAAiB,GAAG,eAAe,CAAC;IACxC,OAAO,EAAE,KAAK;IACd,IAAI,EAAE,KAAK,CAAQ;IACnB,KAAK,EAAE,KAAK,CAAC;IACb,aAAa,EAAE,aAAa,CAAC,KAAK;CACnC,CAAC,CAAC;AAEH,SAAS,qBAAqB,CAC5B,UAA8C;IAE9C,OAAO;QACL,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5C,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;QAChD,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;QAChD,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,YAAY,EAAE,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;QACtD,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;KAC7D,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Function parameters in this file try to follow a common order for the sake of\n * readability and consistency. The order is as follows:\n *\n * resultData\n * observable\n * client\n * query\n * options\n * watchQueryOptions\n * makeWatchQueryOptions\n * isSSRAllowed\n * disableNetworkFetches\n * partialRefetch\n * renderPromises\n * isSyncSSR\n * callbacks\n */\n/** */\nimport { invariant } from \"../../utilities/globals/index.js\";\n\nimport * as React from \"rehackt\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { equal } from \"@wry/equality\";\n\nimport type {\n  ApolloClient,\n  DefaultOptions,\n  OperationVariables,\n  WatchQueryFetchPolicy,\n} from \"../../core/index.js\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { getApolloContext } from \"../context/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport type {\n  ApolloQueryResult,\n  ObservableQuery,\n  DocumentNode,\n  TypedDocumentNode,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport { NetworkStatus } from \"../../core/index.js\";\nimport type {\n  QueryHookOptions,\n  QueryResult,\n  ObservableQueryFields,\n  NoInfer,\n} from \"../types/types.js\";\n\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport {\n  compact,\n  isNonEmptyArray,\n  maybeDeepFreeze,\n} from \"../../utilities/index.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport type { RenderPromises } from \"../ssr/RenderPromises.js\";\nimport type { MaybeMasked } from \"../../masking/index.js\";\n\nconst {\n  prototype: { hasOwnProperty },\n} = Object;\n\ntype InternalQueryResult<TData, TVariables extends OperationVariables> = Omit<\n  QueryResult<TData, TVariables>,\n  Exclude<keyof ObservableQueryFields<TData, TVariables>, \"variables\">\n>;\n\nfunction noop() {}\nexport const lastWatchOptions = Symbol();\n\nexport interface ObsQueryWithMeta<TData, TVariables extends OperationVariables>\n  extends ObservableQuery<TData, TVariables> {\n  [lastWatchOptions]?: WatchQueryOptions<TVariables, TData>;\n}\n\nexport interface InternalResult<TData, TVariables extends OperationVariables> {\n  // These members are populated by getCurrentResult and setResult, and it's\n  // okay/normal for them to be initially undefined.\n  current?: undefined | InternalQueryResult<TData, TVariables>;\n  previousData?: undefined | MaybeMasked<TData>;\n}\n\ninterface InternalState<TData, TVariables extends OperationVariables> {\n  client: ReturnType<typeof useApolloClient>;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  observable: ObsQueryWithMeta<TData, TVariables>;\n  resultData: InternalResult<TData, TVariables>;\n}\n\nexport type UpdateInternalState<\n  TData,\n  TVariables extends OperationVariables,\n> = (state: InternalState<TData, TVariables>) => void;\n\ninterface Callbacks<TData> {\n  // Defining these methods as no-ops on the prototype allows us to call\n  // state.onCompleted and/or state.onError without worrying about whether a\n  // callback was provided.\n  onCompleted(data: MaybeMasked<TData>): void;\n  onError(error: ApolloError): void;\n}\n\n/**\n * A hook for executing queries in an Apollo application.\n *\n * To run a query within a React component, call `useQuery` and pass it a GraphQL query document.\n *\n * When your component renders, `useQuery` returns an object from Apollo Client that contains `loading`, `error`, and `data` properties you can use to render your UI.\n *\n * > Refer to the [Queries](https://www.apollographql.com/docs/react/data/queries) section for a more in-depth overview of `useQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useQuery } from '@apollo/client';\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const { loading, error, data } = useQuery(GET_GREETING, {\n *     variables: { language: 'english' },\n *   });\n *   if (loading) return <p>Loading ...</p>;\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n * @since 3.0.0\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns Query result object\n */\nexport function useQuery<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<\n    NoInfer<TData>,\n    NoInfer<TVariables>\n  > = Object.create(null)\n): QueryResult<TData, TVariables> {\n  return wrapHook(\n    \"useQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useQuery_,\n    useApolloClient(options && options.client)\n  )(query, options);\n}\n\nfunction useQuery_<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n) {\n  const { result, obsQueryFields } = useQueryInternals(query, options);\n  return React.useMemo(\n    () => ({ ...result, ...obsQueryFields }),\n    [result, obsQueryFields]\n  );\n}\n\nfunction useInternalState<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  client: ApolloClient<object>,\n  query: DocumentNode | TypedDocumentNode<any, any>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,\n  renderPromises: RenderPromises | undefined,\n  makeWatchQueryOptions: () => WatchQueryOptions<TVariables, TData>\n) {\n  function createInternalState(previous?: InternalState<TData, TVariables>) {\n    verifyDocumentType(query, DocumentType.Query);\n\n    const internalState: InternalState<TData, TVariables> = {\n      client,\n      query,\n      observable:\n        // See if there is an existing observable that was used to fetch the same\n        // data and if so, use it instead since it will contain the proper queryId\n        // to fetch the result set. This is used during SSR.\n        (renderPromises &&\n          renderPromises.getSSRObservable(makeWatchQueryOptions())) ||\n        client.watchQuery(\n          getObsQueryOptions(void 0, client, options, makeWatchQueryOptions())\n        ),\n      resultData: {\n        // Reuse previousData from previous InternalState (if any) to provide\n        // continuity of previousData even if/when the query or client changes.\n        previousData: previous?.resultData.current?.data,\n      },\n    };\n\n    return internalState as InternalState<TData, TVariables>;\n  }\n\n  let [internalState, updateInternalState] =\n    React.useState(createInternalState);\n\n  /**\n   * Used by `useLazyQuery` when a new query is executed.\n   * We keep this logic here since it needs to update things in unsafe\n   * ways and here we at least can keep track of that in a single place.\n   */\n  function onQueryExecuted(\n    watchQueryOptions: WatchQueryOptions<TVariables, TData>\n  ) {\n    // this needs to be set to prevent an immediate `resubscribe` in the\n    // next rerender of the `useQuery` internals\n    Object.assign(internalState.observable, {\n      [lastWatchOptions]: watchQueryOptions,\n    });\n    const resultData = internalState.resultData;\n    updateInternalState({\n      ...internalState,\n      // might be a different query\n      query: watchQueryOptions.query,\n      resultData: Object.assign(resultData, {\n        // We need to modify the previous `resultData` object as we rely on the\n        // object reference in other places\n        previousData: resultData.current?.data || resultData.previousData,\n        current: undefined,\n      }),\n    });\n  }\n\n  if (client !== internalState.client || query !== internalState.query) {\n    // If the client or query have changed, we need to create a new InternalState.\n    // This will trigger a re-render with the new state, but it will also continue\n    // to run the current render function to completion.\n    // Since we sometimes trigger some side-effects in the render function, we\n    // re-assign `state` to the new state to ensure that those side-effects are\n    // triggered with the new state.\n    const newInternalState = createInternalState(internalState);\n    updateInternalState(newInternalState);\n    return [newInternalState, onQueryExecuted] as const;\n  }\n\n  return [internalState, onQueryExecuted] as const;\n}\n\nexport function useQueryInternals<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n) {\n  const client = useApolloClient(options.client);\n\n  const renderPromises = React.useContext(getApolloContext()).renderPromises;\n  const isSyncSSR = !!renderPromises;\n  const disableNetworkFetches = client.disableNetworkFetches;\n  const ssrAllowed = options.ssr !== false && !options.skip;\n  const partialRefetch = options.partialRefetch;\n\n  const makeWatchQueryOptions = createMakeWatchQueryOptions(\n    client,\n    query,\n    options,\n    isSyncSSR\n  );\n\n  const [{ observable, resultData }, onQueryExecuted] = useInternalState(\n    client,\n    query,\n    options,\n    renderPromises,\n    makeWatchQueryOptions\n  );\n\n  const watchQueryOptions: Readonly<WatchQueryOptions<TVariables, TData>> =\n    makeWatchQueryOptions(observable);\n\n  useResubscribeIfNecessary<TData, TVariables>(\n    resultData, // might get mutated during render\n    observable, // might get mutated during render\n    client,\n    options,\n    watchQueryOptions\n  );\n\n  const obsQueryFields = React.useMemo<\n    Omit<ObservableQueryFields<TData, TVariables>, \"variables\">\n  >(() => bindObservableMethods(observable), [observable]);\n\n  useRegisterSSRObservable(observable, renderPromises, ssrAllowed);\n\n  const result = useObservableSubscriptionResult<TData, TVariables>(\n    resultData,\n    observable,\n    client,\n    options,\n    watchQueryOptions,\n    disableNetworkFetches,\n    partialRefetch,\n    isSyncSSR,\n    {\n      onCompleted: options.onCompleted || noop,\n      onError: options.onError || noop,\n    }\n  );\n\n  return {\n    result,\n    obsQueryFields,\n    observable,\n    resultData,\n    client,\n    onQueryExecuted,\n  };\n}\n\nfunction useObservableSubscriptionResult<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObservableQuery<TData, TVariables>,\n  client: ApolloClient<object>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,\n  watchQueryOptions: Readonly<WatchQueryOptions<TVariables, TData>>,\n  disableNetworkFetches: boolean,\n  partialRefetch: boolean | undefined,\n  isSyncSSR: boolean,\n  callbacks: {\n    onCompleted: (data: MaybeMasked<TData>) => void;\n    onError: (error: ApolloError) => void;\n  }\n) {\n  const callbackRef = React.useRef<Callbacks<TData>>(callbacks);\n  React.useEffect(() => {\n    // Make sure state.onCompleted and state.onError always reflect the latest\n    // options.onCompleted and options.onError callbacks provided to useQuery,\n    // since those functions are often recreated every time useQuery is called.\n    // Like the forceUpdate method, the versions of these methods inherited from\n    // InternalState.prototype are empty no-ops, but we can override them on the\n    // base state object (without modifying the prototype).\n    // eslint-disable-next-line react-compiler/react-compiler\n    callbackRef.current = callbacks;\n  });\n\n  const resultOverride =\n    (\n      (isSyncSSR || disableNetworkFetches) &&\n      options.ssr === false &&\n      !options.skip\n    ) ?\n      // If SSR has been explicitly disabled, and this function has been called\n      // on the server side, return the default loading state.\n      ssrDisabledResult\n    : options.skip || watchQueryOptions.fetchPolicy === \"standby\" ?\n      // When skipping a query (ie. we're not querying for data but still want to\n      // render children), make sure the `data` is cleared out and `loading` is\n      // set to `false` (since we aren't loading anything).\n      //\n      // NOTE: We no longer think this is the correct behavior. Skipping should\n      // not automatically set `data` to `undefined`, but instead leave the\n      // previous data in place. In other words, skipping should not mandate that\n      // previously received data is all of a sudden removed. Unfortunately,\n      // changing this is breaking, so we'll have to wait until Apollo Client 4.0\n      // to address this.\n      skipStandbyResult\n    : void 0;\n\n  const previousData = resultData.previousData;\n  const currentResultOverride = React.useMemo(\n    () =>\n      resultOverride &&\n      toQueryResult(resultOverride, previousData, observable, client),\n    [client, observable, resultOverride, previousData]\n  );\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (handleStoreChange) => {\n        // reference `disableNetworkFetches` here to ensure that the rules of hooks\n        // keep it as a dependency of this effect, even though it's not used\n        disableNetworkFetches;\n\n        if (isSyncSSR) {\n          return () => {};\n        }\n\n        const onNext = () => {\n          const previousResult = resultData.current;\n          // We use `getCurrentResult()` instead of the onNext argument because\n          // the values differ slightly. Specifically, loading results will have\n          // an empty object for data instead of `undefined` for some reason.\n          const result = observable.getCurrentResult();\n          // Make sure we're not attempting to re-render similar results\n          if (\n            previousResult &&\n            previousResult.loading === result.loading &&\n            previousResult.networkStatus === result.networkStatus &&\n            equal(previousResult.data, result.data)\n          ) {\n            return;\n          }\n\n          setResult(\n            result,\n            resultData,\n            observable,\n            client,\n            partialRefetch,\n            handleStoreChange,\n            callbackRef.current\n          );\n        };\n\n        const onError = (error: Error) => {\n          subscription.current.unsubscribe();\n          subscription.current = observable.resubscribeAfterError(\n            onNext,\n            onError\n          );\n\n          if (!hasOwnProperty.call(error, \"graphQLErrors\")) {\n            // The error is not a GraphQL error\n            throw error;\n          }\n\n          const previousResult = resultData.current;\n          if (\n            !previousResult ||\n            (previousResult && previousResult.loading) ||\n            !equal(error, previousResult.error)\n          ) {\n            setResult(\n              {\n                data: (previousResult &&\n                  previousResult.data) as MaybeMasked<TData>,\n                error: error as ApolloError,\n                loading: false,\n                networkStatus: NetworkStatus.error,\n              },\n              resultData,\n              observable,\n              client,\n              partialRefetch,\n              handleStoreChange,\n              callbackRef.current\n            );\n          }\n        };\n\n        // TODO evaluate if we keep this in\n        // React Compiler cannot handle scoped `let` access, but a mutable object\n        // like this is fine.\n        // was:\n        // let subscription = observable.subscribe(onNext, onError);\n        const subscription = { current: observable.subscribe(onNext, onError) };\n\n        // Do the \"unsubscribe\" with a short delay.\n        // This way, an existing subscription can be reused without an additional\n        // request if \"unsubscribe\"  and \"resubscribe\" to the same ObservableQuery\n        // happen in very fast succession.\n        return () => {\n          setTimeout(() => subscription.current.unsubscribe());\n        };\n      },\n\n      [\n        disableNetworkFetches,\n        isSyncSSR,\n        observable,\n        resultData,\n        partialRefetch,\n        client,\n      ]\n    ),\n    () =>\n      currentResultOverride ||\n      getCurrentResult(\n        resultData,\n        observable,\n        callbackRef.current,\n        partialRefetch,\n        client\n      ),\n    () =>\n      currentResultOverride ||\n      getCurrentResult(\n        resultData,\n        observable,\n        callbackRef.current,\n        partialRefetch,\n        client\n      )\n  );\n}\n\nfunction useRegisterSSRObservable(\n  observable: ObsQueryWithMeta<any, any>,\n  renderPromises: RenderPromises | undefined,\n  ssrAllowed: boolean\n) {\n  if (renderPromises && ssrAllowed) {\n    renderPromises.registerSSRObservable(observable);\n\n    if (observable.getCurrentResult().loading) {\n      // TODO: This is a legacy API which could probably be cleaned up\n      renderPromises.addObservableQueryPromise(observable);\n    }\n  }\n}\n\n// this hook is not compatible with any rules of React, and there's no good way to rewrite it.\n// it should stay a separate hook that will not be optimized by the compiler\nfunction useResubscribeIfNecessary<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  /** this hook will mutate properties on `resultData` */\n  resultData: InternalResult<TData, TVariables>,\n  /** this hook will mutate properties on `observable` */\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  client: ApolloClient<object>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,\n  watchQueryOptions: Readonly<WatchQueryOptions<TVariables, TData>>\n) {\n  if (\n    observable[lastWatchOptions] &&\n    !equal(observable[lastWatchOptions], watchQueryOptions)\n  ) {\n    // Though it might be tempting to postpone this reobserve call to the\n    // useEffect block, we need getCurrentResult to return an appropriate\n    // loading:true result synchronously (later within the same call to\n    // useQuery). Since we already have this.observable here (not true for\n    // the very first call to useQuery), we are not initiating any new\n    // subscriptions, though it does feel less than ideal that reobserve\n    // (potentially) kicks off a network request (for example, when the\n    // variables have changed), which is technically a side-effect.\n    observable.reobserve(\n      getObsQueryOptions(observable, client, options, watchQueryOptions)\n    );\n\n    // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n    // but save the current data as this.previousData, just like setResult\n    // usually does.\n    resultData.previousData =\n      resultData.current?.data || resultData.previousData;\n    resultData.current = void 0;\n  }\n  observable[lastWatchOptions] = watchQueryOptions;\n}\n\n/*\n * A function to massage options before passing them to ObservableQuery.\n * This is two-step curried because we want to reuse the `make` function,\n * but the `observable` might differ between calls to `make`.\n */\nexport function createMakeWatchQueryOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  client: ApolloClient<object>,\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  {\n    skip,\n    ssr,\n    onCompleted,\n    onError,\n    defaultOptions,\n    // The above options are useQuery-specific, so this ...otherOptions spread\n    // makes otherOptions almost a WatchQueryOptions object, except for the\n    // query property that we add below.\n    ...otherOptions\n  }: QueryHookOptions<TData, TVariables> = {},\n  isSyncSSR: boolean\n) {\n  return (\n    observable?: ObservableQuery<TData, TVariables>\n  ): WatchQueryOptions<TVariables, TData> => {\n    // This Object.assign is safe because otherOptions is a fresh ...rest object\n    // that did not exist until just now, so modifications are still allowed.\n    const watchQueryOptions: WatchQueryOptions<TVariables, TData> =\n      Object.assign(otherOptions, { query });\n\n    if (\n      isSyncSSR &&\n      (watchQueryOptions.fetchPolicy === \"network-only\" ||\n        watchQueryOptions.fetchPolicy === \"cache-and-network\")\n    ) {\n      // this behavior was added to react-apollo without explanation in this PR\n      // https://github.com/apollographql/react-apollo/pull/1579\n      watchQueryOptions.fetchPolicy = \"cache-first\";\n    }\n\n    if (!watchQueryOptions.variables) {\n      watchQueryOptions.variables = {} as TVariables;\n    }\n\n    if (skip) {\n      // When skipping, we set watchQueryOptions.fetchPolicy initially to\n      // \"standby\", but we also need/want to preserve the initial non-standby\n      // fetchPolicy that would have been used if not skipping.\n      watchQueryOptions.initialFetchPolicy =\n        watchQueryOptions.initialFetchPolicy ||\n        watchQueryOptions.fetchPolicy ||\n        getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n      watchQueryOptions.fetchPolicy = \"standby\";\n    } else if (!watchQueryOptions.fetchPolicy) {\n      watchQueryOptions.fetchPolicy =\n        observable?.options.initialFetchPolicy ||\n        getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n    }\n\n    return watchQueryOptions;\n  };\n}\n\nexport function getObsQueryOptions<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  observable: ObservableQuery<TData, TVariables> | undefined,\n  client: ApolloClient<object>,\n  queryHookOptions: QueryHookOptions<TData, TVariables>,\n  watchQueryOptions: Partial<WatchQueryOptions<TVariables, TData>>\n): WatchQueryOptions<TVariables, TData> {\n  const toMerge: Array<Partial<WatchQueryOptions<TVariables, TData>>> = [];\n\n  const globalDefaults = client.defaultOptions.watchQuery;\n  if (globalDefaults) toMerge.push(globalDefaults);\n\n  if (queryHookOptions.defaultOptions) {\n    toMerge.push(queryHookOptions.defaultOptions);\n  }\n\n  // We use compact rather than mergeOptions for this part of the merge,\n  // because we want watchQueryOptions.variables (if defined) to replace\n  // this.observable.options.variables whole. This replacement allows\n  // removing variables by removing them from the variables input to\n  // useQuery. If the variables were always merged together (rather than\n  // replaced), there would be no way to remove existing variables.\n  // However, the variables from options.defaultOptions and globalDefaults\n  // (if provided) should be merged, to ensure individual defaulted\n  // variables always have values, if not otherwise defined in\n  // observable.options or watchQueryOptions.\n  toMerge.push(compact(observable && observable.options, watchQueryOptions));\n\n  return toMerge.reduce(mergeOptions) as WatchQueryOptions<TVariables, TData>;\n}\n\nfunction setResult<TData, TVariables extends OperationVariables>(\n  nextResult: ApolloQueryResult<MaybeMasked<TData>>,\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObservableQuery<TData, TVariables>,\n  client: ApolloClient<object>,\n  partialRefetch: boolean | undefined,\n  forceUpdate: () => void,\n  callbacks: Callbacks<TData>\n) {\n  const previousResult = resultData.current;\n  if (previousResult && previousResult.data) {\n    resultData.previousData = previousResult.data;\n  }\n\n  if (!nextResult.error && isNonEmptyArray(nextResult.errors)) {\n    // Until a set naming convention for networkError and graphQLErrors is\n    // decided upon, we map errors (graphQLErrors) to the error options.\n    // TODO: Is it possible for both result.error and result.errors to be\n    // defined here?\n    nextResult.error = new ApolloError({ graphQLErrors: nextResult.errors });\n  }\n\n  resultData.current = toQueryResult(\n    unsafeHandlePartialRefetch(nextResult, observable, partialRefetch),\n    resultData.previousData,\n    observable,\n    client\n  );\n  // Calling state.setResult always triggers an update, though some call sites\n  // perform additional equality checks before committing to an update.\n  forceUpdate();\n  handleErrorOrCompleted(nextResult, previousResult?.networkStatus, callbacks);\n}\n\nfunction handleErrorOrCompleted<TData>(\n  result: ApolloQueryResult<MaybeMasked<TData>>,\n  previousNetworkStatus: NetworkStatus | undefined,\n  callbacks: Callbacks<TData>\n) {\n  if (!result.loading) {\n    const error = toApolloError(result);\n\n    // wait a tick in case we are in the middle of rendering a component\n    Promise.resolve()\n      .then(() => {\n        if (error) {\n          callbacks.onError(error);\n        } else if (\n          result.data &&\n          previousNetworkStatus !== result.networkStatus &&\n          result.networkStatus === NetworkStatus.ready\n        ) {\n          callbacks.onCompleted(result.data);\n        }\n      })\n      .catch((error) => {\n        invariant.warn(error);\n      });\n  }\n}\n\nfunction getCurrentResult<TData, TVariables extends OperationVariables>(\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObservableQuery<TData, TVariables>,\n  callbacks: Callbacks<TData>,\n  partialRefetch: boolean | undefined,\n  client: ApolloClient<object>\n): InternalQueryResult<TData, TVariables> {\n  // Using this.result as a cache ensures getCurrentResult continues returning\n  // the same (===) result object, unless state.setResult has been called, or\n  // we're doing server rendering and therefore override the result below.\n  if (!resultData.current) {\n    // WARNING: SIDE-EFFECTS IN THE RENDER FUNCTION\n    // this could call unsafeHandlePartialRefetch\n    setResult(\n      observable.getCurrentResult(),\n      resultData,\n      observable,\n      client,\n      partialRefetch,\n      () => {},\n      callbacks\n    );\n  }\n  return resultData.current!;\n}\n\nexport function getDefaultFetchPolicy<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  queryHookDefaultOptions?: Partial<WatchQueryOptions<TVariables, TData>>,\n  clientDefaultOptions?: DefaultOptions\n): WatchQueryFetchPolicy {\n  return (\n    queryHookDefaultOptions?.fetchPolicy ||\n    clientDefaultOptions?.watchQuery?.fetchPolicy ||\n    \"cache-first\"\n  );\n}\n\nexport function toApolloError<TData>(\n  result: Pick<ApolloQueryResult<TData>, \"errors\" | \"error\">\n): ApolloError | undefined {\n  return isNonEmptyArray(result.errors) ?\n      new ApolloError({ graphQLErrors: result.errors })\n    : result.error;\n}\n\nexport function toQueryResult<TData, TVariables extends OperationVariables>(\n  result: ApolloQueryResult<MaybeMasked<TData>>,\n  previousData: MaybeMasked<TData> | undefined,\n  observable: ObservableQuery<TData, TVariables>,\n  client: ApolloClient<object>\n): InternalQueryResult<TData, TVariables> {\n  const { data, partial, ...resultWithoutPartial } = result;\n  const queryResult: InternalQueryResult<TData, TVariables> = {\n    data, // Ensure always defined, even if result.data is missing.\n    ...resultWithoutPartial,\n    client: client,\n    observable: observable,\n    variables: observable.variables,\n    called: result !== ssrDisabledResult && result !== skipStandbyResult,\n    previousData,\n  };\n  return queryResult;\n}\n\nfunction unsafeHandlePartialRefetch<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  result: ApolloQueryResult<MaybeMasked<TData>>,\n  observable: ObservableQuery<TData, TVariables>,\n  partialRefetch: boolean | undefined\n): ApolloQueryResult<MaybeMasked<TData>> {\n  // TODO: This code should be removed when the partialRefetch option is\n  // removed. I was unable to get this hook to behave reasonably in certain\n  // edge cases when this block was put in an effect.\n  if (\n    result.partial &&\n    partialRefetch &&\n    !result.loading &&\n    (!result.data || Object.keys(result.data).length === 0) &&\n    observable.options.fetchPolicy !== \"cache-only\"\n  ) {\n    observable.refetch();\n    return {\n      ...result,\n      loading: true,\n      networkStatus: NetworkStatus.refetch,\n    };\n  }\n  return result;\n}\n\nconst ssrDisabledResult = maybeDeepFreeze({\n  loading: true,\n  data: void 0 as any,\n  error: void 0,\n  networkStatus: NetworkStatus.loading,\n});\n\nconst skipStandbyResult = maybeDeepFreeze({\n  loading: false,\n  data: void 0 as any,\n  error: void 0,\n  networkStatus: NetworkStatus.ready,\n});\n\nfunction bindObservableMethods<TData, TVariables extends OperationVariables>(\n  observable: ObservableQuery<TData, TVariables>\n) {\n  return {\n    refetch: observable.refetch.bind(observable),\n    reobserve: observable.reobserve.bind(observable),\n    fetchMore: observable.fetchMore.bind(observable),\n    updateQuery: observable.updateQuery.bind(observable),\n    startPolling: observable.startPolling.bind(observable),\n    stopPolling: observable.stopPolling.bind(observable),\n    subscribeToMore: observable.subscribeToMore.bind(observable),\n  };\n}\n"]}