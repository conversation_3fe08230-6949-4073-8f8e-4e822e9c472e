{"version": 3, "file": "SuspenseCache.js", "sourceRoot": "", "sources": ["../../../../src/react/internal/cache/SuspenseCache.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAEjC,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAiB7D;IAME,uBAAY,OAAmD;QAAnD,wBAAA,EAAA,UAAgC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QALvD,cAAS,GAAG,IAAI,IAAI,CAC1B,aAAa,CACd,CAAC;QAIA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,mCAAW,GAAX,UACE,QAAkB,EAClB,gBAA8C;QAE9C,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAE9C,CAAC;QAEF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjB,GAAG,CAAC,OAAO,GAAG,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,EAAE;gBAC3D,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB;gBACvD,SAAS,EAAE;oBACT,OAAO,GAAG,CAAC,OAAO,CAAC;gBACrB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,GAAG,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,2BAAG,GAAH,UAAI,QAAkB,EAAE,QAAyC;QAC/D,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACjD,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC;IACzB,CAAC;IACH,oBAAC;AAAD,CAAC,AAlCD,IAkCC", "sourcesContent": ["import { <PERSON>e } from \"@wry/trie\";\nimport type { ObservableQuery } from \"../../../core/index.js\";\nimport { canUseWeakMap } from \"../../../utilities/index.js\";\nimport { InternalQueryReference } from \"./QueryReference.js\";\nimport type { <PERSON>acheKey } from \"./types.js\";\n\nexport interface SuspenseCacheOptions {\n  /**\n   * Specifies the amount of time, in milliseconds, the suspense cache will wait\n   * for a suspended component to read from the suspense cache before it\n   * automatically disposes of the query. This prevents memory leaks when a\n   * component unmounts before a suspended resource finishes loading. Increase\n   * the timeout if your queries take longer than than the specified time to\n   * prevent your queries from suspending over and over.\n   *\n   * Defaults to 30 seconds.\n   */\n  autoDisposeTimeoutMs?: number;\n}\n\nexport class SuspenseCache {\n  private queryRefs = new Trie<{ current?: InternalQueryReference }>(\n    canUseWeakMap\n  );\n  private options: SuspenseCacheOptions;\n\n  constructor(options: SuspenseCacheOptions = Object.create(null)) {\n    this.options = options;\n  }\n\n  getQueryRef<TData = any>(\n    cacheKey: <PERSON>ache<PERSON><PERSON>,\n    createObservable: () => ObservableQuery<TData>\n  ) {\n    const ref = this.queryRefs.lookupArray(cacheKey) as {\n      current?: InternalQueryReference<TData>;\n    };\n\n    if (!ref.current) {\n      ref.current = new InternalQueryReference(createObservable(), {\n        autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,\n        onDispose: () => {\n          delete ref.current;\n        },\n      });\n    }\n\n    return ref.current;\n  }\n\n  add(cacheKey: CacheKey, queryRef: InternalQueryReference<unknown>) {\n    const ref = this.queryRefs.lookupArray(cacheKey);\n    ref.current = queryRef;\n  }\n}\n"]}