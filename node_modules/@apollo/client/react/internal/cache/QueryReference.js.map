{"version": 3, "file": "QueryReference.js", "sourceRoot": "", "sources": ["../../../../src/react/internal/cache/QueryReference.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAYtC,OAAO,EACL,sBAAsB,EACtB,qBAAqB,GACtB,MAAM,6BAA6B,CAAC;AAErC,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,iDAAiD,CAAC;AAa5E,IAAM,sBAAsB,GAAkB,MAAM,EAAE,CAAC;AACvD,IAAM,cAAc,GAAkB,MAAM,EAAE,CAAC;AA2F/C,MAAM,UAAU,YAAY,CAC1B,gBAA+C;;IAE/C,IAAM,GAAG;YACP,SAAS;gBACP,yEAAyE;gBACzE,wEAAwE;gBACxE,sEAAsE;gBACtE,+DAA+D;gBAC/D,EAAE;gBACF,yEAAyE;gBACzE,kEAAkE;gBAClE,EAAE;gBACF,sBAAsB;gBACtB,+DAA+D;gBAC/D,IAAI;gBACJ,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,GAAG,EAAH,CAAG,CAAC,CAAC;YAChD,CAAC;;QACD,GAAC,sBAAsB,IAAG,gBAAgB;QAC1C,GAAC,cAAc,IAAG,gBAAgB,CAAC,OAAO;WAC3C,CAAC;IAEF,OAAO,GAAG,CAAC;AACb,CAAC;AAQD,MAAM,UAAU,qBAAqB,CACnC,QAAwD;IAExD,SAAS,CACP,CAAC,QAAQ,IAAI,sBAAsB,IAAI,QAAQ,EAC/C,6DAA6D,CAC9D,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,QAAqC;IAErC,IAAM,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAElD,OAAO,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;QACpD,gBAAgB,CAAC,OAAO;QAC1B,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AAC/B,CAAC;AAQD,MAAM,UAAU,cAAc,CAC5B,QAAyC;IAEzC,OAAO,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,QAAgC,EAChC,OAA+B;IAE/B,QAAQ,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;AACrC,CAAC;AAED,IAAM,wBAAwB,GAAG;IAC/B,iBAAiB;IACjB,SAAS;IACT,aAAa;IACb,aAAa;IACb,oBAAoB;IACpB,mBAAmB;CACX,CAAC;AAOX;IAmBE,gCACE,UAAuC,EACvC,OAAsC;QAFxC,iBAiCC;QAlDe,QAAG,GAAa,EAAE,CAAC;QAM3B,cAAS,GAAG,IAAI,GAAG,EAAmB,CAAC;QAQvC,eAAU,GAAG,CAAC,CAAC;QACf,mBAAc,GAAG,CAAC,CAAC;QAMzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,oEAAoE;QACpE,wEAAwE;QACxE,uEAAuE;QACvE,8BAA8B;QAC9B,IAAM,iBAAiB,GAAG;;YACxB,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,KAAI,CAAC,oBAAoB,GAAG,UAAU,CACpC,KAAI,CAAC,OAAO,EACZ,MAAA,OAAO,CAAC,oBAAoB,mCAAI,KAAM,CACvC,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QAEF,0EAA0E;QAC1E,sEAAsE;QACtE,2EAA2E;QAC3E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IAC1D,CAAC;IAED,sBAAI,4CAAQ;aAAZ;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;QAClC,CAAC;;;OAAA;IAED,sBAAI,qDAAiB;aAArB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACjC,CAAC;;;OAAA;IAED,6CAAY,GAAZ;QACU,IAAA,UAAU,GAAK,IAAI,WAAT,CAAU;QAE5B,IAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;QAC/D,IAAM,oBAAoB,GACxB,mBAAmB,KAAK,UAAU,IAAI,mBAAmB,KAAK,SAAS,CAAC;QAE1E,IAAI,CAAC;YACH,IAAI,oBAAoB,EAAE,CAAC;gBACzB,UAAU,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,UAAU,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,IAAI,oBAAoB,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,UAAU,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,uCAAM,GAAN;QAAA,iBAmBC;QAlBC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACxC,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,OAAO;YACL,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,QAAQ,GAAG,IAAI,CAAC;YAChB,KAAI,CAAC,UAAU,EAAE,CAAC;YAElB,UAAU,CAAC;gBACT,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE,CAAC;oBACrB,KAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAED,2CAAU,GAAV;QAAA,iBAoBC;QAnBC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,OAAO;YACL,6DAA6D;YAC7D,gEAAgE;YAChE,uEAAuE;YACvE,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,QAAQ,GAAG,IAAI,CAAC;YAChB,KAAI,CAAC,cAAc,EAAE,CAAC;YACtB,UAAU,CAAC;gBACT,IAAI,CAAC,KAAI,CAAC,cAAc,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE,CAAC;oBAC7C,KAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAED,iDAAgB,GAAhB,UAAiB,iBAAkC;QAAnD,iBAMC;QALC,OAAO,wBAAwB,CAAC,IAAI,CAClC,UAAC,MAAM;YACL,OAAA,MAAM,IAAI,iBAAiB;gBAC3B,CAAC,KAAK,CAAC,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;QADjE,CACiE,CACpE,CAAC;IACJ,CAAC;IAED,6CAAY,GAAZ,UAAa,iBAAkC;QACvC,IAAA,KAGF,IAAI,CAAC,iBAAiB,EAFX,kBAAkB,iBAAA,EACd,sBAAsB,qBACf,CAAC;QAE3B,oEAAoE;QACpE,2EAA2E;QAC3E,IACE,kBAAkB,KAAK,SAAS;YAChC,kBAAkB,KAAK,iBAAiB,CAAC,WAAW,EACpD,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YAEpD,IAAI,sBAAsB,KAAK,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACjE,IAAI,CAAC,MAAM,yBAAQ,IAAI,CAAC,MAAM,GAAK,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAE,CAAC;gBACxE,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,uCAAM,GAAN,UAAO,QAAyB;QAAhC,iBAMC;QALC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7B,OAAO;YACL,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC;IAED,wCAAO,GAAP,UAAQ,SAAyC;QAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,0CAAS,GAAT,UAAU,OAAgC;QACxC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAQ,OAAO,CAAC,CAAC,CAAC;IACvE,CAAC;IAEO,wCAAO,GAAf;QACE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAChC,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEO,0CAAS,GAAjB;QACE,+BAA+B;IACjC,CAAC;IAEO,2CAAU,GAAlB,UAAmB,MAA6C;;QAC9D,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC5B,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,wEAAwE;gBACxE,YAAY;gBACZ,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC3B,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjC,CAAC;gBACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;gBACrB,MAAA,IAAI,CAAC,OAAO,qDAAG,MAAM,CAAC,CAAC;gBACvB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,wEAAwE;gBACxE,iEAAiE;gBACjE,uDAAuD;gBACvD,IACE,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI;oBAChC,MAAM,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,aAAa,EAClD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,wEAAwE;gBACxE,YAAY;gBACZ,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC3B,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjC,CAAC;gBAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;gBACrB,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;gBAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3B,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAEO,4CAAW,GAAnB,UAAoB,KAAkB;;QACpC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CACvD,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,CACjB,CAAC;QAEF,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC5B,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,MAAA,IAAI,CAAC,MAAM,qDAAG,KAAK,CAAC,CAAC;gBACrB,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,wCAAO,GAAf,UAAgB,OAA+B;QAC7C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,OAAO,CAAC,EAAjB,CAAiB,CAAC,CAAC;IAC1D,CAAC;IAEO,8CAAa,GAArB,UACE,eAA+D;QADjE,iBAyCC;QAtCC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;QAE7B,0EAA0E;QAC1E,0EAA0E;QAC1E,2EAA2E;QAC3E,uEAAuE;QACvE,iCAAiC;QACjC,eAAe;aACZ,IAAI,CAAC;YACJ,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,iEAAiE;YACjE,iEAAiE;YACjE,mEAAmE;YACnE,mEAAmE;YACnE,sBAAsB;YACtB,sEAAsE;YACtE,mBAAmB;YACnB,UAAU,CAAC;;gBACT,IAAI,KAAI,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACtC,kEAAkE;oBAClE,oEAAoE;oBACpE,kEAAkE;oBAClE,8DAA8D;oBAC9D,+BAA+B;oBAC/B,EAAE;oBACF,0CAA0C;oBAC1C,8DAA8D;oBAC9D,KAAI,CAAC,MAAM,GAAG,KAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;oBACjD,MAAA,KAAI,CAAC,OAAO,sDAAG,KAAI,CAAC,MAAM,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,KAAK,CAAC,UAAC,KAAK,YAAK,OAAA,MAAA,KAAI,CAAC,MAAM,sDAAG,KAAK,CAAC,CAAA,EAAA,CAAC,CAAC;QAE1C,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,iDAAgB,GAAxB;QAAA,iBAMC;QALC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU;aAChC,MAAM,CACL,UAAC,MAAM,IAAK,OAAA,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAI,CAAC,MAAM,CAAC,EAAtD,CAAsD,CACnE;aACA,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAEO,0CAAS,GAAjB;QACE,2EAA2E;QAC3E,yBAAyB;QACzB,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO;YACV,CACE,MAAM,CAAC,IAAI;gBACX,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAC9D,CAAC,CAAC;gBACD,sBAAsB,CAAC,MAAM,CAAC;gBAChC,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAClC,CAAC;IAEO,qDAAoB,GAA5B;QAAA,iBAOC;QANC,OAAO,oBAAoB,CACzB,IAAI,OAAO,CAAwC,UAAC,OAAO,EAAE,MAAM;YACjE,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IACH,6BAAC;AAAD,CAAC,AAzUD,IAyUC", "sourcesContent": ["import { equal } from \"@wry/equality\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON>,\n  ApolloQueryResult,\n  ObservableQuery,\n  OperationVariables,\n  WatchQueryOptions,\n} from \"../../../core/index.js\";\nimport type {\n  ObservableSubscription,\n  PromiseWithState,\n} from \"../../../utilities/index.js\";\nimport {\n  createFulfilledPromise,\n  createRejectedPromise,\n} from \"../../../utilities/index.js\";\nimport type { QueryKey } from \"./types.js\";\nimport { wrapPromiseWithState } from \"../../../utilities/index.js\";\nimport { invariant } from \"../../../utilities/globals/invariantWrappers.js\";\nimport type { MaybeMasked } from \"../../../masking/index.js\";\n\ntype QueryRefPromise<TData> = PromiseWithState<\n  ApolloQueryResult<MaybeMasked<TData>>\n>;\n\ntype Listener<TData> = (promise: QueryRefPromise<TData>) => void;\n\ntype FetchMoreOptions<TData> = Parameters<\n  ObservableQuery<TData>[\"fetchMore\"]\n>[0];\n\nconst QUERY_REFERENCE_SYMBOL: unique symbol = Symbol();\nconst PROMISE_SYMBOL: unique symbol = Symbol();\ndeclare const QUERY_REF_BRAND: unique symbol;\n/**\n * A `QueryReference` is an opaque object returned by `useBackgroundQuery`.\n * A child component reading the `QueryReference` via `useReadQuery` will\n * suspend until the promise resolves.\n */\nexport interface QueryRef<TData = unknown, TVariables = unknown> {\n  /** @internal */\n  [QUERY_REF_BRAND]?(variables: TVariables): TData;\n}\n\n/**\n * @internal\n * For usage in internal helpers only.\n */\ninterface WrappedQueryRef<TData = unknown, TVariables = unknown>\n  extends QueryRef<TData, TVariables> {\n  /** @internal */\n  readonly [QUERY_REFERENCE_SYMBOL]: InternalQueryReference<TData>;\n  /** @internal */\n  [PROMISE_SYMBOL]: QueryRefPromise<TData>;\n  /** @internal */\n  toPromise?(): Promise<unknown>;\n}\n\n/**\n * @deprecated Please use the `QueryRef` interface instead of `QueryReference`.\n *\n * {@inheritDoc @apollo/client!QueryRef:interface}\n */\nexport interface QueryReference<TData = unknown, TVariables = unknown>\n  extends QueryRef<TData, TVariables> {\n  /**\n   * @deprecated Please use the `QueryRef` interface instead of `QueryReference`.\n   *\n   * {@inheritDoc @apollo/client!PreloadedQueryRef#toPromise:member(1)}\n   */\n  toPromise?: unknown;\n}\n\n/**\n * {@inheritDoc @apollo/client!QueryRef:interface}\n */\nexport interface PreloadedQueryRef<TData = unknown, TVariables = unknown>\n  extends QueryRef<TData, TVariables> {\n  /**\n   * A function that returns a promise that resolves when the query has finished\n   * loading. The promise resolves with the `QueryReference` itself.\n   *\n   * @remarks\n   * This method is useful for preloading queries in data loading routers, such\n   * as [React Router](https://reactrouter.com/en/main) or [TanStack Router](https://tanstack.com/router),\n   * to prevent routes from transitioning until the query has finished loading.\n   * `data` is not exposed on the promise to discourage using the data in\n   * `loader` functions and exposing it to your route components. Instead, we\n   * prefer you rely on `useReadQuery` to access the data to ensure your\n   * component can rerender with cache updates. If you need to access raw query\n   * data, use `client.query()` directly.\n   *\n   * @example\n   * Here's an example using React Router's `loader` function:\n   * ```ts\n   * import { createQueryPreloader } from \"@apollo/client\";\n   *\n   * const preloadQuery = createQueryPreloader(client);\n   *\n   * export async function loader() {\n   *   const queryRef = preloadQuery(GET_DOGS_QUERY);\n   *\n   *   return queryRef.toPromise();\n   * }\n   *\n   * export function RouteComponent() {\n   *   const queryRef = useLoaderData();\n   *   const { data } = useReadQuery(queryRef);\n   *\n   *   // ...\n   * }\n   * ```\n   *\n   * @since 3.9.0\n   */\n  toPromise(): Promise<PreloadedQueryRef<TData, TVariables>>;\n}\n\ninterface InternalQueryReferenceOptions {\n  onDispose?: () => void;\n  autoDisposeTimeoutMs?: number;\n}\n\nexport function wrapQueryRef<TData, TVariables extends OperationVariables>(\n  internalQueryRef: InternalQueryReference<TData>\n) {\n  const ref: WrappedQueryRef<TData, TVariables> = {\n    toPromise() {\n      // We avoid resolving this promise with the query data because we want to\n      // discourage using the server data directly from the queryRef. Instead,\n      // the data should be accessed through `useReadQuery`. When the server\n      // data is needed, its better to use `client.query()` directly.\n      //\n      // Here we resolve with the ref itself to make using this in React Router\n      // or TanStack Router `loader` functions a bit more ergonomic e.g.\n      //\n      // function loader() {\n      //   return { queryRef: await preloadQuery(query).toPromise() }\n      // }\n      return getWrappedPromise(ref).then(() => ref);\n    },\n    [QUERY_REFERENCE_SYMBOL]: internalQueryRef,\n    [PROMISE_SYMBOL]: internalQueryRef.promise,\n  };\n\n  return ref;\n}\n\nexport function assertWrappedQueryRef<TData, TVariables>(\n  queryRef: QueryRef<TData, TVariables>\n): asserts queryRef is WrappedQueryRef<TData, TVariables>;\nexport function assertWrappedQueryRef<TData, TVariables>(\n  queryRef: QueryRef<TData, TVariables> | undefined | null\n): asserts queryRef is WrappedQueryRef<TData, TVariables> | undefined | null;\nexport function assertWrappedQueryRef<TData, TVariables>(\n  queryRef: QueryRef<TData, TVariables> | undefined | null\n) {\n  invariant(\n    !queryRef || QUERY_REFERENCE_SYMBOL in queryRef,\n    \"Expected a QueryRef object, but got something else instead.\"\n  );\n}\n\nexport function getWrappedPromise<TData>(\n  queryRef: WrappedQueryRef<TData, any>\n) {\n  const internalQueryRef = unwrapQueryRef(queryRef);\n\n  return internalQueryRef.promise.status === \"fulfilled\" ?\n      internalQueryRef.promise\n    : queryRef[PROMISE_SYMBOL];\n}\n\nexport function unwrapQueryRef<TData>(\n  queryRef: WrappedQueryRef<TData>\n): InternalQueryReference<TData>;\nexport function unwrapQueryRef<TData>(\n  queryRef: Partial<WrappedQueryRef<TData>>\n): undefined | InternalQueryReference<TData>;\nexport function unwrapQueryRef<TData>(\n  queryRef: Partial<WrappedQueryRef<TData>>\n) {\n  return queryRef[QUERY_REFERENCE_SYMBOL];\n}\n\nexport function updateWrappedQueryRef<TData>(\n  queryRef: WrappedQueryRef<TData>,\n  promise: QueryRefPromise<TData>\n) {\n  queryRef[PROMISE_SYMBOL] = promise;\n}\n\nconst OBSERVED_CHANGED_OPTIONS = [\n  \"canonizeResults\",\n  \"context\",\n  \"errorPolicy\",\n  \"fetchPolicy\",\n  \"refetchWritePolicy\",\n  \"returnPartialData\",\n] as const;\n\ntype ObservedOptions = Pick<\n  WatchQueryOptions,\n  (typeof OBSERVED_CHANGED_OPTIONS)[number]\n>;\n\nexport class InternalQueryReference<TData = unknown> {\n  public result!: ApolloQueryResult<MaybeMasked<TData>>;\n  public readonly key: QueryKey = {};\n  public readonly observable: ObservableQuery<TData>;\n\n  public promise!: QueryRefPromise<TData>;\n\n  private subscription!: ObservableSubscription;\n  private listeners = new Set<Listener<TData>>();\n  private autoDisposeTimeoutId?: NodeJS.Timeout;\n\n  private resolve:\n    | ((result: ApolloQueryResult<MaybeMasked<TData>>) => void)\n    | undefined;\n  private reject: ((error: unknown) => void) | undefined;\n\n  private references = 0;\n  private softReferences = 0;\n\n  constructor(\n    observable: ObservableQuery<TData, any>,\n    options: InternalQueryReferenceOptions\n  ) {\n    this.handleNext = this.handleNext.bind(this);\n    this.handleError = this.handleError.bind(this);\n    this.dispose = this.dispose.bind(this);\n    this.observable = observable;\n\n    if (options.onDispose) {\n      this.onDispose = options.onDispose;\n    }\n\n    this.setResult();\n    this.subscribeToQuery();\n\n    // Start a timer that will automatically dispose of the query if the\n    // suspended resource does not use this queryRef in the given time. This\n    // helps prevent memory leaks when a component has unmounted before the\n    // query has finished loading.\n    const startDisposeTimer = () => {\n      if (!this.references) {\n        this.autoDisposeTimeoutId = setTimeout(\n          this.dispose,\n          options.autoDisposeTimeoutMs ?? 30_000\n        );\n      }\n    };\n\n    // We wait until the request has settled to ensure we don't dispose of the\n    // query ref before the request finishes, otherwise we would leave the\n    // promise in a pending state rendering the suspense boundary indefinitely.\n    this.promise.then(startDisposeTimer, startDisposeTimer);\n  }\n\n  get disposed() {\n    return this.subscription.closed;\n  }\n\n  get watchQueryOptions() {\n    return this.observable.options;\n  }\n\n  reinitialize() {\n    const { observable } = this;\n\n    const originalFetchPolicy = this.watchQueryOptions.fetchPolicy;\n    const avoidNetworkRequests =\n      originalFetchPolicy === \"no-cache\" || originalFetchPolicy === \"standby\";\n\n    try {\n      if (avoidNetworkRequests) {\n        observable.silentSetOptions({ fetchPolicy: \"standby\" });\n      } else {\n        observable.resetLastResults();\n        observable.silentSetOptions({ fetchPolicy: \"cache-first\" });\n      }\n\n      this.subscribeToQuery();\n\n      if (avoidNetworkRequests) {\n        return;\n      }\n\n      observable.resetDiff();\n      this.setResult();\n    } finally {\n      observable.silentSetOptions({ fetchPolicy: originalFetchPolicy });\n    }\n  }\n\n  retain() {\n    this.references++;\n    clearTimeout(this.autoDisposeTimeoutId);\n    let disposed = false;\n\n    return () => {\n      if (disposed) {\n        return;\n      }\n\n      disposed = true;\n      this.references--;\n\n      setTimeout(() => {\n        if (!this.references) {\n          this.dispose();\n        }\n      });\n    };\n  }\n\n  softRetain() {\n    this.softReferences++;\n    let disposed = false;\n\n    return () => {\n      // Tracking if this has already been called helps ensure that\n      // multiple calls to this function won't decrement the reference\n      // counter more than it should. Subsequent calls just result in a noop.\n      if (disposed) {\n        return;\n      }\n\n      disposed = true;\n      this.softReferences--;\n      setTimeout(() => {\n        if (!this.softReferences && !this.references) {\n          this.dispose();\n        }\n      });\n    };\n  }\n\n  didChangeOptions(watchQueryOptions: ObservedOptions) {\n    return OBSERVED_CHANGED_OPTIONS.some(\n      (option) =>\n        option in watchQueryOptions &&\n        !equal(this.watchQueryOptions[option], watchQueryOptions[option])\n    );\n  }\n\n  applyOptions(watchQueryOptions: ObservedOptions) {\n    const {\n      fetchPolicy: currentFetchPolicy,\n      canonizeResults: currentCanonizeResults,\n    } = this.watchQueryOptions;\n\n    // \"standby\" is used when `skip` is set to `true`. Detect when we've\n    // enabled the query (i.e. `skip` is `false`) to execute a network request.\n    if (\n      currentFetchPolicy === \"standby\" &&\n      currentFetchPolicy !== watchQueryOptions.fetchPolicy\n    ) {\n      this.initiateFetch(this.observable.reobserve(watchQueryOptions));\n    } else {\n      this.observable.silentSetOptions(watchQueryOptions);\n\n      if (currentCanonizeResults !== watchQueryOptions.canonizeResults) {\n        this.result = { ...this.result, ...this.observable.getCurrentResult() };\n        this.promise = createFulfilledPromise(this.result);\n      }\n    }\n\n    return this.promise;\n  }\n\n  listen(listener: Listener<TData>) {\n    this.listeners.add(listener);\n\n    return () => {\n      this.listeners.delete(listener);\n    };\n  }\n\n  refetch(variables: OperationVariables | undefined) {\n    return this.initiateFetch(this.observable.refetch(variables));\n  }\n\n  fetchMore(options: FetchMoreOptions<TData>) {\n    return this.initiateFetch(this.observable.fetchMore<TData>(options));\n  }\n\n  private dispose() {\n    this.subscription.unsubscribe();\n    this.onDispose();\n  }\n\n  private onDispose() {\n    // noop. overridable by options\n  }\n\n  private handleNext(result: ApolloQueryResult<MaybeMasked<TData>>) {\n    switch (this.promise.status) {\n      case \"pending\": {\n        // Maintain the last successful `data` value if the next result does not\n        // have one.\n        if (result.data === void 0) {\n          result.data = this.result.data;\n        }\n        this.result = result;\n        this.resolve?.(result);\n        break;\n      }\n      default: {\n        // This occurs when switching to a result that is fully cached when this\n        // class is instantiated. ObservableQuery will run reobserve when\n        // subscribing, which delivers a result from the cache.\n        if (\n          result.data === this.result.data &&\n          result.networkStatus === this.result.networkStatus\n        ) {\n          return;\n        }\n\n        // Maintain the last successful `data` value if the next result does not\n        // have one.\n        if (result.data === void 0) {\n          result.data = this.result.data;\n        }\n\n        this.result = result;\n        this.promise = createFulfilledPromise(result);\n        this.deliver(this.promise);\n        break;\n      }\n    }\n  }\n\n  private handleError(error: ApolloError) {\n    this.subscription.unsubscribe();\n    this.subscription = this.observable.resubscribeAfterError(\n      this.handleNext,\n      this.handleError\n    );\n\n    switch (this.promise.status) {\n      case \"pending\": {\n        this.reject?.(error);\n        break;\n      }\n      default: {\n        this.promise = createRejectedPromise(error);\n        this.deliver(this.promise);\n      }\n    }\n  }\n\n  private deliver(promise: QueryRefPromise<TData>) {\n    this.listeners.forEach((listener) => listener(promise));\n  }\n\n  private initiateFetch(\n    returnedPromise: Promise<ApolloQueryResult<MaybeMasked<TData>>>\n  ) {\n    this.promise = this.createPendingPromise();\n    this.promise.catch(() => {});\n\n    // If the data returned from the fetch is deeply equal to the data already\n    // in the cache, `handleNext` will not be triggered leaving the promise we\n    // created in a pending state forever. To avoid this situtation, we attempt\n    // to resolve the promise if `handleNext` hasn't been run to ensure the\n    // promise is resolved correctly.\n    returnedPromise\n      .then(() => {\n        // In the case of `fetchMore`, this promise is resolved before a cache\n        // result is emitted due to the fact that `fetchMore` sets a `no-cache`\n        // fetch policy and runs `cache.batch` in its `.then` handler. Because\n        // the timing is different, we accidentally run this update twice\n        // causing an additional re-render with the `fetchMore` result by\n        // itself. By wrapping in `setTimeout`, this should provide a short\n        // delay to allow the `QueryInfo.notify` handler to run before this\n        // promise is checked.\n        // See https://github.com/apollographql/apollo-client/issues/11315 for\n        // more information\n        setTimeout(() => {\n          if (this.promise.status === \"pending\") {\n            // Use the current result from the observable instead of the value\n            // resolved from the promise. This avoids issues in some cases where\n            // the raw resolved value should not be the emitted value, such as\n            // when a `fetchMore` call returns an empty array after it has\n            // reached the end of the list.\n            //\n            // See the following for more information:\n            // https://github.com/apollographql/apollo-client/issues/11642\n            this.result = this.observable.getCurrentResult();\n            this.resolve?.(this.result);\n          }\n        });\n      })\n      .catch((error) => this.reject?.(error));\n\n    return returnedPromise;\n  }\n\n  private subscribeToQuery() {\n    this.subscription = this.observable\n      .filter(\n        (result) => !equal(result.data, {}) && !equal(result, this.result)\n      )\n      .subscribe(this.handleNext, this.handleError);\n  }\n\n  private setResult() {\n    // Don't save this result as last result to prevent delivery of last result\n    // when first subscribing\n    const result = this.observable.getCurrentResult(false);\n\n    if (equal(result, this.result)) {\n      return;\n    }\n\n    this.result = result;\n    this.promise =\n      (\n        result.data &&\n        (!result.partial || this.watchQueryOptions.returnPartialData)\n      ) ?\n        createFulfilledPromise(result)\n      : this.createPendingPromise();\n  }\n\n  private createPendingPromise() {\n    return wrapPromiseWithState(\n      new Promise<ApolloQueryResult<MaybeMasked<TData>>>((resolve, reject) => {\n        this.resolve = resolve;\n        this.reject = reject;\n      })\n    );\n  }\n}\n"]}