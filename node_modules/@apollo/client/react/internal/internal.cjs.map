{"version": 3, "file": "internal.cjs", "sources": ["../../version.js", "../../utilities/globals/maybe.js", "../../utilities/globals/global.js", "../../utilities/common/makeUniqueId.js", "../../utilities/common/stringifyForDisplay.js", "../../utilities/globals/invariantWrappers.js", "cache/QueryReference.js", "cache/SuspenseCache.js", "cache/getSuspenseCache.js"], "sourcesContent": ["export var version = \"3.12.8\";\n//# sourceMappingURL=version.js.map", "export function maybe(thunk) {\n    try {\n        return thunk();\n    }\n    catch (_a) { }\n}\n//# sourceMappingURL=maybe.js.map", "import { maybe } from \"./maybe.js\";\nexport default (maybe(function () { return globalThis; }) ||\n    maybe(function () { return window; }) ||\n    maybe(function () { return self; }) ||\n    maybe(function () { return global; }) || // We don't expect the Function constructor ever to be invoked at runtime, as\n// long as at least one of globalThis, window, self, or global is defined, so\n// we are under no obligation to make it easy for static analysis tools to\n// detect syntactic usage of the Function constructor. If you think you can\n// improve your static analysis to detect this obfuscation, think again. This\n// is an arms race you cannot win, at least not in JavaScript.\nmaybe(function () {\n    return maybe.constructor(\"return this\")();\n}));\n//# sourceMappingURL=global.js.map", "var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n    var count = prefixCounts.get(prefix) || 1;\n    prefixCounts.set(prefix, count + 1);\n    return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}\n//# sourceMappingURL=makeUniqueId.js.map", "import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n    if (space === void 0) { space = 0; }\n    var undefId = makeUniqueId(\"stringifyForDisplay\");\n    return JSON.stringify(value, function (key, value) {\n        return value === void 0 ? undefId : value;\n    }, space)\n        .split(JSON.stringify(undefId))\n        .join(\"<undefined>\");\n}\n//# sourceMappingURL=stringifyForDisplay.js.map", "import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\nfunction wrap(fn) {\n    return function (message) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (typeof message === \"number\") {\n            var arg0 = message;\n            message = getHandledErrorMsg(arg0);\n            if (!message) {\n                message = getFallbackErrorMsg(arg0, args);\n                args = [];\n            }\n        }\n        fn.apply(void 0, [message].concat(args));\n    };\n}\nvar invariant = Object.assign(function invariant(condition, message) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    if (!condition) {\n        originalInvariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));\n    }\n}, {\n    debug: wrap(originalInvariant.debug),\n    log: wrap(originalInvariant.log),\n    warn: wrap(originalInvariant.warn),\n    error: wrap(originalInvariant.error),\n});\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(message) {\n    var optionalParams = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        optionalParams[_i - 1] = arguments[_i];\n    }\n    return new InvariantError(getHandledErrorMsg(message, optionalParams) ||\n        getFallbackErrorMsg(message, optionalParams));\n}\nvar ApolloErrorMessageHandler = Symbol.for(\"ApolloErrorMessageHandler_\" + version);\nfunction stringify(arg) {\n    if (typeof arg == \"string\") {\n        return arg;\n    }\n    try {\n        return stringifyForDisplay(arg, 2).slice(0, 1000);\n    }\n    catch (_a) {\n        return \"<non-serializable>\";\n    }\n}\nfunction getHandledErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return (global[ApolloErrorMessageHandler] &&\n        global[ApolloErrorMessageHandler](message, messageArgs.map(stringify)));\n}\nfunction getFallbackErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return \"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#\".concat(encodeURIComponent(JSON.stringify({\n        version: version,\n        message: message,\n        args: messageArgs.map(stringify),\n    })));\n}\nexport { invariant, InvariantError, newInvariantError, ApolloErrorMessageHandler, };\n//# sourceMappingURL=invariantWrappers.js.map", "import { __assign } from \"tslib\";\nimport { equal } from \"@wry/equality\";\nimport { createFulfilledPromise, createRejectedPromise, } from \"../../../utilities/index.js\";\nimport { wrapPromiseWithState } from \"../../../utilities/index.js\";\nimport { invariant } from \"../../../utilities/globals/invariantWrappers.js\";\nvar QUERY_REFERENCE_SYMBOL = Symbol();\nvar PROMISE_SYMBOL = Symbol();\nexport function wrapQueryRef(internalQueryRef) {\n    var _a;\n    var ref = (_a = {\n            toPromise: function () {\n                // We avoid resolving this promise with the query data because we want to\n                // discourage using the server data directly from the queryRef. Instead,\n                // the data should be accessed through `useReadQuery`. When the server\n                // data is needed, its better to use `client.query()` directly.\n                //\n                // Here we resolve with the ref itself to make using this in React Router\n                // or TanStack Router `loader` functions a bit more ergonomic e.g.\n                //\n                // function loader() {\n                //   return { queryRef: await preloadQuery(query).toPromise() }\n                // }\n                return getWrappedPromise(ref).then(function () { return ref; });\n            }\n        },\n        _a[QUERY_REFERENCE_SYMBOL] = internalQueryRef,\n        _a[PROMISE_SYMBOL] = internalQueryRef.promise,\n        _a);\n    return ref;\n}\nexport function assertWrappedQueryRef(queryRef) {\n    invariant(!queryRef || QUERY_REFERENCE_SYMBOL in queryRef, 69);\n}\nexport function getWrappedPromise(queryRef) {\n    var internalQueryRef = unwrapQueryRef(queryRef);\n    return internalQueryRef.promise.status === \"fulfilled\" ?\n        internalQueryRef.promise\n        : queryRef[PROMISE_SYMBOL];\n}\nexport function unwrapQueryRef(queryRef) {\n    return queryRef[QUERY_REFERENCE_SYMBOL];\n}\nexport function updateWrappedQueryRef(queryRef, promise) {\n    queryRef[PROMISE_SYMBOL] = promise;\n}\nvar OBSERVED_CHANGED_OPTIONS = [\n    \"canonizeResults\",\n    \"context\",\n    \"errorPolicy\",\n    \"fetchPolicy\",\n    \"refetchWritePolicy\",\n    \"returnPartialData\",\n];\nvar InternalQueryReference = /** @class */ (function () {\n    function InternalQueryReference(observable, options) {\n        var _this = this;\n        this.key = {};\n        this.listeners = new Set();\n        this.references = 0;\n        this.softReferences = 0;\n        this.handleNext = this.handleNext.bind(this);\n        this.handleError = this.handleError.bind(this);\n        this.dispose = this.dispose.bind(this);\n        this.observable = observable;\n        if (options.onDispose) {\n            this.onDispose = options.onDispose;\n        }\n        this.setResult();\n        this.subscribeToQuery();\n        // Start a timer that will automatically dispose of the query if the\n        // suspended resource does not use this queryRef in the given time. This\n        // helps prevent memory leaks when a component has unmounted before the\n        // query has finished loading.\n        var startDisposeTimer = function () {\n            var _a;\n            if (!_this.references) {\n                _this.autoDisposeTimeoutId = setTimeout(_this.dispose, (_a = options.autoDisposeTimeoutMs) !== null && _a !== void 0 ? _a : 30000);\n            }\n        };\n        // We wait until the request has settled to ensure we don't dispose of the\n        // query ref before the request finishes, otherwise we would leave the\n        // promise in a pending state rendering the suspense boundary indefinitely.\n        this.promise.then(startDisposeTimer, startDisposeTimer);\n    }\n    Object.defineProperty(InternalQueryReference.prototype, \"disposed\", {\n        get: function () {\n            return this.subscription.closed;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(InternalQueryReference.prototype, \"watchQueryOptions\", {\n        get: function () {\n            return this.observable.options;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    InternalQueryReference.prototype.reinitialize = function () {\n        var observable = this.observable;\n        var originalFetchPolicy = this.watchQueryOptions.fetchPolicy;\n        var avoidNetworkRequests = originalFetchPolicy === \"no-cache\" || originalFetchPolicy === \"standby\";\n        try {\n            if (avoidNetworkRequests) {\n                observable.silentSetOptions({ fetchPolicy: \"standby\" });\n            }\n            else {\n                observable.resetLastResults();\n                observable.silentSetOptions({ fetchPolicy: \"cache-first\" });\n            }\n            this.subscribeToQuery();\n            if (avoidNetworkRequests) {\n                return;\n            }\n            observable.resetDiff();\n            this.setResult();\n        }\n        finally {\n            observable.silentSetOptions({ fetchPolicy: originalFetchPolicy });\n        }\n    };\n    InternalQueryReference.prototype.retain = function () {\n        var _this = this;\n        this.references++;\n        clearTimeout(this.autoDisposeTimeoutId);\n        var disposed = false;\n        return function () {\n            if (disposed) {\n                return;\n            }\n            disposed = true;\n            _this.references--;\n            setTimeout(function () {\n                if (!_this.references) {\n                    _this.dispose();\n                }\n            });\n        };\n    };\n    InternalQueryReference.prototype.softRetain = function () {\n        var _this = this;\n        this.softReferences++;\n        var disposed = false;\n        return function () {\n            // Tracking if this has already been called helps ensure that\n            // multiple calls to this function won't decrement the reference\n            // counter more than it should. Subsequent calls just result in a noop.\n            if (disposed) {\n                return;\n            }\n            disposed = true;\n            _this.softReferences--;\n            setTimeout(function () {\n                if (!_this.softReferences && !_this.references) {\n                    _this.dispose();\n                }\n            });\n        };\n    };\n    InternalQueryReference.prototype.didChangeOptions = function (watchQueryOptions) {\n        var _this = this;\n        return OBSERVED_CHANGED_OPTIONS.some(function (option) {\n            return option in watchQueryOptions &&\n                !equal(_this.watchQueryOptions[option], watchQueryOptions[option]);\n        });\n    };\n    InternalQueryReference.prototype.applyOptions = function (watchQueryOptions) {\n        var _a = this.watchQueryOptions, currentFetchPolicy = _a.fetchPolicy, currentCanonizeResults = _a.canonizeResults;\n        // \"standby\" is used when `skip` is set to `true`. Detect when we've\n        // enabled the query (i.e. `skip` is `false`) to execute a network request.\n        if (currentFetchPolicy === \"standby\" &&\n            currentFetchPolicy !== watchQueryOptions.fetchPolicy) {\n            this.initiateFetch(this.observable.reobserve(watchQueryOptions));\n        }\n        else {\n            this.observable.silentSetOptions(watchQueryOptions);\n            if (currentCanonizeResults !== watchQueryOptions.canonizeResults) {\n                this.result = __assign(__assign({}, this.result), this.observable.getCurrentResult());\n                this.promise = createFulfilledPromise(this.result);\n            }\n        }\n        return this.promise;\n    };\n    InternalQueryReference.prototype.listen = function (listener) {\n        var _this = this;\n        this.listeners.add(listener);\n        return function () {\n            _this.listeners.delete(listener);\n        };\n    };\n    InternalQueryReference.prototype.refetch = function (variables) {\n        return this.initiateFetch(this.observable.refetch(variables));\n    };\n    InternalQueryReference.prototype.fetchMore = function (options) {\n        return this.initiateFetch(this.observable.fetchMore(options));\n    };\n    InternalQueryReference.prototype.dispose = function () {\n        this.subscription.unsubscribe();\n        this.onDispose();\n    };\n    InternalQueryReference.prototype.onDispose = function () {\n        // noop. overridable by options\n    };\n    InternalQueryReference.prototype.handleNext = function (result) {\n        var _a;\n        switch (this.promise.status) {\n            case \"pending\": {\n                // Maintain the last successful `data` value if the next result does not\n                // have one.\n                if (result.data === void 0) {\n                    result.data = this.result.data;\n                }\n                this.result = result;\n                (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, result);\n                break;\n            }\n            default: {\n                // This occurs when switching to a result that is fully cached when this\n                // class is instantiated. ObservableQuery will run reobserve when\n                // subscribing, which delivers a result from the cache.\n                if (result.data === this.result.data &&\n                    result.networkStatus === this.result.networkStatus) {\n                    return;\n                }\n                // Maintain the last successful `data` value if the next result does not\n                // have one.\n                if (result.data === void 0) {\n                    result.data = this.result.data;\n                }\n                this.result = result;\n                this.promise = createFulfilledPromise(result);\n                this.deliver(this.promise);\n                break;\n            }\n        }\n    };\n    InternalQueryReference.prototype.handleError = function (error) {\n        var _a;\n        this.subscription.unsubscribe();\n        this.subscription = this.observable.resubscribeAfterError(this.handleNext, this.handleError);\n        switch (this.promise.status) {\n            case \"pending\": {\n                (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, error);\n                break;\n            }\n            default: {\n                this.promise = createRejectedPromise(error);\n                this.deliver(this.promise);\n            }\n        }\n    };\n    InternalQueryReference.prototype.deliver = function (promise) {\n        this.listeners.forEach(function (listener) { return listener(promise); });\n    };\n    InternalQueryReference.prototype.initiateFetch = function (returnedPromise) {\n        var _this = this;\n        this.promise = this.createPendingPromise();\n        this.promise.catch(function () { });\n        // If the data returned from the fetch is deeply equal to the data already\n        // in the cache, `handleNext` will not be triggered leaving the promise we\n        // created in a pending state forever. To avoid this situtation, we attempt\n        // to resolve the promise if `handleNext` hasn't been run to ensure the\n        // promise is resolved correctly.\n        returnedPromise\n            .then(function () {\n            // In the case of `fetchMore`, this promise is resolved before a cache\n            // result is emitted due to the fact that `fetchMore` sets a `no-cache`\n            // fetch policy and runs `cache.batch` in its `.then` handler. Because\n            // the timing is different, we accidentally run this update twice\n            // causing an additional re-render with the `fetchMore` result by\n            // itself. By wrapping in `setTimeout`, this should provide a short\n            // delay to allow the `QueryInfo.notify` handler to run before this\n            // promise is checked.\n            // See https://github.com/apollographql/apollo-client/issues/11315 for\n            // more information\n            setTimeout(function () {\n                var _a;\n                if (_this.promise.status === \"pending\") {\n                    // Use the current result from the observable instead of the value\n                    // resolved from the promise. This avoids issues in some cases where\n                    // the raw resolved value should not be the emitted value, such as\n                    // when a `fetchMore` call returns an empty array after it has\n                    // reached the end of the list.\n                    //\n                    // See the following for more information:\n                    // https://github.com/apollographql/apollo-client/issues/11642\n                    _this.result = _this.observable.getCurrentResult();\n                    (_a = _this.resolve) === null || _a === void 0 ? void 0 : _a.call(_this, _this.result);\n                }\n            });\n        })\n            .catch(function (error) { var _a; return (_a = _this.reject) === null || _a === void 0 ? void 0 : _a.call(_this, error); });\n        return returnedPromise;\n    };\n    InternalQueryReference.prototype.subscribeToQuery = function () {\n        var _this = this;\n        this.subscription = this.observable\n            .filter(function (result) { return !equal(result.data, {}) && !equal(result, _this.result); })\n            .subscribe(this.handleNext, this.handleError);\n    };\n    InternalQueryReference.prototype.setResult = function () {\n        // Don't save this result as last result to prevent delivery of last result\n        // when first subscribing\n        var result = this.observable.getCurrentResult(false);\n        if (equal(result, this.result)) {\n            return;\n        }\n        this.result = result;\n        this.promise =\n            (result.data &&\n                (!result.partial || this.watchQueryOptions.returnPartialData)) ?\n                createFulfilledPromise(result)\n                : this.createPendingPromise();\n    };\n    InternalQueryReference.prototype.createPendingPromise = function () {\n        var _this = this;\n        return wrapPromiseWithState(new Promise(function (resolve, reject) {\n            _this.resolve = resolve;\n            _this.reject = reject;\n        }));\n    };\n    return InternalQueryReference;\n}());\nexport { InternalQueryReference };\n//# sourceMappingURL=QueryReference.js.map", "import { Trie } from \"@wry/trie\";\nimport { canUseWeakMap } from \"../../../utilities/index.js\";\nimport { InternalQueryReference } from \"./QueryReference.js\";\nvar SuspenseCache = /** @class */ (function () {\n    function SuspenseCache(options) {\n        if (options === void 0) { options = Object.create(null); }\n        this.queryRefs = new Trie(canUseWeakMap);\n        this.options = options;\n    }\n    SuspenseCache.prototype.getQueryRef = function (cacheKey, createObservable) {\n        var ref = this.queryRefs.lookupArray(cacheKey);\n        if (!ref.current) {\n            ref.current = new InternalQueryReference(createObservable(), {\n                autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,\n                onDispose: function () {\n                    delete ref.current;\n                },\n            });\n        }\n        return ref.current;\n    };\n    SuspenseCache.prototype.add = function (cacheKey, queryRef) {\n        var ref = this.queryRefs.lookupArray(cacheKey);\n        ref.current = queryRef;\n    };\n    return SuspenseCache;\n}());\nexport { SuspenseCache };\n//# sourceMappingURL=SuspenseCache.js.map", "import { SuspenseCache } from \"./SuspenseCache.js\";\nvar suspenseCacheSymbol = Symbol.for(\"apollo.suspenseCache\");\nexport function getSuspenseCache(client) {\n    var _a;\n    if (!client[suspenseCacheSymbol]) {\n        client[suspenseCacheSymbol] = new SuspenseCache((_a = client.defaultOptions.react) === null || _a === void 0 ? void 0 : _a.suspense);\n    }\n    return client[suspenseCacheSymbol];\n}\n//# sourceMappingURL=getSuspenseCache.js.map"], "names": ["originalInvariant", "global", "equal", "__assign", "createFulfilledPromise", "createRejectedPromise", "wrapPromiseWithState", "<PERSON><PERSON>", "canUseWeakMap"], "mappings": ";;;;;;;;;;AAAO,IAAI,OAAO,GAAG,QAAQ;;ACAtB,SAAS,KAAK,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI;AACR,QAAQ,OAAO,KAAK,EAAE,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,GAAG;AAClB;;ACJA,eAAe,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;AACzD,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AACzC,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AACvC,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AAMzC,KAAK,CAAC,YAAY;AAClB,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9C,CAAC,CAAC;;ACZF,IAAI,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AAGtB,SAAS,YAAY,CAAC,MAAM,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG;;ACNO,SAAS,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE;AAClD,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;AACxC,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,qBAAqB,CAAC,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;AACvD,QAAQ,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;AAClD,KAAK,EAAE,KAAK,CAAC;AACb,SAAS,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACvC,SAAS,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7B;;ACLA,SAAS,IAAI,CAAC,EAAE,EAAE;AAClB,IAAI,OAAO,UAAU,OAAO,EAAE;AAC9B,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACzC,YAAY,IAAI,IAAI,GAAG,OAAO,CAAC;AAC/B,YAAY,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/C,YAAY,IAAI,CAAC,OAAO,EAAE;AAC1B,gBAAgB,OAAO,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1D,gBAAgB,IAAI,GAAG,EAAE,CAAC;AAC1B,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,KAAK,CAAC;AACN,CAAC;AACD,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACrE,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,QAAQA,qBAAiB,CAAC,SAAS,EAAE,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9G,KAAK;AACL,CAAC,EAAE;AACH,IAAI,KAAK,EAAE,IAAI,CAACA,qBAAiB,CAAC,KAAK,CAAC;AACxC,IAAI,GAAG,EAAE,IAAI,CAACA,qBAAiB,CAAC,GAAG,CAAC;AACpC,IAAI,IAAI,EAAE,IAAI,CAACA,qBAAiB,CAAC,IAAI,CAAC;AACtC,IAAI,KAAK,EAAE,IAAI,CAACA,qBAAiB,CAAC,KAAK,CAAC;AACxC,CAAC,CAAC,CAAC;AAmBH,IAAI,yBAAyB,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,GAAG,OAAO,CAAC,CAAC;AACnF,SAAS,SAAS,CAAC,GAAG,EAAE;AACxB,IAAI,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE;AAChC,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,IAAI;AACR,QAAQ,OAAO,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,EAAE,EAAE;AACf,QAAQ,OAAO,oBAAoB,CAAC;AACpC,KAAK;AACL,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE;AAClD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,CAAC,OAAO;AAChB,QAAQ,OAAO;AACf,IAAI,QAAQC,QAAM,CAAC,yBAAyB,CAAC;AAC7C,QAAQA,QAAM,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE;AAChF,CAAC;AACD,SAAS,mBAAmB,CAAC,OAAO,EAAE,WAAW,EAAE;AACnD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,CAAC,OAAO;AAChB,QAAQ,OAAO;AACf,IAAI,OAAO,8FAA8F,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC;AACnJ,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;AACxC,KAAK,CAAC,CAAC,CAAC,CAAC;AACT;;AC5EA,IAAI,sBAAsB,GAAG,MAAM,EAAE,CAAC;AACtC,IAAI,cAAc,GAAG,MAAM,EAAE,CAAC;AACvB,SAAS,YAAY,CAAC,gBAAgB,EAAE;AAC/C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,GAAG,IAAI,EAAE,GAAG;AACpB,YAAY,SAAS,EAAE,YAAY;AAYnC,gBAAgB,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AAChF,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,CAAC,sBAAsB,CAAC,GAAG,gBAAgB;AACrD,QAAQ,EAAE,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC,OAAO;AACrD,QAAQ,EAAE,CAAC,CAAC;AACZ,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACM,SAAS,qBAAqB,CAAC,QAAQ,EAAE;AAChD,IAAI,SAAS,CAAC,CAAC,QAAQ,IAAI,sBAAsB,IAAI,QAAQ,EAAE,EAAE,CAAC,CAAC;AACnE,CAAC;AACM,SAAS,iBAAiB,CAAC,QAAQ,EAAE;AAC5C,IAAI,IAAI,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,OAAO,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW;AAC1D,QAAQ,gBAAgB,CAAC,OAAO;AAChC,UAAU,QAAQ,CAAC,cAAc,CAAC,CAAC;AACnC,CAAC;AACM,SAAS,cAAc,CAAC,QAAQ,EAAE;AACzC,IAAI,OAAO,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AAC5C,CAAC;AACM,SAAS,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE;AACzD,IAAI,QAAQ,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;AACvC,CAAC;AACD,IAAI,wBAAwB,GAAG;AAC/B,IAAI,iBAAiB;AACrB,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,CAAC,CAAC;AACC,IAAC,sBAAsB,KAAkB,YAAY;AACxD,IAAI,SAAS,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE;AACzD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AACtB,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAQ,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAChC,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACrC,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE;AAC/B,YAAY,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AAC/C,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;AACzB,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAKhC,QAAQ,IAAI,iBAAiB,GAAG,YAAY;AAC5C,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AACnC,gBAAgB,KAAK,CAAC,oBAAoB,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,oBAAoB,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;AACnJ,aAAa;AACb,SAAS,CAAC;AAIV,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,MAAM,CAAC,cAAc,CAAC,sBAAsB,CAAC,SAAS,EAAE,UAAU,EAAE;AACxE,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AAC5C,SAAS;AACT,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,cAAc,CAAC,sBAAsB,CAAC,SAAS,EAAE,mBAAmB,EAAE;AACjF,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AAC3C,SAAS;AACT,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,sBAAsB,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AAChE,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACzC,QAAQ,IAAI,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;AACrE,QAAQ,IAAI,oBAAoB,GAAG,mBAAmB,KAAK,UAAU,IAAI,mBAAmB,KAAK,SAAS,CAAC;AAC3G,QAAQ,IAAI;AACZ,YAAY,IAAI,oBAAoB,EAAE;AACtC,gBAAgB,UAAU,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC;AACxE,aAAa;AACb,iBAAiB;AACjB,gBAAgB,UAAU,CAAC,gBAAgB,EAAE,CAAC;AAC9C,gBAAgB,UAAU,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,CAAC;AAC5E,aAAa;AACb,YAAY,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACpC,YAAY,IAAI,oBAAoB,EAAE;AACtC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,UAAU,CAAC,SAAS,EAAE,CAAC;AACnC,YAAY,IAAI,CAAC,SAAS,EAAE,CAAC;AAC7B,SAAS;AACT,gBAAgB;AAChB,YAAY,UAAU,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CAAC,CAAC;AAC9E,SAAS;AACT,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AAC1D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,QAAQ,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAChD,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC;AAC7B,QAAQ,OAAO,YAAY;AAC3B,YAAY,IAAI,QAAQ,EAAE;AAC1B,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,QAAQ,GAAG,IAAI,CAAC;AAC5B,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC;AAC/B,YAAY,UAAU,CAAC,YAAY;AACnC,gBAAgB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AACvC,oBAAoB,KAAK,CAAC,OAAO,EAAE,CAAC;AACpC,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AAC9D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;AAC9B,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC;AAC7B,QAAQ,OAAO,YAAY;AAI3B,YAAY,IAAI,QAAQ,EAAE;AAC1B,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,QAAQ,GAAG,IAAI,CAAC;AAC5B,YAAY,KAAK,CAAC,cAAc,EAAE,CAAC;AACnC,YAAY,UAAU,CAAC,YAAY;AACnC,gBAAgB,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AAChE,oBAAoB,KAAK,CAAC,OAAO,EAAE,CAAC;AACpC,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,iBAAiB,EAAE;AACrF,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,wBAAwB,CAAC,IAAI,CAAC,UAAU,MAAM,EAAE;AAC/D,YAAY,OAAO,MAAM,IAAI,iBAAiB;AAC9C,gBAAgB,CAACC,cAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;AACnF,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,iBAAiB,EAAE;AACjF,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,kBAAkB,GAAG,EAAE,CAAC,WAAW,EAAE,sBAAsB,GAAG,EAAE,CAAC,eAAe,CAAC;AAG1H,QAAQ,IAAI,kBAAkB,KAAK,SAAS;AAC5C,YAAY,kBAAkB,KAAK,iBAAiB,CAAC,WAAW,EAAE;AAClE,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;AAC7E,SAAS;AACT,aAAa;AACb,YAAY,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAChE,YAAY,IAAI,sBAAsB,KAAK,iBAAiB,CAAC,eAAe,EAAE;AAC9E,gBAAgB,IAAI,CAAC,MAAM,GAAGC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;AACtG,gBAAgB,IAAI,CAAC,OAAO,GAAGC,gCAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnE,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,QAAQ,EAAE;AAClE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrC,QAAQ,OAAO,YAAY;AAC3B,YAAY,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7C,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AACpE,QAAQ,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AACtE,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,OAAO,EAAE;AACpE,QAAQ,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACtE,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AAC3D,QAAQ,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACxC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;AACzB,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY;AAE7D,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,MAAM,EAAE;AACpE,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;AACnC,YAAY,KAAK,SAAS,EAAE;AAG5B,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AAC5C,oBAAoB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACnD,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrC,gBAAgB,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC/F,gBAAgB,MAAM;AACtB,aAAa;AACb,YAAY,SAAS;AAIrB,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI;AACpD,oBAAoB,MAAM,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;AACxE,oBAAoB,OAAO;AAC3B,iBAAiB;AAGjB,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AAC5C,oBAAoB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACnD,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrC,gBAAgB,IAAI,CAAC,OAAO,GAAGA,gCAAsB,CAAC,MAAM,CAAC,CAAC;AAC9D,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3C,gBAAgB,MAAM;AACtB,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,KAAK,EAAE;AACpE,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACxC,QAAQ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACrG,QAAQ,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;AACnC,YAAY,KAAK,SAAS,EAAE;AAC5B,gBAAgB,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7F,gBAAgB,MAAM;AACtB,aAAa;AACb,YAAY,SAAS;AACrB,gBAAgB,IAAI,CAAC,OAAO,GAAGC,+BAAqB,CAAC,KAAK,CAAC,CAAC;AAC5D,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,OAAO,EAAE;AAClE,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAClF,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,eAAe,EAAE;AAChF,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AACnD,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AAM5C,QAAQ,eAAe;AACvB,aAAa,IAAI,CAAC,YAAY;AAW9B,YAAY,UAAU,CAAC,YAAY;AACnC,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;AASxD,oBAAoB,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;AACvE,oBAAoB,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3G,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,aAAa,KAAK,CAAC,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACxI,QAAQ,OAAO,eAAe,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY;AACpE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU;AAC3C,aAAa,MAAM,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,CAACH,cAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAACA,cAAK,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AAC1G,aAAa,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC1D,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY;AAG7D,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC7D,QAAQ,IAAIA,cAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;AACxC,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,QAAQ,IAAI,CAAC,OAAO;AACpB,YAAY,CAAC,MAAM,CAAC,IAAI;AACxB,iBAAiB,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;AAC7E,gBAAgBE,gCAAsB,CAAC,MAAM,CAAC;AAC9C,kBAAkB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC9C,KAAK,CAAC;AACN,IAAI,sBAAsB,CAAC,SAAS,CAAC,oBAAoB,GAAG,YAAY;AACxE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAOE,8BAAoB,CAAC,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAC3E,YAAY,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AACpC,YAAY,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAClC,SAAS,CAAC,CAAC,CAAC;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,sBAAsB,CAAC;AAClC,CAAC,EAAE;;AC/TH,IAAI,aAAa,KAAkB,YAAY;AAC/C,IAAI,SAAS,aAAa,CAAC,OAAO,EAAE;AACpC,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAClE,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAIC,SAAI,CAACC,uBAAa,CAAC,CAAC;AACjD,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,KAAK;AACL,IAAI,aAAa,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE,gBAAgB,EAAE;AAChF,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AAC1B,YAAY,GAAG,CAAC,OAAO,GAAG,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,EAAE;AACzE,gBAAgB,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB;AACvE,gBAAgB,SAAS,EAAE,YAAY;AACvC,oBAAoB,OAAO,GAAG,CAAC,OAAO,CAAC;AACvC,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC,OAAO,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE;AAChE,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACvD,QAAQ,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC,EAAE,CAAC;;ACzBJ,IAAI,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AACtD,SAAS,gBAAgB,CAAC,MAAM,EAAE;AACzC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE;AACtC,QAAQ,MAAM,CAAC,mBAAmB,CAAC,GAAG,IAAI,aAAa,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC7I,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACvC;;;;;;;;;;"}