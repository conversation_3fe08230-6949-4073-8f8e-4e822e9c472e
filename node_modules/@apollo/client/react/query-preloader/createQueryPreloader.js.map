{"version": 3, "file": "createQueryPreloader.js", "sourceRoot": "", "sources": ["../../../src/react/query-preloader/createQueryPreloader.ts"], "names": [], "mappings": ";AAeA,OAAO,EAAE,sBAAsB,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAG5E,OAAO,EAAE,QAAQ,EAAE,MAAM,4BAA4B,CAAC;AAoItD;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,oBAAoB,CAClC,MAAyB;IAEzB,OAAO,QAAQ,CACb,sBAAsB,EACtB,qBAAqB,EACrB,MAAM,CACP,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED,IAAM,qBAAqB,GAAgC,UAAC,MAAM;IAChE,OAAO,SAAS,YAAY,CAI1B,KAA0D,EAC1D,OACmD;;QADnD,wBAAA,EAAA,UACgC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAEnD,IAAM,QAAQ,GAAG,IAAI,sBAAsB,CACzC,MAAM,CAAC,UAAU,CAAC,sBACb,OAAO,KACV,KAAK,OAAA,GACyB,CAAC,EACjC;YACE,oBAAoB,EAClB,MAAA,MAAA,MAAM,CAAC,cAAc,CAAC,KAAK,0CAAE,QAAQ,0CAAE,oBAAoB;SAC9D,CACF,CAAC;QAEF,OAAO,YAAY,CAAC,QAAQ,CAAyC,CAAC;IACxE,CAAC,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import type {\n  ApolloClient,\n  DefaultContext,\n  DocumentNode,\n  ErrorPolicy,\n  OperationVariables,\n  RefetchWritePolicy,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport type {\n  DeepPartial,\n  OnlyRequiredProperties,\n} from \"../../utilities/index.js\";\nimport { InternalQueryReference, wrapQueryRef } from \"../internal/index.js\";\nimport type { PreloadedQueryRef } from \"../internal/index.js\";\nimport type { NoInfer } from \"../index.js\";\nimport { wrapHook } from \"../hooks/internal/index.js\";\n\ntype VariablesOption<TVariables extends OperationVariables> =\n  [TVariables] extends [never] ?\n    {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n      variables?: Record<string, never>;\n    }\n  : {} extends OnlyRequiredProperties<TVariables> ?\n    {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n      variables?: TVariables;\n    }\n  : {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n      variables: TVariables;\n    };\n\nexport type PreloadQueryFetchPolicy = Extract<\n  WatchQueryFetchPolicy,\n  \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n>;\n\nexport type PreloadQueryOptions<\n  TVariables extends OperationVariables = OperationVariables,\n> = {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n  canonizeResults?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: PreloadQueryFetchPolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n  returnPartialData?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n  refetchWritePolicy?: RefetchWritePolicy;\n} & VariablesOption<TVariables>;\n\ntype PreloadQueryOptionsArg<\n  TVariables extends OperationVariables,\n  TOptions = unknown,\n> = [TVariables] extends [never] ?\n  [options?: PreloadQueryOptions<never> & TOptions]\n: {} extends OnlyRequiredProperties<TVariables> ?\n  [\n    options?: PreloadQueryOptions<NoInfer<TVariables>> &\n      Omit<TOptions, \"variables\">,\n  ]\n: [\n    options: PreloadQueryOptions<NoInfer<TVariables>> &\n      Omit<TOptions, \"variables\">,\n  ];\n\n/**\n * A function that will begin loading a query when called. It's result can be\n * read by `useReadQuery` which will suspend until the query is loaded.\n * This is useful when you want to start loading a query as early as possible\n * outside of a React component.\n *\n * @example\n * ```js\n * const preloadQuery = createQueryPreloader(client);\n * const queryRef = preloadQuery(query, { variables, ...otherOptions });\n *\n * function App() {\n *   return (\n *     <Suspense fallback={<div>Loading</div>}>\n *       <MyQuery />\n *     </Suspense>\n *   );\n * }\n *\n * function MyQuery() {\n *   const { data } = useReadQuery(queryRef);\n *\n *   // do something with `data`\n * }\n * ```\n */\nexport interface PreloadQueryFunction {\n  /** {@inheritDoc @apollo/client!PreloadQueryFunction:interface} */\n  <\n    TData,\n    TVariables extends OperationVariables,\n    TOptions extends Omit<PreloadQueryOptions, \"variables\">,\n  >(\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    ...[options]: PreloadQueryOptionsArg<NoInfer<TVariables>, TOptions>\n  ): PreloadedQueryRef<\n    TOptions[\"errorPolicy\"] extends \"ignore\" | \"all\" ?\n      TOptions[\"returnPartialData\"] extends true ?\n        DeepPartial<TData> | undefined\n      : TData | undefined\n    : TOptions[\"returnPartialData\"] extends true ? DeepPartial<TData>\n    : TData,\n    TVariables\n  >;\n\n  /** {@inheritDoc @apollo/client!PreloadQueryFunction:interface} */\n  <TData = unknown, TVariables extends OperationVariables = OperationVariables>(\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    options: PreloadQueryOptions<NoInfer<TVariables>> & {\n      returnPartialData: true;\n      errorPolicy: \"ignore\" | \"all\";\n    }\n  ): PreloadedQueryRef<DeepPartial<TData> | undefined, TVariables>;\n\n  /** {@inheritDoc @apollo/client!PreloadQueryFunction:interface} */\n  <TData = unknown, TVariables extends OperationVariables = OperationVariables>(\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    options: PreloadQueryOptions<NoInfer<TVariables>> & {\n      errorPolicy: \"ignore\" | \"all\";\n    }\n  ): PreloadedQueryRef<TData | undefined, TVariables>;\n\n  /** {@inheritDoc @apollo/client!PreloadQueryFunction:interface} */\n  <TData = unknown, TVariables extends OperationVariables = OperationVariables>(\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    options: PreloadQueryOptions<NoInfer<TVariables>> & {\n      returnPartialData: true;\n    }\n  ): PreloadedQueryRef<DeepPartial<TData>, TVariables>;\n\n  /** {@inheritDoc @apollo/client!PreloadQueryFunction:interface} */\n  <TData = unknown, TVariables extends OperationVariables = OperationVariables>(\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    ...[options]: PreloadQueryOptionsArg<NoInfer<TVariables>>\n  ): PreloadedQueryRef<TData, TVariables>;\n}\n\n/**\n * A higher order function that returns a `preloadQuery` function which\n * can be used to begin loading a query with the given `client`. This is useful\n * when you want to start loading a query as early as possible outside of a\n * React component.\n *\n * > Refer to the [Suspense - Initiating queries outside React](https://www.apollographql.com/docs/react/data/suspense#initiating-queries-outside-react) section for a more in-depth overview.\n *\n * @param client - The `ApolloClient` instance that will be used to load queries\n * from the returned `preloadQuery` function.\n * @returns The `preloadQuery` function.\n *\n * @example\n * ```js\n * const preloadQuery = createQueryPreloader(client);\n * ```\n * @since 3.9.0\n */\nexport function createQueryPreloader(\n  client: ApolloClient<any>\n): PreloadQueryFunction {\n  return wrapHook(\n    \"createQueryPreloader\",\n    _createQueryPreloader,\n    client\n  )(client);\n}\n\nconst _createQueryPreloader: typeof createQueryPreloader = (client) => {\n  return function preloadQuery<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  >(\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    options: PreloadQueryOptions<NoInfer<TVariables>> &\n      VariablesOption<TVariables> = Object.create(null)\n  ): PreloadedQueryRef<TData, TVariables> {\n    const queryRef = new InternalQueryReference(\n      client.watchQuery({\n        ...options,\n        query,\n      } as WatchQueryOptions<any, any>),\n      {\n        autoDisposeTimeoutMs:\n          client.defaultOptions.react?.suspense?.autoDisposeTimeoutMs,\n      }\n    );\n\n    return wrapQueryRef(queryRef) as PreloadedQueryRef<TData, TVariables>;\n  };\n};\n"]}