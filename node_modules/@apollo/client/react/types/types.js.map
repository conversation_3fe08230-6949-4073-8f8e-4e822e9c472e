{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/react/types/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type * as ReactTypes from \"react\";\nimport type { DocumentNode, GraphQLFormattedError } from \"graphql\";\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\n\nimport type {\n  Observable,\n  ObservableSubscription,\n} from \"../../utilities/index.js\";\nimport type { FetchResult } from \"../../link/core/index.js\";\nimport type { ApolloError } from \"../../errors/index.js\";\nimport type {\n  ApolloCache,\n  ApolloClient,\n  DefaultContext,\n  FetchPolicy,\n  NetworkStatus,\n  ObservableQuery,\n  OperationVariables,\n  InternalRefetchQueriesInclude,\n  WatchQueryOptions,\n  WatchQueryFetchPolicy,\n  SubscribeToMoreOptions,\n  ApolloQueryResult,\n  FetchMoreQueryOptions,\n  ErrorPolicy,\n  RefetchWritePolicy,\n} from \"../../core/index.js\";\nimport type {\n  MutationSharedOptions,\n  SharedWatchQueryOptions,\n} from \"../../core/watchQueryOptions.js\";\nimport type { MaybeMasked, Unmasked } from \"../../masking/index.js\";\n\n/* QueryReference type */\n\nexport type {\n  QueryReference,\n  QueryRef,\n  PreloadedQueryRef,\n} from \"../internal/index.js\";\n\n/* Common types */\n\nexport type { DefaultContext as Context } from \"../../core/index.js\";\n\nexport type CommonOptions<TOptions> = TOptions & {\n  client?: ApolloClient<object>;\n};\n\n/* Query types */\n\nexport interface BaseQueryOptions<\n  TVariables extends OperationVariables = OperationVariables,\n  TData = any,\n> extends SharedWatchQueryOptions<TVariables, TData> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#ssr:member} */\n  ssr?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n  client?: ApolloClient<any>;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n}\n\nexport interface QueryFunctionOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends BaseQueryOptions<TVariables, TData> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip:member} */\n  skip?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#onCompleted:member} */\n  onCompleted?: (data: MaybeMasked<TData>) => void;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#onError:member} */\n  onError?: (error: ApolloError) => void;\n\n  // Default WatchQueryOptions for this useQuery, providing initial values for\n  // unspecified options, superseding client.defaultOptions.watchQuery (option\n  // by option, not whole), but never overriding options previously passed to\n  // useQuery (or options added/modified later by other means).\n  // TODO What about about default values that are expensive to evaluate?\n  /** @internal */\n  defaultOptions?: Partial<WatchQueryOptions<TVariables, TData>>;\n}\n\nexport interface ObservableQueryFields<\n  TData,\n  TVariables extends OperationVariables,\n> {\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#startPolling:member} */\n  startPolling: (pollInterval: number) => void;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#stopPolling:member} */\n  stopPolling: () => void;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#subscribeToMore:member} */\n  subscribeToMore: <\n    TSubscriptionData = TData,\n    TSubscriptionVariables extends OperationVariables = TVariables,\n  >(\n    options: SubscribeToMoreOptions<\n      TData,\n      TSubscriptionVariables,\n      TSubscriptionData\n    >\n  ) => () => void;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#updateQuery:member} */\n  updateQuery: <TVars extends OperationVariables = TVariables>(\n    mapFn: (\n      previousQueryResult: Unmasked<TData>,\n      options: Pick<WatchQueryOptions<TVars, TData>, \"variables\">\n    ) => Unmasked<TData>\n  ) => void;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member} */\n  refetch: (\n    variables?: Partial<TVariables>\n  ) => Promise<ApolloQueryResult<MaybeMasked<TData>>>;\n  /** @internal */\n  reobserve: (\n    newOptions?: Partial<WatchQueryOptions<TVariables, TData>>,\n    newNetworkStatus?: NetworkStatus\n  ) => Promise<ApolloQueryResult<MaybeMasked<TData>>>;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#variables:member} */\n  variables: TVariables | undefined;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member} */\n  fetchMore: <\n    TFetchData = TData,\n    TFetchVars extends OperationVariables = TVariables,\n  >(\n    fetchMoreOptions: FetchMoreQueryOptions<TFetchVars, TFetchData> & {\n      updateQuery?: (\n        previousQueryResult: Unmasked<TData>,\n        options: {\n          fetchMoreResult: Unmasked<TFetchData>;\n          variables: TFetchVars;\n        }\n      ) => Unmasked<TData>;\n    }\n  ) => Promise<ApolloQueryResult<MaybeMasked<TFetchData>>>;\n}\n\nexport interface QueryResult<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends ObservableQueryFields<TData, TVariables> {\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#client:member} */\n  client: ApolloClient<any>;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#observable:member} */\n  observable: ObservableQuery<TData, TVariables>;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#data:member} */\n  data: MaybeMasked<TData> | undefined;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#previousData:member} */\n  previousData?: MaybeMasked<TData>;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n  error?: ApolloError;\n  /**\n   * @deprecated This property will be removed in a future version of Apollo Client.\n   * Please use `error.graphQLErrors` instead.\n   */\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#loading:member} */\n  loading: boolean;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n  networkStatus: NetworkStatus;\n  /** {@inheritDoc @apollo/client!QueryResultDocumentation#called:member} */\n  called: boolean;\n}\n\nexport interface QueryDataOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends QueryFunctionOptions<TData, TVariables> {\n  children?: (result: QueryResult<TData, TVariables>) => ReactTypes.ReactNode;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#query:member} */\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\n\nexport interface QueryHookOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends QueryFunctionOptions<TData, TVariables> {}\n\nexport interface LazyQueryHookOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends BaseQueryOptions<TVariables, TData> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#onCompleted:member} */\n  onCompleted?: (data: MaybeMasked<TData>) => void;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#onError:member} */\n  onError?: (error: ApolloError) => void;\n\n  /** @internal */\n  defaultOptions?: Partial<WatchQueryOptions<TVariables, TData>>;\n}\nexport interface LazyQueryHookExecOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends LazyQueryHookOptions<TData, TVariables> {\n  query?: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\n\nexport type SuspenseQueryHookFetchPolicy = Extract<\n  WatchQueryFetchPolicy,\n  \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n>;\n\nexport interface SuspenseQueryHookOptions<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n  client?: ApolloClient<any>;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n  canonizeResults?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n  returnPartialData?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy_suspense:member} */\n  refetchWritePolicy?: RefetchWritePolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: SuspenseQueryHookFetchPolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n  queryKey?: string | number | any[];\n\n  /**\n   * {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip_deprecated:member}\n   *\n   * @example Recommended usage of `skipToken`:\n   * ```ts\n   * import { skipToken, useSuspenseQuery } from '@apollo/client';\n   *\n   * const { data } = useSuspenseQuery(query, id ? { variables: { id } } : skipToken);\n   * ```\n   */\n  skip?: boolean;\n}\n\nexport type BackgroundQueryHookFetchPolicy = Extract<\n  WatchQueryFetchPolicy,\n  \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n>;\n\nexport interface BackgroundQueryHookOptions<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n> extends Pick<\n    QueryHookOptions<TData, TVariables>,\n    | \"client\"\n    | \"variables\"\n    | \"errorPolicy\"\n    | \"context\"\n    | \"canonizeResults\"\n    | \"returnPartialData\"\n    | \"refetchWritePolicy\"\n  > {\n  fetchPolicy?: BackgroundQueryHookFetchPolicy;\n  queryKey?: string | number | any[];\n\n  /**\n   * {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip_deprecated:member}\n   *\n   * @example Recommended usage of `skipToken`:\n   * ```ts\n   * import { skipToken, useBackgroundQuery } from '@apollo/client';\n   *\n   * const [queryRef] = useBackgroundQuery(query, id ? { variables: { id } } : skipToken);\n   * ```\n   */\n  skip?: boolean;\n}\n\nexport type LoadableQueryHookFetchPolicy = Extract<\n  WatchQueryFetchPolicy,\n  \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n>;\n\nexport interface LoadableQueryHookOptions {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n  canonizeResults?: boolean;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n  client?: ApolloClient<any>;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: LoadableQueryHookFetchPolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n  queryKey?: string | number | any[];\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n  refetchWritePolicy?: RefetchWritePolicy;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n  returnPartialData?: boolean;\n}\n\n/**\n * @deprecated This type will be removed in the next major version of Apollo Client\n */\nexport interface QueryLazyOptions<TVariables> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n}\n\n/**\n * @deprecated This type will be removed in the next major version of Apollo Client\n */\nexport type LazyQueryResult<\n  TData,\n  TVariables extends OperationVariables,\n> = QueryResult<TData, TVariables>;\n\n/**\n * @deprecated This type will be removed in the next major version of Apollo Client\n */\nexport type QueryTuple<\n  TData,\n  TVariables extends OperationVariables,\n> = LazyQueryResultTuple<TData, TVariables>;\n\nexport type LazyQueryExecFunction<\n  TData,\n  TVariables extends OperationVariables,\n> = (\n  options?: Partial<LazyQueryHookExecOptions<TData, TVariables>>\n) => Promise<QueryResult<TData, TVariables>>;\n\nexport type LazyQueryResultTuple<\n  TData,\n  TVariables extends OperationVariables,\n> = [\n  execute: LazyQueryExecFunction<TData, TVariables>,\n  result: QueryResult<TData, TVariables>,\n];\n\n/* Mutation types */\n\nexport type RefetchQueriesFunction = (\n  ...args: any[]\n) => InternalRefetchQueriesInclude;\n\nexport interface BaseMutationOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends MutationSharedOptions<TData, TVariables, TContext, TCache> {\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#client:member} */\n  client?: ApolloClient<object>;\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n  notifyOnNetworkStatusChange?: boolean;\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onCompleted:member} */\n  onCompleted?: (\n    data: MaybeMasked<TData>,\n    clientOptions?: BaseMutationOptions\n  ) => void;\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onError:member} */\n  onError?: (error: ApolloError, clientOptions?: BaseMutationOptions) => void;\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#ignoreResults:member} */\n  ignoreResults?: boolean;\n}\n\nexport interface MutationFunctionOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends BaseMutationOptions<TData, TVariables, TContext, TCache> {\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#mutation:member} */\n  mutation?: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\n\nexport interface MutationResult<TData = any> {\n  /** {@inheritDoc @apollo/client!MutationResultDocumentation#data:member} */\n  data?: MaybeMasked<TData> | null;\n  /** {@inheritDoc @apollo/client!MutationResultDocumentation#error:member} */\n  error?: ApolloError;\n  /** {@inheritDoc @apollo/client!MutationResultDocumentation#loading:member} */\n  loading: boolean;\n  /** {@inheritDoc @apollo/client!MutationResultDocumentation#called:member} */\n  called: boolean;\n  /** {@inheritDoc @apollo/client!MutationResultDocumentation#client:member} */\n  client: ApolloClient<object>;\n  /** {@inheritDoc @apollo/client!MutationResultDocumentation#reset:member} */\n  reset: () => void;\n}\n\nexport declare type MutationFunction<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> = (\n  options?: MutationFunctionOptions<TData, TVariables, TContext, TCache>\n) => Promise<FetchResult<MaybeMasked<TData>>>;\n\nexport interface MutationHookOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends BaseMutationOptions<TData, TVariables, TContext, TCache> {}\n\nexport interface MutationDataOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends BaseMutationOptions<TData, TVariables, TContext, TCache> {\n  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\n\nexport type MutationTuple<\n  TData,\n  TVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> = [\n  mutate: (\n    options?: MutationFunctionOptions<TData, TVariables, TContext, TCache>\n    // TODO This FetchResult<TData> seems strange here, as opposed to an\n    // ApolloQueryResult<TData>\n  ) => Promise<FetchResult<MaybeMasked<TData>>>,\n  result: MutationResult<TData>,\n];\n\n/* Subscription types */\n\nexport interface OnDataOptions<TData = any> {\n  client: ApolloClient<object>;\n  data: SubscriptionResult<TData>;\n}\n\nexport interface OnSubscriptionDataOptions<TData = any> {\n  client: ApolloClient<object>;\n  subscriptionData: SubscriptionResult<TData>;\n}\n\nexport interface BaseSubscriptionOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> {\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: FetchPolicy;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#shouldResubscribe:member} */\n  shouldResubscribe?:\n    | boolean\n    | ((options: BaseSubscriptionOptions<TData, TVariables>) => boolean);\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#client:member} */\n  client?: ApolloClient<object>;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#skip:member} */\n  skip?: boolean;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#extensions:member} */\n  extensions?: Record<string, any>;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onComplete:member} */\n  onComplete?: () => void;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onData:member} */\n  onData?: (options: OnDataOptions<TData>) => any;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onSubscriptionData:member} */\n  onSubscriptionData?: (options: OnSubscriptionDataOptions<TData>) => any;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onError:member} */\n  onError?: (error: ApolloError) => void;\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onSubscriptionComplete:member} */\n  onSubscriptionComplete?: () => void;\n  /**\n   * {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#ignoreResults:member}\n   * @defaultValue `false`\n   */\n  ignoreResults?: boolean;\n}\n\nexport interface SubscriptionResult<TData = any, TVariables = any> {\n  /** {@inheritDoc @apollo/client!SubscriptionResultDocumentation#loading:member} */\n  loading: boolean;\n  /** {@inheritDoc @apollo/client!SubscriptionResultDocumentation#data:member} */\n  data?: MaybeMasked<TData>;\n  /** {@inheritDoc @apollo/client!SubscriptionResultDocumentation#error:member} */\n  error?: ApolloError;\n  // This was added by the legacy useSubscription type, and is tested in unit\n  // tests, but probably shouldn’t be added to the result.\n  /**\n   * @internal\n   */\n  variables?: TVariables;\n}\n\nexport interface SubscriptionHookOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends BaseSubscriptionOptions<TData, TVariables> {}\n\n/**\n * @deprecated This type is not used anymore. It will be removed in the next major version of Apollo Client\n */\nexport interface SubscriptionDataOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends BaseSubscriptionOptions<TData, TVariables> {\n  subscription: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  children?:\n    | null\n    | ((result: SubscriptionResult<TData>) => ReactTypes.ReactNode);\n}\n\nexport interface SubscriptionCurrentObservable {\n  query?: Observable<any>;\n  subscription?: ObservableSubscription;\n}\n\nexport type { NoInfer } from \"../../utilities/index.js\";\n"]}