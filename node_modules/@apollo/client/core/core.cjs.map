{"version": 3, "file": "core.cjs", "sources": ["../version.js", "../utilities/common/objects.js", "../utilities/common/arrays.js", "../utilities/common/mergeDeep.js", "../utilities/common/incrementalResult.js", "networkStatus.js", "equalByQuery.js", "ObservableQuery.js", "QueryInfo.js", "QueryManager.js", "LocalState.js", "../utilities/caching/sizes.js", "../utilities/caching/getMemoryInternals.js", "ApolloClient.js", "index.js"], "sourcesContent": ["export var version = \"3.12.8\";\n//# sourceMappingURL=version.js.map", "export function isNonNullObject(obj) {\n    return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n    return (obj !== null &&\n        typeof obj === \"object\" &&\n        (Object.getPrototypeOf(obj) === Object.prototype ||\n            Object.getPrototypeOf(obj) === null));\n}\n//# sourceMappingURL=objects.js.map", "// A version of Array.isArray that works better with readonly arrays.\nexport var isArray = Array.isArray;\nexport function isNonEmptyArray(value) {\n    return Array.isArray(value) && value.length > 0;\n}\n//# sourceMappingURL=arrays.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { isNonNullObject } from \"./objects.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function mergeDeep() {\n    var sources = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        sources[_i] = arguments[_i];\n    }\n    return mergeDeepArray(sources);\n}\n// In almost any situation where you could succeed in getting the\n// TypeScript compiler to infer a tuple type for the sources array, you\n// could just use mergeDeep instead of mergeDeepArray, so instead of\n// trying to convert T[] to an intersection type we just infer the array\n// element type, which works perfectly when the sources array has a\n// consistent element type.\nexport function mergeDeepArray(sources) {\n    var target = sources[0] || {};\n    var count = sources.length;\n    if (count > 1) {\n        var merger = new DeepMerger();\n        for (var i = 1; i < count; ++i) {\n            target = merger.merge(target, sources[i]);\n        }\n    }\n    return target;\n}\nvar defaultReconciler = function (target, source, property) {\n    return this.merge(target[property], source[property]);\n};\nvar DeepMerger = /** @class */ (function () {\n    function DeepMerger(reconciler) {\n        if (reconciler === void 0) { reconciler = defaultReconciler; }\n        this.reconciler = reconciler;\n        this.isObject = isNonNullObject;\n        this.pastCopies = new Set();\n    }\n    DeepMerger.prototype.merge = function (target, source) {\n        var _this = this;\n        var context = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            context[_i - 2] = arguments[_i];\n        }\n        if (isNonNullObject(source) && isNonNullObject(target)) {\n            Object.keys(source).forEach(function (sourceKey) {\n                if (hasOwnProperty.call(target, sourceKey)) {\n                    var targetValue = target[sourceKey];\n                    if (source[sourceKey] !== targetValue) {\n                        var result = _this.reconciler.apply(_this, __spreadArray([target,\n                            source,\n                            sourceKey], context, false));\n                        // A well-implemented reconciler may return targetValue to indicate\n                        // the merge changed nothing about the structure of the target.\n                        if (result !== targetValue) {\n                            target = _this.shallowCopyForMerge(target);\n                            target[sourceKey] = result;\n                        }\n                    }\n                }\n                else {\n                    // If there is no collision, the target can safely share memory with\n                    // the source, and the recursion can terminate here.\n                    target = _this.shallowCopyForMerge(target);\n                    target[sourceKey] = source[sourceKey];\n                }\n            });\n            return target;\n        }\n        // If source (or target) is not an object, let source replace target.\n        return source;\n    };\n    DeepMerger.prototype.shallowCopyForMerge = function (value) {\n        if (isNonNullObject(value)) {\n            if (!this.pastCopies.has(value)) {\n                if (Array.isArray(value)) {\n                    value = value.slice(0);\n                }\n                else {\n                    value = __assign({ __proto__: Object.getPrototypeOf(value) }, value);\n                }\n                this.pastCopies.add(value);\n            }\n        }\n        return value;\n    };\n    return DeepMerger;\n}());\nexport { DeepMerger };\n//# sourceMappingURL=mergeDeep.js.map", "import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n    return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n    return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n    return (isExecutionPatchIncrementalResult(value) ||\n        isExecutionPatchInitialResult(value));\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n    return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n    var mergedData = prevResult;\n    var merger = new DeepMerger();\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (_a) {\n            var data = _a.data, path = _a.path;\n            for (var i = path.length - 1; i >= 0; --i) {\n                var key = path[i];\n                var isNumericKey = !isNaN(+key);\n                var parent_1 = isNumericKey ? [] : {};\n                parent_1[key] = data;\n                data = parent_1;\n            }\n            mergedData = merger.merge(mergedData, data);\n        });\n    }\n    return mergedData;\n}\n//# sourceMappingURL=incrementalResult.js.map", "/**\n * The current status of a query’s execution in our system.\n */\nexport var NetworkStatus;\n(function (NetworkStatus) {\n    /**\n     * The query has never been run before and the query is now currently running. A query will still\n     * have this network status even if a partial data result was returned from the cache, but a\n     * query was dispatched anyway.\n     */\n    NetworkStatus[NetworkStatus[\"loading\"] = 1] = \"loading\";\n    /**\n     * If `setVariables` was called and a query was fired because of that then the network status\n     * will be `setVariables` until the result of that query comes back.\n     */\n    NetworkStatus[NetworkStatus[\"setVariables\"] = 2] = \"setVariables\";\n    /**\n     * Indicates that `fetchMore` was called on this query and that the query created is currently in\n     * flight.\n     */\n    NetworkStatus[NetworkStatus[\"fetchMore\"] = 3] = \"fetchMore\";\n    /**\n     * Similar to the `setVariables` network status. It means that `refetch` was called on a query\n     * and the refetch request is currently in flight.\n     */\n    NetworkStatus[NetworkStatus[\"refetch\"] = 4] = \"refetch\";\n    /**\n     * Indicates that a polling query is currently in flight. So for example if you are polling a\n     * query every 10 seconds then the network status will switch to `poll` every 10 seconds whenever\n     * a poll request has been sent but not resolved.\n     */\n    NetworkStatus[NetworkStatus[\"poll\"] = 6] = \"poll\";\n    /**\n     * No request is in flight for this query, and no errors happened. Everything is OK.\n     */\n    NetworkStatus[NetworkStatus[\"ready\"] = 7] = \"ready\";\n    /**\n     * No request is in flight for this query, but one or more errors were detected.\n     */\n    NetworkStatus[NetworkStatus[\"error\"] = 8] = \"error\";\n})(NetworkStatus || (NetworkStatus = {}));\n/**\n * Returns true if there is currently a network request in flight according to a given network\n * status.\n */\nexport function isNetworkRequestInFlight(networkStatus) {\n    return networkStatus ? networkStatus < 7 : false;\n}\n/**\n * Returns true if the network request is in ready or error state according to a given network\n * status.\n */\nexport function isNetworkRequestSettled(networkStatus) {\n    return networkStatus === 7 || networkStatus === 8;\n}\n//# sourceMappingURL=networkStatus.js.map", "import { __rest } from \"tslib\";\nimport equal from \"@wry/equality\";\nimport { createFragmentMap, getFragmentDefinitions, getFragmentFromSelection, getMainDefinition, isField, resultKeyNameFromField, shouldInclude, } from \"../utilities/index.js\";\n// Returns true if aResult and bResult are deeply equal according to the fields\n// selected by the given query, ignoring any fields marked as @nonreactive.\nexport function equalByQuery(query, _a, _b, variables) {\n    var aData = _a.data, aRest = __rest(_a, [\"data\"]);\n    var bData = _b.data, bRest = __rest(_b, [\"data\"]);\n    return (equal(aRest, bRest) &&\n        equalBySelectionSet(getMainDefinition(query).selectionSet, aData, bData, {\n            fragmentMap: createFragmentMap(getFragmentDefinitions(query)),\n            variables: variables,\n        }));\n}\nfunction equalBySelectionSet(selectionSet, aResult, bResult, context) {\n    if (aResult === bResult) {\n        return true;\n    }\n    var seenSelections = new Set();\n    // Returning true from this Array.prototype.every callback function skips the\n    // current field/subtree. Returning false aborts the entire traversal\n    // immediately, causing equalBySelectionSet to return false.\n    return selectionSet.selections.every(function (selection) {\n        // Avoid re-processing the same selection at the same level of recursion, in\n        // case the same field gets included via multiple indirect fragment spreads.\n        if (seenSelections.has(selection))\n            return true;\n        seenSelections.add(selection);\n        // Ignore @skip(if: true) and @include(if: false) fields.\n        if (!shouldInclude(selection, context.variables))\n            return true;\n        // If the field or (named) fragment spread has a @nonreactive directive on\n        // it, we don't care if it's different, so we pretend it's the same.\n        if (selectionHasNonreactiveDirective(selection))\n            return true;\n        if (isField(selection)) {\n            var resultKey = resultKeyNameFromField(selection);\n            var aResultChild = aResult && aResult[resultKey];\n            var bResultChild = bResult && bResult[resultKey];\n            var childSelectionSet = selection.selectionSet;\n            if (!childSelectionSet) {\n                // These are scalar values, so we can compare them with deep equal\n                // without redoing the main recursive work.\n                return equal(aResultChild, bResultChild);\n            }\n            var aChildIsArray = Array.isArray(aResultChild);\n            var bChildIsArray = Array.isArray(bResultChild);\n            if (aChildIsArray !== bChildIsArray)\n                return false;\n            if (aChildIsArray && bChildIsArray) {\n                var length_1 = aResultChild.length;\n                if (bResultChild.length !== length_1) {\n                    return false;\n                }\n                for (var i = 0; i < length_1; ++i) {\n                    if (!equalBySelectionSet(childSelectionSet, aResultChild[i], bResultChild[i], context)) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n            return equalBySelectionSet(childSelectionSet, aResultChild, bResultChild, context);\n        }\n        else {\n            var fragment = getFragmentFromSelection(selection, context.fragmentMap);\n            if (fragment) {\n                // The fragment might === selection if it's an inline fragment, but\n                // could be !== if it's a named fragment ...spread.\n                if (selectionHasNonreactiveDirective(fragment))\n                    return true;\n                return equalBySelectionSet(fragment.selectionSet, \n                // Notice that we reuse the same aResult and bResult values here,\n                // since the fragment ...spread does not specify a field name, but\n                // consists of multiple fields (within the fragment's selection set)\n                // that should be applied to the current result value(s).\n                aResult, bResult, context);\n            }\n        }\n    });\n}\nfunction selectionHasNonreactiveDirective(selection) {\n    return (!!selection.directives && selection.directives.some(directiveIsNonreactive));\n}\nfunction directiveIsNonreactive(dir) {\n    return dir.name.value === \"nonreactive\";\n}\n//# sourceMappingURL=equalByQuery.js.map", "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { cloneDeep, compact, getOperationDefinition, Observable, iterateObserversSafely, fixObservableSubclass, getQueryDefinition, preventUnhandledRejection, } from \"../utilities/index.js\";\nimport { ApolloError, isApolloError } from \"../errors/index.js\";\nimport { equalByQuery } from \"./equalByQuery.js\";\nvar assign = Object.assign, hasOwnProperty = Object.hasOwnProperty;\nvar ObservableQuery = /** @class */ (function (_super) {\n    __extends(ObservableQuery, _super);\n    function ObservableQuery(_a) {\n        var queryManager = _a.queryManager, queryInfo = _a.queryInfo, options = _a.options;\n        var _this = _super.call(this, function (observer) {\n            // Zen Observable has its own error function, so in order to log correctly\n            // we need to provide a custom error callback.\n            try {\n                var subObserver = observer._subscription._observer;\n                if (subObserver && !subObserver.error) {\n                    subObserver.error = defaultSubscriptionObserverErrorCallback;\n                }\n            }\n            catch (_a) { }\n            var first = !_this.observers.size;\n            _this.observers.add(observer);\n            // Deliver most recent error or result.\n            var last = _this.last;\n            if (last && last.error) {\n                observer.error && observer.error(last.error);\n            }\n            else if (last && last.result) {\n                observer.next && observer.next(_this.maskResult(last.result));\n            }\n            // Initiate observation of this query if it hasn't been reported to\n            // the QueryManager yet.\n            if (first) {\n                // Blindly catching here prevents unhandled promise rejections,\n                // and is safe because the ObservableQuery handles this error with\n                // this.observer.error, so we're not just swallowing the error by\n                // ignoring it here.\n                _this.reobserve().catch(function () { });\n            }\n            return function () {\n                if (_this.observers.delete(observer) && !_this.observers.size) {\n                    _this.tearDownQuery();\n                }\n            };\n        }) || this;\n        _this.observers = new Set();\n        _this.subscriptions = new Set();\n        // related classes\n        _this.queryInfo = queryInfo;\n        _this.queryManager = queryManager;\n        // active state\n        _this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy);\n        _this.isTornDown = false;\n        _this.subscribeToMore = _this.subscribeToMore.bind(_this);\n        _this.maskResult = _this.maskResult.bind(_this);\n        var _b = queryManager.defaultOptions.watchQuery, _c = _b === void 0 ? {} : _b, _d = _c.fetchPolicy, defaultFetchPolicy = _d === void 0 ? \"cache-first\" : _d;\n        var _e = options.fetchPolicy, fetchPolicy = _e === void 0 ? defaultFetchPolicy : _e, \n        // Make sure we don't store \"standby\" as the initialFetchPolicy.\n        _f = options.initialFetchPolicy, \n        // Make sure we don't store \"standby\" as the initialFetchPolicy.\n        initialFetchPolicy = _f === void 0 ? fetchPolicy === \"standby\" ? defaultFetchPolicy : (fetchPolicy) : _f;\n        _this.options = __assign(__assign({}, options), { \n            // Remember the initial options.fetchPolicy so we can revert back to this\n            // policy when variables change. This information can also be specified\n            // (or overridden) by providing options.initialFetchPolicy explicitly.\n            initialFetchPolicy: initialFetchPolicy, \n            // This ensures this.options.fetchPolicy always has a string value, in\n            // case options.fetchPolicy was not provided.\n            fetchPolicy: fetchPolicy });\n        _this.queryId = queryInfo.queryId || queryManager.generateQueryId();\n        var opDef = getOperationDefinition(_this.query);\n        _this.queryName = opDef && opDef.name && opDef.name.value;\n        return _this;\n    }\n    Object.defineProperty(ObservableQuery.prototype, \"query\", {\n        // The `query` computed property will always reflect the document transformed\n        // by the last run query. `this.options.query` will always reflect the raw\n        // untransformed query to ensure document transforms with runtime conditionals\n        // are run on the original document.\n        get: function () {\n            return this.lastQuery || this.options.query;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ObservableQuery.prototype, \"variables\", {\n        // Computed shorthand for this.options.variables, preserved for\n        // backwards compatibility.\n        /**\n         * An object containing the variables that were provided for the query.\n         */\n        get: function () {\n            return this.options.variables;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ObservableQuery.prototype.result = function () {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            // TODO: this code doesn’t actually make sense insofar as the observer\n            // will never exist in this.observers due how zen-observable wraps observables.\n            // https://github.com/zenparsing/zen-observable/blob/master/src/Observable.js#L169\n            var observer = {\n                next: function (result) {\n                    resolve(result);\n                    // Stop the query within the QueryManager if we can before\n                    // this function returns.\n                    //\n                    // We do this in order to prevent observers piling up within\n                    // the QueryManager. Notice that we only fully unsubscribe\n                    // from the subscription in a setTimeout(..., 0)  call. This call can\n                    // actually be handled by the browser at a much later time. If queries\n                    // are fired in the meantime, observers that should have been removed\n                    // from the QueryManager will continue to fire, causing an unnecessary\n                    // performance hit.\n                    _this.observers.delete(observer);\n                    if (!_this.observers.size) {\n                        _this.queryManager.removeQuery(_this.queryId);\n                    }\n                    setTimeout(function () {\n                        subscription.unsubscribe();\n                    }, 0);\n                },\n                error: reject,\n            };\n            var subscription = _this.subscribe(observer);\n        });\n    };\n    /** @internal */\n    ObservableQuery.prototype.resetDiff = function () {\n        this.queryInfo.resetDiff();\n    };\n    ObservableQuery.prototype.getCurrentFullResult = function (saveAsLastResult) {\n        if (saveAsLastResult === void 0) { saveAsLastResult = true; }\n        // Use the last result as long as the variables match this.variables.\n        var lastResult = this.getLastResult(true);\n        var networkStatus = this.queryInfo.networkStatus ||\n            (lastResult && lastResult.networkStatus) ||\n            NetworkStatus.ready;\n        var result = __assign(__assign({}, lastResult), { loading: isNetworkRequestInFlight(networkStatus), networkStatus: networkStatus });\n        var _a = this.options.fetchPolicy, fetchPolicy = _a === void 0 ? \"cache-first\" : _a;\n        if (\n        // These fetch policies should never deliver data from the cache, unless\n        // redelivering a previously delivered result.\n        skipCacheDataFor(fetchPolicy) ||\n            // If this.options.query has @client(always: true) fields, we cannot\n            // trust diff.result, since it was read from the cache without running\n            // local resolvers (and it's too late to run resolvers now, since we must\n            // return a result synchronously).\n            this.queryManager.getDocumentInfo(this.query).hasForcedResolvers) {\n            // Fall through.\n        }\n        else if (this.waitForOwnResult) {\n            // This would usually be a part of `QueryInfo.getDiff()`.\n            // which we skip in the waitForOwnResult case since we are not\n            // interested in the diff.\n            this.queryInfo[\"updateWatch\"]();\n        }\n        else {\n            var diff = this.queryInfo.getDiff();\n            if (diff.complete || this.options.returnPartialData) {\n                result.data = diff.result;\n            }\n            if (equal(result.data, {})) {\n                result.data = void 0;\n            }\n            if (diff.complete) {\n                // Similar to setting result.partial to false, but taking advantage of the\n                // falsiness of missing fields.\n                delete result.partial;\n                // If the diff is complete, and we're using a FetchPolicy that\n                // terminates after a complete cache read, we can assume the next result\n                // we receive will have NetworkStatus.ready and !loading.\n                if (diff.complete &&\n                    result.networkStatus === NetworkStatus.loading &&\n                    (fetchPolicy === \"cache-first\" || fetchPolicy === \"cache-only\")) {\n                    result.networkStatus = NetworkStatus.ready;\n                    result.loading = false;\n                }\n            }\n            else {\n                result.partial = true;\n            }\n            if (globalThis.__DEV__ !== false &&\n                !diff.complete &&\n                !this.options.partialRefetch &&\n                !result.loading &&\n                !result.data &&\n                !result.error) {\n                logMissingFieldErrors(diff.missing);\n            }\n        }\n        if (saveAsLastResult) {\n            this.updateLastResult(result);\n        }\n        return result;\n    };\n    ObservableQuery.prototype.getCurrentResult = function (saveAsLastResult) {\n        if (saveAsLastResult === void 0) { saveAsLastResult = true; }\n        return this.maskResult(this.getCurrentFullResult(saveAsLastResult));\n    };\n    // Compares newResult to the snapshot we took of this.lastResult when it was\n    // first received.\n    ObservableQuery.prototype.isDifferentFromLastResult = function (newResult, variables) {\n        if (!this.last) {\n            return true;\n        }\n        var documentInfo = this.queryManager.getDocumentInfo(this.query);\n        var dataMasking = this.queryManager.dataMasking;\n        var query = dataMasking ? documentInfo.nonReactiveQuery : this.query;\n        var resultIsDifferent = dataMasking || documentInfo.hasNonreactiveDirective ?\n            !equalByQuery(query, this.last.result, newResult, this.variables)\n            : !equal(this.last.result, newResult);\n        return (resultIsDifferent || (variables && !equal(this.last.variables, variables)));\n    };\n    ObservableQuery.prototype.getLast = function (key, variablesMustMatch) {\n        var last = this.last;\n        if (last &&\n            last[key] &&\n            (!variablesMustMatch || equal(last.variables, this.variables))) {\n            return last[key];\n        }\n    };\n    ObservableQuery.prototype.getLastResult = function (variablesMustMatch) {\n        return this.getLast(\"result\", variablesMustMatch);\n    };\n    ObservableQuery.prototype.getLastError = function (variablesMustMatch) {\n        return this.getLast(\"error\", variablesMustMatch);\n    };\n    ObservableQuery.prototype.resetLastResults = function () {\n        delete this.last;\n        this.isTornDown = false;\n    };\n    ObservableQuery.prototype.resetQueryStoreErrors = function () {\n        this.queryManager.resetErrors(this.queryId);\n    };\n    /**\n     * Update the variables of this observable query, and fetch the new results.\n     * This method should be preferred over `setVariables` in most use cases.\n     *\n     * @param variables - The new set of variables. If there are missing variables,\n     * the previous values of those variables will be used.\n     */\n    ObservableQuery.prototype.refetch = function (variables) {\n        var _a;\n        var reobserveOptions = {\n            // Always disable polling for refetches.\n            pollInterval: 0,\n        };\n        // Unless the provided fetchPolicy always consults the network\n        // (no-cache, network-only, or cache-and-network), override it with\n        // network-only to force the refetch for this fetchQuery call.\n        var fetchPolicy = this.options.fetchPolicy;\n        if (fetchPolicy === \"cache-and-network\") {\n            reobserveOptions.fetchPolicy = fetchPolicy;\n        }\n        else if (fetchPolicy === \"no-cache\") {\n            reobserveOptions.fetchPolicy = \"no-cache\";\n        }\n        else {\n            reobserveOptions.fetchPolicy = \"network-only\";\n        }\n        if (globalThis.__DEV__ !== false && variables && hasOwnProperty.call(variables, \"variables\")) {\n            var queryDef = getQueryDefinition(this.query);\n            var vars = queryDef.variableDefinitions;\n            if (!vars || !vars.some(function (v) { return v.variable.name.value === \"variables\"; })) {\n                globalThis.__DEV__ !== false && invariant.warn(\n                    21,\n                    variables,\n                    ((_a = queryDef.name) === null || _a === void 0 ? void 0 : _a.value) || queryDef\n                );\n            }\n        }\n        if (variables && !equal(this.options.variables, variables)) {\n            // Update the existing options with new variables\n            reobserveOptions.variables = this.options.variables = __assign(__assign({}, this.options.variables), variables);\n        }\n        this.queryInfo.resetLastWrite();\n        return this.reobserve(reobserveOptions, NetworkStatus.refetch);\n    };\n    /**\n     * A function that helps you fetch the next set of results for a [paginated list field](https://www.apollographql.com/docs/react/pagination/core-api/).\n     */\n    ObservableQuery.prototype.fetchMore = function (fetchMoreOptions) {\n        var _this = this;\n        var combinedOptions = __assign(__assign({}, (fetchMoreOptions.query ? fetchMoreOptions : (__assign(__assign(__assign(__assign({}, this.options), { query: this.options.query }), fetchMoreOptions), { variables: __assign(__assign({}, this.options.variables), fetchMoreOptions.variables) })))), { \n            // The fetchMore request goes immediately to the network and does\n            // not automatically write its result to the cache (hence no-cache\n            // instead of network-only), because we allow the caller of\n            // fetchMore to provide an updateQuery callback that determines how\n            // the data gets written to the cache.\n            fetchPolicy: \"no-cache\" });\n        combinedOptions.query = this.transformDocument(combinedOptions.query);\n        var qid = this.queryManager.generateQueryId();\n        // If a temporary query is passed to `fetchMore`, we don't want to store\n        // it as the last query result since it may be an optimized query for\n        // pagination. We will however run the transforms on the original document\n        // as well as the document passed in `fetchMoreOptions` to ensure the cache\n        // uses the most up-to-date document which may rely on runtime conditionals.\n        this.lastQuery =\n            fetchMoreOptions.query ?\n                this.transformDocument(this.options.query)\n                : combinedOptions.query;\n        // Simulate a loading result for the original query with\n        // result.networkStatus === NetworkStatus.fetchMore.\n        var queryInfo = this.queryInfo;\n        var originalNetworkStatus = queryInfo.networkStatus;\n        queryInfo.networkStatus = NetworkStatus.fetchMore;\n        if (combinedOptions.notifyOnNetworkStatusChange) {\n            this.observe();\n        }\n        var updatedQuerySet = new Set();\n        var updateQuery = fetchMoreOptions === null || fetchMoreOptions === void 0 ? void 0 : fetchMoreOptions.updateQuery;\n        var isCached = this.options.fetchPolicy !== \"no-cache\";\n        if (!isCached) {\n            invariant(updateQuery, 22);\n        }\n        return this.queryManager\n            .fetchQuery(qid, combinedOptions, NetworkStatus.fetchMore)\n            .then(function (fetchMoreResult) {\n            _this.queryManager.removeQuery(qid);\n            if (queryInfo.networkStatus === NetworkStatus.fetchMore) {\n                queryInfo.networkStatus = originalNetworkStatus;\n            }\n            if (isCached) {\n                // Performing this cache update inside a cache.batch transaction ensures\n                // any affected cache.watch watchers are notified at most once about any\n                // updates. Most watchers will be using the QueryInfo class, which\n                // responds to notifications by calling reobserveCacheFirst to deliver\n                // fetchMore cache results back to this ObservableQuery.\n                _this.queryManager.cache.batch({\n                    update: function (cache) {\n                        var updateQuery = fetchMoreOptions.updateQuery;\n                        if (updateQuery) {\n                            cache.updateQuery({\n                                query: _this.query,\n                                variables: _this.variables,\n                                returnPartialData: true,\n                                optimistic: false,\n                            }, function (previous) {\n                                return updateQuery(previous, {\n                                    fetchMoreResult: fetchMoreResult.data,\n                                    variables: combinedOptions.variables,\n                                });\n                            });\n                        }\n                        else {\n                            // If we're using a field policy instead of updateQuery, the only\n                            // thing we need to do is write the new data to the cache using\n                            // combinedOptions.variables (instead of this.variables, which is\n                            // what this.updateQuery uses, because it works by abusing the\n                            // original field value, keyed by the original variables).\n                            cache.writeQuery({\n                                query: combinedOptions.query,\n                                variables: combinedOptions.variables,\n                                data: fetchMoreResult.data,\n                            });\n                        }\n                    },\n                    onWatchUpdated: function (watch) {\n                        // Record the DocumentNode associated with any watched query whose\n                        // data were updated by the cache writes above.\n                        updatedQuerySet.add(watch.query);\n                    },\n                });\n            }\n            else {\n                // There is a possibility `lastResult` may not be set when\n                // `fetchMore` is called which would cause this to crash. This should\n                // only happen if we haven't previously reported a result. We don't\n                // quite know what the right behavior should be here since this block\n                // of code runs after the fetch result has executed on the network.\n                // We plan to let it crash in the meantime.\n                //\n                // If we get bug reports due to the `data` property access on\n                // undefined, this should give us a real-world scenario that we can\n                // use to test against and determine the right behavior. If we do end\n                // up changing this behavior, this may require, for example, an\n                // adjustment to the types on `updateQuery` since that function\n                // expects that the first argument always contains previous result\n                // data, but not `undefined`.\n                var lastResult = _this.getLast(\"result\");\n                var data = updateQuery(lastResult.data, {\n                    fetchMoreResult: fetchMoreResult.data,\n                    variables: combinedOptions.variables,\n                });\n                _this.reportResult(__assign(__assign({}, lastResult), { data: data }), _this.variables);\n            }\n            return _this.maskResult(fetchMoreResult);\n        })\n            .finally(function () {\n            // In case the cache writes above did not generate a broadcast\n            // notification (which would have been intercepted by onWatchUpdated),\n            // likely because the written data were the same as what was already in\n            // the cache, we still want fetchMore to deliver its final loading:false\n            // result with the unchanged data.\n            if (isCached && !updatedQuerySet.has(_this.query)) {\n                reobserveCacheFirst(_this);\n            }\n        });\n    };\n    // XXX the subscription variables are separate from the query variables.\n    // if you want to update subscription variables, right now you have to do that separately,\n    // and you can only do it by stopping the subscription and then subscribing again with new variables.\n    /**\n     * A function that enables you to execute a [subscription](https://www.apollographql.com/docs/react/data/subscriptions/), usually to subscribe to specific fields that were included in the query.\n     *\n     * This function returns _another_ function that you can call to terminate the subscription.\n     */\n    ObservableQuery.prototype.subscribeToMore = function (options) {\n        var _this = this;\n        var subscription = this.queryManager\n            .startGraphQLSubscription({\n            query: options.document,\n            variables: options.variables,\n            context: options.context,\n        })\n            .subscribe({\n            next: function (subscriptionData) {\n                var updateQuery = options.updateQuery;\n                if (updateQuery) {\n                    _this.updateQuery(function (previous, _a) {\n                        var variables = _a.variables;\n                        return updateQuery(previous, {\n                            subscriptionData: subscriptionData,\n                            variables: variables,\n                        });\n                    });\n                }\n            },\n            error: function (err) {\n                if (options.onError) {\n                    options.onError(err);\n                    return;\n                }\n                globalThis.__DEV__ !== false && invariant.error(23, err);\n            },\n        });\n        this.subscriptions.add(subscription);\n        return function () {\n            if (_this.subscriptions.delete(subscription)) {\n                subscription.unsubscribe();\n            }\n        };\n    };\n    ObservableQuery.prototype.setOptions = function (newOptions) {\n        return this.reobserve(newOptions);\n    };\n    ObservableQuery.prototype.silentSetOptions = function (newOptions) {\n        var mergedOptions = compact(this.options, newOptions || {});\n        assign(this.options, mergedOptions);\n    };\n    /**\n     * Update the variables of this observable query, and fetch the new results\n     * if they've changed. Most users should prefer `refetch` instead of\n     * `setVariables` in order to to be properly notified of results even when\n     * they come from the cache.\n     *\n     * Note: the `next` callback will *not* fire if the variables have not changed\n     * or if the result is coming from cache.\n     *\n     * Note: the promise will return the old results immediately if the variables\n     * have not changed.\n     *\n     * Note: the promise will return null immediately if the query is not active\n     * (there are no subscribers).\n     *\n     * @param variables - The new set of variables. If there are missing variables,\n     * the previous values of those variables will be used.\n     */\n    ObservableQuery.prototype.setVariables = function (variables) {\n        if (equal(this.variables, variables)) {\n            // If we have no observers, then we don't actually want to make a network\n            // request. As soon as someone observes the query, the request will kick\n            // off. For now, we just store any changes. (See #1077)\n            return this.observers.size ? this.result() : Promise.resolve();\n        }\n        this.options.variables = variables;\n        // See comment above\n        if (!this.observers.size) {\n            return Promise.resolve();\n        }\n        return this.reobserve({\n            // Reset options.fetchPolicy to its original value.\n            fetchPolicy: this.options.initialFetchPolicy,\n            variables: variables,\n        }, NetworkStatus.setVariables);\n    };\n    /**\n     * A function that enables you to update the query's cached result without executing a followup GraphQL operation.\n     *\n     * See [using updateQuery and updateFragment](https://www.apollographql.com/docs/react/caching/cache-interaction/#using-updatequery-and-updatefragment) for additional information.\n     */\n    ObservableQuery.prototype.updateQuery = function (mapFn) {\n        var queryManager = this.queryManager;\n        var result = queryManager.cache.diff({\n            query: this.options.query,\n            variables: this.variables,\n            returnPartialData: true,\n            optimistic: false,\n        }).result;\n        var newResult = mapFn(result, {\n            variables: this.variables,\n        });\n        if (newResult) {\n            queryManager.cache.writeQuery({\n                query: this.options.query,\n                data: newResult,\n                variables: this.variables,\n            });\n            queryManager.broadcastQueries();\n        }\n    };\n    /**\n     * A function that instructs the query to begin re-executing at a specified interval (in milliseconds).\n     */\n    ObservableQuery.prototype.startPolling = function (pollInterval) {\n        this.options.pollInterval = pollInterval;\n        this.updatePolling();\n    };\n    /**\n     * A function that instructs the query to stop polling after a previous call to `startPolling`.\n     */\n    ObservableQuery.prototype.stopPolling = function () {\n        this.options.pollInterval = 0;\n        this.updatePolling();\n    };\n    // Update options.fetchPolicy according to options.nextFetchPolicy.\n    ObservableQuery.prototype.applyNextFetchPolicy = function (reason, \n    // It's possible to use this method to apply options.nextFetchPolicy to\n    // options.fetchPolicy even if options !== this.options, though that happens\n    // most often when the options are temporary, used for only one request and\n    // then thrown away, so nextFetchPolicy may not end up mattering.\n    options) {\n        if (options.nextFetchPolicy) {\n            var _a = options.fetchPolicy, fetchPolicy = _a === void 0 ? \"cache-first\" : _a, _b = options.initialFetchPolicy, initialFetchPolicy = _b === void 0 ? fetchPolicy : _b;\n            if (fetchPolicy === \"standby\") {\n                // Do nothing, leaving options.fetchPolicy unchanged.\n            }\n            else if (typeof options.nextFetchPolicy === \"function\") {\n                // When someone chooses \"cache-and-network\" or \"network-only\" as their\n                // initial FetchPolicy, they often do not want future cache updates to\n                // trigger unconditional network requests, which is what repeatedly\n                // applying the \"cache-and-network\" or \"network-only\" policies would\n                // seem to imply. Instead, when the cache reports an update after the\n                // initial network request, it may be desirable for subsequent network\n                // requests to be triggered only if the cache result is incomplete. To\n                // that end, the options.nextFetchPolicy option provides an easy way to\n                // update options.fetchPolicy after the initial network request, without\n                // having to call observableQuery.setOptions.\n                options.fetchPolicy = options.nextFetchPolicy(fetchPolicy, {\n                    reason: reason,\n                    options: options,\n                    observable: this,\n                    initialFetchPolicy: initialFetchPolicy,\n                });\n            }\n            else if (reason === \"variables-changed\") {\n                options.fetchPolicy = initialFetchPolicy;\n            }\n            else {\n                options.fetchPolicy = options.nextFetchPolicy;\n            }\n        }\n        return options.fetchPolicy;\n    };\n    ObservableQuery.prototype.fetch = function (options, newNetworkStatus, query) {\n        // TODO Make sure we update the networkStatus (and infer fetchVariables)\n        // before actually committing to the fetch.\n        this.queryManager.setObservableQuery(this);\n        return this.queryManager[\"fetchConcastWithInfo\"](this.queryId, options, newNetworkStatus, query);\n    };\n    // Turns polling on or off based on this.options.pollInterval.\n    ObservableQuery.prototype.updatePolling = function () {\n        var _this = this;\n        // Avoid polling in SSR mode\n        if (this.queryManager.ssrMode) {\n            return;\n        }\n        var _a = this, pollingInfo = _a.pollingInfo, pollInterval = _a.options.pollInterval;\n        if (!pollInterval || !this.hasObservers()) {\n            if (pollingInfo) {\n                clearTimeout(pollingInfo.timeout);\n                delete this.pollingInfo;\n            }\n            return;\n        }\n        if (pollingInfo && pollingInfo.interval === pollInterval) {\n            return;\n        }\n        invariant(pollInterval, 24);\n        var info = pollingInfo || (this.pollingInfo = {});\n        info.interval = pollInterval;\n        var maybeFetch = function () {\n            var _a, _b;\n            if (_this.pollingInfo) {\n                if (!isNetworkRequestInFlight(_this.queryInfo.networkStatus) &&\n                    !((_b = (_a = _this.options).skipPollAttempt) === null || _b === void 0 ? void 0 : _b.call(_a))) {\n                    _this.reobserve({\n                        // Most fetchPolicy options don't make sense to use in a polling context, as\n                        // users wouldn't want to be polling the cache directly. However, network-only and\n                        // no-cache are both useful for when the user wants to control whether or not the\n                        // polled results are written to the cache.\n                        fetchPolicy: _this.options.initialFetchPolicy === \"no-cache\" ?\n                            \"no-cache\"\n                            : \"network-only\",\n                    }, NetworkStatus.poll).then(poll, poll);\n                }\n                else {\n                    poll();\n                }\n            }\n        };\n        var poll = function () {\n            var info = _this.pollingInfo;\n            if (info) {\n                clearTimeout(info.timeout);\n                info.timeout = setTimeout(maybeFetch, info.interval);\n            }\n        };\n        poll();\n    };\n    ObservableQuery.prototype.updateLastResult = function (newResult, variables) {\n        if (variables === void 0) { variables = this.variables; }\n        var error = this.getLastError();\n        // Preserve this.last.error unless the variables have changed.\n        if (error && this.last && !equal(variables, this.last.variables)) {\n            error = void 0;\n        }\n        return (this.last = __assign({ result: this.queryManager.assumeImmutableResults ?\n                newResult\n                : cloneDeep(newResult), variables: variables }, (error ? { error: error } : null)));\n    };\n    ObservableQuery.prototype.reobserveAsConcast = function (newOptions, newNetworkStatus) {\n        var _this = this;\n        this.isTornDown = false;\n        var useDisposableConcast = \n        // Refetching uses a disposable Concast to allow refetches using different\n        // options/variables, without permanently altering the options of the\n        // original ObservableQuery.\n        newNetworkStatus === NetworkStatus.refetch ||\n            // The fetchMore method does not actually call the reobserve method, but,\n            // if it did, it would definitely use a disposable Concast.\n            newNetworkStatus === NetworkStatus.fetchMore ||\n            // Polling uses a disposable Concast so the polling options (which force\n            // fetchPolicy to be \"network-only\" or \"no-cache\") won't override the original options.\n            newNetworkStatus === NetworkStatus.poll;\n        // Save the old variables, since Object.assign may modify them below.\n        var oldVariables = this.options.variables;\n        var oldFetchPolicy = this.options.fetchPolicy;\n        var mergedOptions = compact(this.options, newOptions || {});\n        var options = useDisposableConcast ?\n            // Disposable Concast fetches receive a shallow copy of this.options\n            // (merged with newOptions), leaving this.options unmodified.\n            mergedOptions\n            : assign(this.options, mergedOptions);\n        // Don't update options.query with the transformed query to avoid\n        // overwriting this.options.query when we aren't using a disposable concast.\n        // We want to ensure we can re-run the custom document transforms the next\n        // time a request is made against the original query.\n        var query = this.transformDocument(options.query);\n        this.lastQuery = query;\n        if (!useDisposableConcast) {\n            // We can skip calling updatePolling if we're not changing this.options.\n            this.updatePolling();\n            // Reset options.fetchPolicy to its original value when variables change,\n            // unless a new fetchPolicy was provided by newOptions.\n            if (newOptions &&\n                newOptions.variables &&\n                !equal(newOptions.variables, oldVariables) &&\n                // Don't mess with the fetchPolicy if it's currently \"standby\".\n                options.fetchPolicy !== \"standby\" &&\n                // If we're changing the fetchPolicy anyway, don't try to change it here\n                // using applyNextFetchPolicy. The explicit options.fetchPolicy wins.\n                (options.fetchPolicy === oldFetchPolicy ||\n                    // A `nextFetchPolicy` function has even higher priority, though,\n                    // so in that case `applyNextFetchPolicy` must be called.\n                    typeof options.nextFetchPolicy === \"function\")) {\n                this.applyNextFetchPolicy(\"variables-changed\", options);\n                if (newNetworkStatus === void 0) {\n                    newNetworkStatus = NetworkStatus.setVariables;\n                }\n            }\n        }\n        this.waitForOwnResult && (this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy));\n        var finishWaitingForOwnResult = function () {\n            if (_this.concast === concast) {\n                _this.waitForOwnResult = false;\n            }\n        };\n        var variables = options.variables && __assign({}, options.variables);\n        var _a = this.fetch(options, newNetworkStatus, query), concast = _a.concast, fromLink = _a.fromLink;\n        var observer = {\n            next: function (result) {\n                if (equal(_this.variables, variables)) {\n                    finishWaitingForOwnResult();\n                    _this.reportResult(result, variables);\n                }\n            },\n            error: function (error) {\n                if (equal(_this.variables, variables)) {\n                    // Coming from `getResultsFromLink`, `error` here should always be an `ApolloError`.\n                    // However, calling `concast.cancel` can inject another type of error, so we have to\n                    // wrap it again here.\n                    if (!isApolloError(error)) {\n                        error = new ApolloError({ networkError: error });\n                    }\n                    finishWaitingForOwnResult();\n                    _this.reportError(error, variables);\n                }\n            },\n        };\n        if (!useDisposableConcast && (fromLink || !this.concast)) {\n            // We use the {add,remove}Observer methods directly to avoid wrapping\n            // observer with an unnecessary SubscriptionObserver object.\n            if (this.concast && this.observer) {\n                this.concast.removeObserver(this.observer);\n            }\n            this.concast = concast;\n            this.observer = observer;\n        }\n        concast.addObserver(observer);\n        return concast;\n    };\n    ObservableQuery.prototype.reobserve = function (newOptions, newNetworkStatus) {\n        return preventUnhandledRejection(this.reobserveAsConcast(newOptions, newNetworkStatus).promise.then(this.maskResult));\n    };\n    ObservableQuery.prototype.resubscribeAfterError = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        // If `lastError` is set in the current when the subscription is re-created,\n        // the subscription will immediately receive the error, which will\n        // cause it to terminate again. To avoid this, we first clear\n        // the last error/result from the `observableQuery` before re-starting\n        // the subscription, and restore the last value afterwards so that the\n        // subscription has a chance to stay open.\n        var last = this.last;\n        this.resetLastResults();\n        var subscription = this.subscribe.apply(this, args);\n        this.last = last;\n        return subscription;\n    };\n    // (Re)deliver the current result to this.observers without applying fetch\n    // policies or making network requests.\n    ObservableQuery.prototype.observe = function () {\n        this.reportResult(\n        // Passing false is important so that this.getCurrentResult doesn't\n        // save the fetchMore result as this.lastResult, causing it to be\n        // ignored due to the this.isDifferentFromLastResult check in\n        // this.reportResult.\n        this.getCurrentFullResult(false), this.variables);\n    };\n    ObservableQuery.prototype.reportResult = function (result, variables) {\n        var lastError = this.getLastError();\n        var isDifferent = this.isDifferentFromLastResult(result, variables);\n        // Update the last result even when isDifferentFromLastResult returns false,\n        // because the query may be using the @nonreactive directive, and we want to\n        // save the the latest version of any nonreactive subtrees (in case\n        // getCurrentResult is called), even though we skip broadcasting changes.\n        if (lastError || !result.partial || this.options.returnPartialData) {\n            this.updateLastResult(result, variables);\n        }\n        if (lastError || isDifferent) {\n            iterateObserversSafely(this.observers, \"next\", this.maskResult(result));\n        }\n    };\n    ObservableQuery.prototype.reportError = function (error, variables) {\n        // Since we don't get the current result on errors, only the error, we\n        // must mirror the updates that occur in QueryStore.markQueryError here\n        var errorResult = __assign(__assign({}, this.getLastResult()), { error: error, errors: error.graphQLErrors, networkStatus: NetworkStatus.error, loading: false });\n        this.updateLastResult(errorResult, variables);\n        iterateObserversSafely(this.observers, \"error\", (this.last.error = error));\n    };\n    ObservableQuery.prototype.hasObservers = function () {\n        return this.observers.size > 0;\n    };\n    ObservableQuery.prototype.tearDownQuery = function () {\n        if (this.isTornDown)\n            return;\n        if (this.concast && this.observer) {\n            this.concast.removeObserver(this.observer);\n            delete this.concast;\n            delete this.observer;\n        }\n        this.stopPolling();\n        // stop all active GraphQL subscriptions\n        this.subscriptions.forEach(function (sub) { return sub.unsubscribe(); });\n        this.subscriptions.clear();\n        this.queryManager.stopQuery(this.queryId);\n        this.observers.clear();\n        this.isTornDown = true;\n    };\n    ObservableQuery.prototype.transformDocument = function (document) {\n        return this.queryManager.transform(document);\n    };\n    ObservableQuery.prototype.maskResult = function (result) {\n        return result && \"data\" in result ? __assign(__assign({}, result), { data: this.queryManager.maskOperation({\n                document: this.query,\n                data: result.data,\n                fetchPolicy: this.options.fetchPolicy,\n                id: this.queryId,\n            }) }) : result;\n    };\n    return ObservableQuery;\n}(Observable));\nexport { ObservableQuery };\n// Necessary because the ObservableQuery constructor has a different\n// signature than the Observable constructor.\nfixObservableSubclass(ObservableQuery);\n// Reobserve with fetchPolicy effectively set to \"cache-first\", triggering\n// delivery of any new data from the cache, possibly falling back to the network\n// if any cache data are missing. This allows _complete_ cache results to be\n// delivered without also kicking off unnecessary network requests when\n// this.options.fetchPolicy is \"cache-and-network\" or \"network-only\". When\n// this.options.fetchPolicy is any other policy (\"cache-first\", \"cache-only\",\n// \"standby\", or \"no-cache\"), we call this.reobserve() as usual.\nexport function reobserveCacheFirst(obsQuery) {\n    var _a = obsQuery.options, fetchPolicy = _a.fetchPolicy, nextFetchPolicy = _a.nextFetchPolicy;\n    if (fetchPolicy === \"cache-and-network\" || fetchPolicy === \"network-only\") {\n        return obsQuery.reobserve({\n            fetchPolicy: \"cache-first\",\n            // Use a temporary nextFetchPolicy function that replaces itself with the\n            // previous nextFetchPolicy value and returns the original fetchPolicy.\n            nextFetchPolicy: function (currentFetchPolicy, context) {\n                // Replace this nextFetchPolicy function in the options object with the\n                // original this.options.nextFetchPolicy value.\n                this.nextFetchPolicy = nextFetchPolicy;\n                // If the original nextFetchPolicy value was a function, give it a\n                // chance to decide what happens here.\n                if (typeof this.nextFetchPolicy === \"function\") {\n                    return this.nextFetchPolicy(currentFetchPolicy, context);\n                }\n                // Otherwise go back to the original this.options.fetchPolicy.\n                return fetchPolicy;\n            },\n        });\n    }\n    return obsQuery.reobserve();\n}\nfunction defaultSubscriptionObserverErrorCallback(error) {\n    globalThis.__DEV__ !== false && invariant.error(25, error.message, error.stack);\n}\nexport function logMissingFieldErrors(missing) {\n    if (globalThis.__DEV__ !== false && missing) {\n        globalThis.__DEV__ !== false && invariant.debug(26, missing);\n    }\n}\nfunction skipCacheDataFor(fetchPolicy /* `undefined` would mean `\"cache-first\"` */) {\n    return (fetchPolicy === \"network-only\" ||\n        fetchPolicy === \"no-cache\" ||\n        fetchPolicy === \"standby\");\n}\n//# sourceMappingURL=ObservableQuery.js.map", "import { __assign } from \"tslib\";\nimport { equal } from \"@wry/equality\";\nimport { DeepMerger } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/index.js\";\nimport { reobserveCacheFirst } from \"./ObservableQuery.js\";\nimport { isNonEmptyArray, graphQLResultHasError, canUseWeakMap, } from \"../utilities/index.js\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nvar destructiveMethodCounts = new (canUseWeakMap ? WeakMap : Map)();\nfunction wrapDestructiveCacheMethod(cache, methodName) {\n    var original = cache[methodName];\n    if (typeof original === \"function\") {\n        // @ts-expect-error this is just too generic to be typed correctly\n        cache[methodName] = function () {\n            destructiveMethodCounts.set(cache, \n            // The %1e15 allows the count to wrap around to 0 safely every\n            // quadrillion evictions, so there's no risk of overflow. To be\n            // clear, this is more of a pedantic principle than something\n            // that matters in any conceivable practical scenario.\n            (destructiveMethodCounts.get(cache) + 1) % 1e15);\n            // @ts-expect-error this is just too generic to be typed correctly\n            return original.apply(this, arguments);\n        };\n    }\n}\nfunction cancelNotifyTimeout(info) {\n    if (info[\"notifyTimeout\"]) {\n        clearTimeout(info[\"notifyTimeout\"]);\n        info[\"notifyTimeout\"] = void 0;\n    }\n}\n// A QueryInfo object represents a single query managed by the\n// QueryManager, which tracks all QueryInfo objects by queryId in its\n// this.queries Map. QueryInfo objects store the latest results and errors\n// for the given query, and are responsible for reporting those results to\n// the corresponding ObservableQuery, via the QueryInfo.notify method.\n// Results are reported asynchronously whenever setDiff marks the\n// QueryInfo object as dirty, though a call to the QueryManager's\n// broadcastQueries method may trigger the notification before it happens\n// automatically. This class used to be a simple interface type without\n// any field privacy or meaningful methods, which is why it still has so\n// many public fields. The effort to lock down and simplify the QueryInfo\n// interface is ongoing, and further improvements are welcome.\nvar QueryInfo = /** @class */ (function () {\n    function QueryInfo(queryManager, queryId) {\n        if (queryId === void 0) { queryId = queryManager.generateQueryId(); }\n        this.queryId = queryId;\n        this.listeners = new Set();\n        this.document = null;\n        this.lastRequestId = 1;\n        this.stopped = false;\n        this.dirty = false;\n        this.observableQuery = null;\n        var cache = (this.cache = queryManager.cache);\n        // Track how often cache.evict is called, since we want eviction to\n        // override the feud-stopping logic in the markResult method, by\n        // causing shouldWrite to return true. Wrapping the cache.evict method\n        // is a bit of a hack, but it saves us from having to make eviction\n        // counting an official part of the ApolloCache API.\n        if (!destructiveMethodCounts.has(cache)) {\n            destructiveMethodCounts.set(cache, 0);\n            wrapDestructiveCacheMethod(cache, \"evict\");\n            wrapDestructiveCacheMethod(cache, \"modify\");\n            wrapDestructiveCacheMethod(cache, \"reset\");\n        }\n    }\n    QueryInfo.prototype.init = function (query) {\n        var networkStatus = query.networkStatus || NetworkStatus.loading;\n        if (this.variables &&\n            this.networkStatus !== NetworkStatus.loading &&\n            !equal(this.variables, query.variables)) {\n            networkStatus = NetworkStatus.setVariables;\n        }\n        if (!equal(query.variables, this.variables)) {\n            this.lastDiff = void 0;\n        }\n        Object.assign(this, {\n            document: query.document,\n            variables: query.variables,\n            networkError: null,\n            graphQLErrors: this.graphQLErrors || [],\n            networkStatus: networkStatus,\n        });\n        if (query.observableQuery) {\n            this.setObservableQuery(query.observableQuery);\n        }\n        if (query.lastRequestId) {\n            this.lastRequestId = query.lastRequestId;\n        }\n        return this;\n    };\n    QueryInfo.prototype.reset = function () {\n        cancelNotifyTimeout(this);\n        this.dirty = false;\n    };\n    QueryInfo.prototype.resetDiff = function () {\n        this.lastDiff = void 0;\n    };\n    QueryInfo.prototype.getDiff = function () {\n        var options = this.getDiffOptions();\n        if (this.lastDiff && equal(options, this.lastDiff.options)) {\n            return this.lastDiff.diff;\n        }\n        this.updateWatch(this.variables);\n        var oq = this.observableQuery;\n        if (oq && oq.options.fetchPolicy === \"no-cache\") {\n            return { complete: false };\n        }\n        var diff = this.cache.diff(options);\n        this.updateLastDiff(diff, options);\n        return diff;\n    };\n    QueryInfo.prototype.updateLastDiff = function (diff, options) {\n        this.lastDiff =\n            diff ?\n                {\n                    diff: diff,\n                    options: options || this.getDiffOptions(),\n                }\n                : void 0;\n    };\n    QueryInfo.prototype.getDiffOptions = function (variables) {\n        var _a;\n        if (variables === void 0) { variables = this.variables; }\n        return {\n            query: this.document,\n            variables: variables,\n            returnPartialData: true,\n            optimistic: true,\n            canonizeResults: (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.options.canonizeResults,\n        };\n    };\n    QueryInfo.prototype.setDiff = function (diff) {\n        var _this = this;\n        var _a;\n        var oldDiff = this.lastDiff && this.lastDiff.diff;\n        // If we are trying to deliver an incomplete cache result, we avoid\n        // reporting it if the query has errored, otherwise we let the broadcast try\n        // and repair the partial result by refetching the query. This check avoids\n        // a situation where a query that errors and another succeeds with\n        // overlapping data does not report the partial data result to the errored\n        // query.\n        //\n        // See https://github.com/apollographql/apollo-client/issues/11400 for more\n        // information on this issue.\n        if (diff && !diff.complete && ((_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.getLastError())) {\n            return;\n        }\n        this.updateLastDiff(diff);\n        if (!this.dirty && !equal(oldDiff && oldDiff.result, diff && diff.result)) {\n            this.dirty = true;\n            if (!this.notifyTimeout) {\n                this.notifyTimeout = setTimeout(function () { return _this.notify(); }, 0);\n            }\n        }\n    };\n    QueryInfo.prototype.setObservableQuery = function (oq) {\n        var _this = this;\n        if (oq === this.observableQuery)\n            return;\n        if (this.oqListener) {\n            this.listeners.delete(this.oqListener);\n        }\n        this.observableQuery = oq;\n        if (oq) {\n            oq[\"queryInfo\"] = this;\n            this.listeners.add((this.oqListener = function () {\n                var diff = _this.getDiff();\n                if (diff.fromOptimisticTransaction) {\n                    // If this diff came from an optimistic transaction, deliver the\n                    // current cache data to the ObservableQuery, but don't perform a\n                    // reobservation, since oq.reobserveCacheFirst might make a network\n                    // request, and we never want to trigger network requests in the\n                    // middle of optimistic updates.\n                    oq[\"observe\"]();\n                }\n                else {\n                    // Otherwise, make the ObservableQuery \"reobserve\" the latest data\n                    // using a temporary fetch policy of \"cache-first\", so complete cache\n                    // results have a chance to be delivered without triggering additional\n                    // network requests, even when options.fetchPolicy is \"network-only\"\n                    // or \"cache-and-network\". All other fetch policies are preserved by\n                    // this method, and are handled by calling oq.reobserve(). If this\n                    // reobservation is spurious, isDifferentFromLastResult still has a\n                    // chance to catch it before delivery to ObservableQuery subscribers.\n                    reobserveCacheFirst(oq);\n                }\n            }));\n        }\n        else {\n            delete this.oqListener;\n        }\n    };\n    QueryInfo.prototype.notify = function () {\n        var _this = this;\n        cancelNotifyTimeout(this);\n        if (this.shouldNotify()) {\n            this.listeners.forEach(function (listener) { return listener(_this); });\n        }\n        this.dirty = false;\n    };\n    QueryInfo.prototype.shouldNotify = function () {\n        if (!this.dirty || !this.listeners.size) {\n            return false;\n        }\n        if (isNetworkRequestInFlight(this.networkStatus) && this.observableQuery) {\n            var fetchPolicy = this.observableQuery.options.fetchPolicy;\n            if (fetchPolicy !== \"cache-only\" && fetchPolicy !== \"cache-and-network\") {\n                return false;\n            }\n        }\n        return true;\n    };\n    QueryInfo.prototype.stop = function () {\n        if (!this.stopped) {\n            this.stopped = true;\n            // Cancel the pending notify timeout\n            this.reset();\n            this.cancel();\n            // Revert back to the no-op version of cancel inherited from\n            // QueryInfo.prototype.\n            this.cancel = QueryInfo.prototype.cancel;\n            var oq = this.observableQuery;\n            if (oq)\n                oq.stopPolling();\n        }\n    };\n    // This method is a no-op by default, until/unless overridden by the\n    // updateWatch method.\n    QueryInfo.prototype.cancel = function () { };\n    QueryInfo.prototype.updateWatch = function (variables) {\n        var _this = this;\n        if (variables === void 0) { variables = this.variables; }\n        var oq = this.observableQuery;\n        if (oq && oq.options.fetchPolicy === \"no-cache\") {\n            return;\n        }\n        var watchOptions = __assign(__assign({}, this.getDiffOptions(variables)), { watcher: this, callback: function (diff) { return _this.setDiff(diff); } });\n        if (!this.lastWatch || !equal(watchOptions, this.lastWatch)) {\n            this.cancel();\n            this.cancel = this.cache.watch((this.lastWatch = watchOptions));\n        }\n    };\n    QueryInfo.prototype.resetLastWrite = function () {\n        this.lastWrite = void 0;\n    };\n    QueryInfo.prototype.shouldWrite = function (result, variables) {\n        var lastWrite = this.lastWrite;\n        return !(lastWrite &&\n            // If cache.evict has been called since the last time we wrote this\n            // data into the cache, there's a chance writing this result into\n            // the cache will repair what was evicted.\n            lastWrite.dmCount === destructiveMethodCounts.get(this.cache) &&\n            equal(variables, lastWrite.variables) &&\n            equal(result.data, lastWrite.result.data));\n    };\n    QueryInfo.prototype.markResult = function (result, document, options, cacheWriteBehavior) {\n        var _this = this;\n        var merger = new DeepMerger();\n        var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n        // Cancel the pending notify timeout (if it exists) to prevent extraneous network\n        // requests. To allow future notify timeouts, diff and dirty are reset as well.\n        this.reset();\n        if (\"incremental\" in result && isNonEmptyArray(result.incremental)) {\n            var mergedData = mergeIncrementalData(this.getDiff().result, result);\n            result.data = mergedData;\n            // Detect the first chunk of a deferred query and merge it with existing\n            // cache data. This ensures a `cache-first` fetch policy that returns\n            // partial cache data or a `cache-and-network` fetch policy that already\n            // has full data in the cache does not complain when trying to merge the\n            // initial deferred server data with existing cache data.\n        }\n        else if (\"hasNext\" in result && result.hasNext) {\n            var diff = this.getDiff();\n            result.data = merger.merge(diff.result, result.data);\n        }\n        this.graphQLErrors = graphQLErrors;\n        if (options.fetchPolicy === \"no-cache\") {\n            this.updateLastDiff({ result: result.data, complete: true }, this.getDiffOptions(options.variables));\n        }\n        else if (cacheWriteBehavior !== 0 /* CacheWriteBehavior.FORBID */) {\n            if (shouldWriteResult(result, options.errorPolicy)) {\n                // Using a transaction here so we have a chance to read the result\n                // back from the cache before the watch callback fires as a result\n                // of writeQuery, so we can store the new diff quietly and ignore\n                // it when we receive it redundantly from the watch callback.\n                this.cache.performTransaction(function (cache) {\n                    if (_this.shouldWrite(result, options.variables)) {\n                        cache.writeQuery({\n                            query: document,\n                            data: result.data,\n                            variables: options.variables,\n                            overwrite: cacheWriteBehavior === 1 /* CacheWriteBehavior.OVERWRITE */,\n                        });\n                        _this.lastWrite = {\n                            result: result,\n                            variables: options.variables,\n                            dmCount: destructiveMethodCounts.get(_this.cache),\n                        };\n                    }\n                    else {\n                        // If result is the same as the last result we received from\n                        // the network (and the variables match too), avoid writing\n                        // result into the cache again. The wisdom of skipping this\n                        // cache write is far from obvious, since any cache write\n                        // could be the one that puts the cache back into a desired\n                        // state, fixing corruption or missing data. However, if we\n                        // always write every network result into the cache, we enable\n                        // feuds between queries competing to update the same data in\n                        // incompatible ways, which can lead to an endless cycle of\n                        // cache broadcasts and useless network requests. As with any\n                        // feud, eventually one side must step back from the brink,\n                        // letting the other side(s) have the last word(s). There may\n                        // be other points where we could break this cycle, such as\n                        // silencing the broadcast for cache.writeQuery (not a good\n                        // idea, since it just delays the feud a bit) or somehow\n                        // avoiding the network request that just happened (also bad,\n                        // because the server could return useful new data). All\n                        // options considered, skipping this cache write seems to be\n                        // the least damaging place to break the cycle, because it\n                        // reflects the intuition that we recently wrote this exact\n                        // result into the cache, so the cache *should* already/still\n                        // contain this data. If some other query has clobbered that\n                        // data in the meantime, that's too bad, but there will be no\n                        // winners if every query blindly reverts to its own version\n                        // of the data. This approach also gives the network a chance\n                        // to return new data, which will be written into the cache as\n                        // usual, notifying only those queries that are directly\n                        // affected by the cache updates, as usual. In the future, an\n                        // even more sophisticated cache could perhaps prevent or\n                        // mitigate the clobbering somehow, but that would make this\n                        // particular cache write even less important, and thus\n                        // skipping it would be even safer than it is today.\n                        if (_this.lastDiff && _this.lastDiff.diff.complete) {\n                            // Reuse data from the last good (complete) diff that we\n                            // received, when possible.\n                            result.data = _this.lastDiff.diff.result;\n                            return;\n                        }\n                        // If the previous this.diff was incomplete, fall through to\n                        // re-reading the latest data with cache.diff, below.\n                    }\n                    var diffOptions = _this.getDiffOptions(options.variables);\n                    var diff = cache.diff(diffOptions);\n                    // In case the QueryManager stops this QueryInfo before its\n                    // results are delivered, it's important to avoid restarting the\n                    // cache watch when markResult is called. We also avoid updating\n                    // the watch if we are writing a result that doesn't match the current\n                    // variables to avoid race conditions from broadcasting the wrong\n                    // result.\n                    if (!_this.stopped && equal(_this.variables, options.variables)) {\n                        // Any time we're about to update this.diff, we need to make\n                        // sure we've started watching the cache.\n                        _this.updateWatch(options.variables);\n                    }\n                    // If we're allowed to write to the cache, and we can read a\n                    // complete result from the cache, update result.data to be the\n                    // result from the cache, rather than the raw network result.\n                    // Set without setDiff to avoid triggering a notify call, since\n                    // we have other ways of notifying for this result.\n                    _this.updateLastDiff(diff, diffOptions);\n                    if (diff.complete) {\n                        result.data = diff.result;\n                    }\n                });\n            }\n            else {\n                this.lastWrite = void 0;\n            }\n        }\n    };\n    QueryInfo.prototype.markReady = function () {\n        this.networkError = null;\n        return (this.networkStatus = NetworkStatus.ready);\n    };\n    QueryInfo.prototype.markError = function (error) {\n        this.networkStatus = NetworkStatus.error;\n        this.lastWrite = void 0;\n        this.reset();\n        if (error.graphQLErrors) {\n            this.graphQLErrors = error.graphQLErrors;\n        }\n        if (error.networkError) {\n            this.networkError = error.networkError;\n        }\n        return error;\n    };\n    return QueryInfo;\n}());\nexport { QueryInfo };\nexport function shouldWriteResult(result, errorPolicy) {\n    if (errorPolicy === void 0) { errorPolicy = \"none\"; }\n    var ignoreErrors = errorPolicy === \"ignore\" || errorPolicy === \"all\";\n    var writeWithErrors = !graphQLResultHasError(result);\n    if (!writeWithErrors && ignoreErrors && result.data) {\n        writeWithErrors = true;\n    }\n    return writeWithErrors;\n}\n//# sourceMappingURL=QueryInfo.js.map", "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { execute } from \"../link/core/index.js\";\nimport { addNonReactiveToNamedFragments, hasDirectives, isExecutionPatchIncrementalResult, isExecutionPatchResult, isFullyUnmaskedOperation, removeDirectivesFromDocument, } from \"../utilities/index.js\";\nimport { canonicalStringify } from \"../cache/index.js\";\nimport { getDefaultValues, getOperationDefinition, getOperationName, hasClientExports, graphQLResultHasError, getGraphQLErrorsFromResult, Observable, asyncMap, isNonEmptyArray, Concast, makeUniqueId, isDocumentNode, isNonNullObject, DocumentTransform, } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/common/incrementalResult.js\";\nimport { ApolloError, isApolloError, graphQLResultHasProtocolErrors, } from \"../errors/index.js\";\nimport { ObservableQuery, logMissingFieldErrors } from \"./ObservableQuery.js\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { QueryInfo, shouldWriteResult, } from \"./QueryInfo.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../errors/index.js\";\nimport { print } from \"../utilities/index.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar IGNORE = Object.create(null);\nimport { Trie } from \"@wry/trie\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../utilities/index.js\";\nimport { maskFragment, maskOperation } from \"../masking/index.js\";\nvar QueryManager = /** @class */ (function () {\n    function QueryManager(options) {\n        var _this = this;\n        this.clientAwareness = {};\n        // All the queries that the QueryManager is currently managing (not\n        // including mutations and subscriptions).\n        this.queries = new Map();\n        // Maps from queryId strings to Promise rejection functions for\n        // currently active queries and fetches.\n        // Use protected instead of private field so\n        // @apollo/experimental-nextjs-app-support can access type info.\n        this.fetchCancelFns = new Map();\n        this.transformCache = new AutoCleanedWeakCache(cacheSizes[\"queryManager.getDocumentInfo\"] ||\n            2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */);\n        this.queryIdCounter = 1;\n        this.requestIdCounter = 1;\n        this.mutationIdCounter = 1;\n        // Use protected instead of private field so\n        // @apollo/experimental-nextjs-app-support can access type info.\n        this.inFlightLinkObservables = new Trie(false);\n        this.noCacheWarningsByQueryId = new Set();\n        var defaultDocumentTransform = new DocumentTransform(function (document) { return _this.cache.transformDocument(document); }, \n        // Allow the apollo cache to manage its own transform caches\n        { cache: false });\n        this.cache = options.cache;\n        this.link = options.link;\n        this.defaultOptions = options.defaultOptions;\n        this.queryDeduplication = options.queryDeduplication;\n        this.clientAwareness = options.clientAwareness;\n        this.localState = options.localState;\n        this.ssrMode = options.ssrMode;\n        this.assumeImmutableResults = options.assumeImmutableResults;\n        this.dataMasking = options.dataMasking;\n        var documentTransform = options.documentTransform;\n        this.documentTransform =\n            documentTransform ?\n                defaultDocumentTransform\n                    .concat(documentTransform)\n                    // The custom document transform may add new fragment spreads or new\n                    // field selections, so we want to give the cache a chance to run\n                    // again. For example, the InMemoryCache adds __typename to field\n                    // selections and fragments from the fragment registry.\n                    .concat(defaultDocumentTransform)\n                : defaultDocumentTransform;\n        this.defaultContext = options.defaultContext || Object.create(null);\n        if ((this.onBroadcast = options.onBroadcast)) {\n            this.mutationStore = Object.create(null);\n        }\n    }\n    /**\n     * Call this method to terminate any active query processes, making it safe\n     * to dispose of this QueryManager instance.\n     */\n    QueryManager.prototype.stop = function () {\n        var _this = this;\n        this.queries.forEach(function (_info, queryId) {\n            _this.stopQueryNoBroadcast(queryId);\n        });\n        this.cancelPendingFetches(newInvariantError(27));\n    };\n    QueryManager.prototype.cancelPendingFetches = function (error) {\n        this.fetchCancelFns.forEach(function (cancel) { return cancel(error); });\n        this.fetchCancelFns.clear();\n    };\n    QueryManager.prototype.mutate = function (_a) {\n        return __awaiter(this, arguments, void 0, function (_b) {\n            var mutationId, hasClientExports, mutationStoreValue, isOptimistic, self;\n            var _c, _d;\n            var mutation = _b.mutation, variables = _b.variables, optimisticResponse = _b.optimisticResponse, updateQueries = _b.updateQueries, _e = _b.refetchQueries, refetchQueries = _e === void 0 ? [] : _e, _f = _b.awaitRefetchQueries, awaitRefetchQueries = _f === void 0 ? false : _f, updateWithProxyFn = _b.update, onQueryUpdated = _b.onQueryUpdated, _g = _b.fetchPolicy, fetchPolicy = _g === void 0 ? ((_c = this.defaultOptions.mutate) === null || _c === void 0 ? void 0 : _c.fetchPolicy) || \"network-only\" : _g, _h = _b.errorPolicy, errorPolicy = _h === void 0 ? ((_d = this.defaultOptions.mutate) === null || _d === void 0 ? void 0 : _d.errorPolicy) || \"none\" : _h, keepRootFields = _b.keepRootFields, context = _b.context;\n            return __generator(this, function (_j) {\n                switch (_j.label) {\n                    case 0:\n                        invariant(mutation, 28);\n                        invariant(fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\", 29);\n                        mutationId = this.generateMutationId();\n                        mutation = this.cache.transformForLink(this.transform(mutation));\n                        hasClientExports = this.getDocumentInfo(mutation).hasClientExports;\n                        variables = this.getVariables(mutation, variables);\n                        if (!hasClientExports) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this.localState.addExportedVariables(mutation, variables, context)];\n                    case 1:\n                        variables = (_j.sent());\n                        _j.label = 2;\n                    case 2:\n                        mutationStoreValue = this.mutationStore &&\n                            (this.mutationStore[mutationId] = {\n                                mutation: mutation,\n                                variables: variables,\n                                loading: true,\n                                error: null,\n                            });\n                        isOptimistic = optimisticResponse &&\n                            this.markMutationOptimistic(optimisticResponse, {\n                                mutationId: mutationId,\n                                document: mutation,\n                                variables: variables,\n                                fetchPolicy: fetchPolicy,\n                                errorPolicy: errorPolicy,\n                                context: context,\n                                updateQueries: updateQueries,\n                                update: updateWithProxyFn,\n                                keepRootFields: keepRootFields,\n                            });\n                        this.broadcastQueries();\n                        self = this;\n                        return [2 /*return*/, new Promise(function (resolve, reject) {\n                                return asyncMap(self.getObservableFromLink(mutation, __assign(__assign({}, context), { optimisticResponse: isOptimistic ? optimisticResponse : void 0 }), variables, {}, false), function (result) {\n                                    if (graphQLResultHasError(result) && errorPolicy === \"none\") {\n                                        throw new ApolloError({\n                                            graphQLErrors: getGraphQLErrorsFromResult(result),\n                                        });\n                                    }\n                                    if (mutationStoreValue) {\n                                        mutationStoreValue.loading = false;\n                                        mutationStoreValue.error = null;\n                                    }\n                                    var storeResult = __assign({}, result);\n                                    if (typeof refetchQueries === \"function\") {\n                                        refetchQueries = refetchQueries(storeResult);\n                                    }\n                                    if (errorPolicy === \"ignore\" && graphQLResultHasError(storeResult)) {\n                                        delete storeResult.errors;\n                                    }\n                                    return self.markMutationResult({\n                                        mutationId: mutationId,\n                                        result: storeResult,\n                                        document: mutation,\n                                        variables: variables,\n                                        fetchPolicy: fetchPolicy,\n                                        errorPolicy: errorPolicy,\n                                        context: context,\n                                        update: updateWithProxyFn,\n                                        updateQueries: updateQueries,\n                                        awaitRefetchQueries: awaitRefetchQueries,\n                                        refetchQueries: refetchQueries,\n                                        removeOptimistic: isOptimistic ? mutationId : void 0,\n                                        onQueryUpdated: onQueryUpdated,\n                                        keepRootFields: keepRootFields,\n                                    });\n                                }).subscribe({\n                                    next: function (storeResult) {\n                                        self.broadcastQueries();\n                                        // Since mutations might receive multiple payloads from the\n                                        // ApolloLink chain (e.g. when used with @defer),\n                                        // we resolve with a SingleExecutionResult or after the final\n                                        // ExecutionPatchResult has arrived and we have assembled the\n                                        // multipart response into a single result.\n                                        if (!(\"hasNext\" in storeResult) || storeResult.hasNext === false) {\n                                            resolve(__assign(__assign({}, storeResult), { data: self.maskOperation({\n                                                    document: mutation,\n                                                    data: storeResult.data,\n                                                    fetchPolicy: fetchPolicy,\n                                                    id: mutationId,\n                                                }) }));\n                                        }\n                                    },\n                                    error: function (err) {\n                                        if (mutationStoreValue) {\n                                            mutationStoreValue.loading = false;\n                                            mutationStoreValue.error = err;\n                                        }\n                                        if (isOptimistic) {\n                                            self.cache.removeOptimistic(mutationId);\n                                        }\n                                        self.broadcastQueries();\n                                        reject(err instanceof ApolloError ? err : (new ApolloError({\n                                            networkError: err,\n                                        })));\n                                    },\n                                });\n                            })];\n                }\n            });\n        });\n    };\n    QueryManager.prototype.markMutationResult = function (mutation, cache) {\n        var _this = this;\n        if (cache === void 0) { cache = this.cache; }\n        var result = mutation.result;\n        var cacheWrites = [];\n        var skipCache = mutation.fetchPolicy === \"no-cache\";\n        if (!skipCache && shouldWriteResult(result, mutation.errorPolicy)) {\n            if (!isExecutionPatchIncrementalResult(result)) {\n                cacheWrites.push({\n                    result: result.data,\n                    dataId: \"ROOT_MUTATION\",\n                    query: mutation.document,\n                    variables: mutation.variables,\n                });\n            }\n            if (isExecutionPatchIncrementalResult(result) &&\n                isNonEmptyArray(result.incremental)) {\n                var diff = cache.diff({\n                    id: \"ROOT_MUTATION\",\n                    // The cache complains if passed a mutation where it expects a\n                    // query, so we transform mutations and subscriptions to queries\n                    // (only once, thanks to this.transformCache).\n                    query: this.getDocumentInfo(mutation.document).asQuery,\n                    variables: mutation.variables,\n                    optimistic: false,\n                    returnPartialData: true,\n                });\n                var mergedData = void 0;\n                if (diff.result) {\n                    mergedData = mergeIncrementalData(diff.result, result);\n                }\n                if (typeof mergedData !== \"undefined\") {\n                    // cast the ExecutionPatchResult to FetchResult here since\n                    // ExecutionPatchResult never has `data` when returned from the server\n                    result.data = mergedData;\n                    cacheWrites.push({\n                        result: mergedData,\n                        dataId: \"ROOT_MUTATION\",\n                        query: mutation.document,\n                        variables: mutation.variables,\n                    });\n                }\n            }\n            var updateQueries_1 = mutation.updateQueries;\n            if (updateQueries_1) {\n                this.queries.forEach(function (_a, queryId) {\n                    var observableQuery = _a.observableQuery;\n                    var queryName = observableQuery && observableQuery.queryName;\n                    if (!queryName || !hasOwnProperty.call(updateQueries_1, queryName)) {\n                        return;\n                    }\n                    var updater = updateQueries_1[queryName];\n                    var _b = _this.queries.get(queryId), document = _b.document, variables = _b.variables;\n                    // Read the current query result from the store.\n                    var _c = cache.diff({\n                        query: document,\n                        variables: variables,\n                        returnPartialData: true,\n                        optimistic: false,\n                    }), currentQueryResult = _c.result, complete = _c.complete;\n                    if (complete && currentQueryResult) {\n                        // Run our reducer using the current query result and the mutation result.\n                        var nextQueryResult = updater(currentQueryResult, {\n                            mutationResult: result,\n                            queryName: (document && getOperationName(document)) || void 0,\n                            queryVariables: variables,\n                        });\n                        // Write the modified result back into the store if we got a new result.\n                        if (nextQueryResult) {\n                            cacheWrites.push({\n                                result: nextQueryResult,\n                                dataId: \"ROOT_QUERY\",\n                                query: document,\n                                variables: variables,\n                            });\n                        }\n                    }\n                });\n            }\n        }\n        if (cacheWrites.length > 0 ||\n            (mutation.refetchQueries || \"\").length > 0 ||\n            mutation.update ||\n            mutation.onQueryUpdated ||\n            mutation.removeOptimistic) {\n            var results_1 = [];\n            this.refetchQueries({\n                updateCache: function (cache) {\n                    if (!skipCache) {\n                        cacheWrites.forEach(function (write) { return cache.write(write); });\n                    }\n                    // If the mutation has some writes associated with it then we need to\n                    // apply those writes to the store by running this reducer again with\n                    // a write action.\n                    var update = mutation.update;\n                    // Determine whether result is a SingleExecutionResult,\n                    // or the final ExecutionPatchResult.\n                    var isFinalResult = !isExecutionPatchResult(result) ||\n                        (isExecutionPatchIncrementalResult(result) && !result.hasNext);\n                    if (update) {\n                        if (!skipCache) {\n                            // Re-read the ROOT_MUTATION data we just wrote into the cache\n                            // (the first cache.write call in the cacheWrites.forEach loop\n                            // above), so field read functions have a chance to run for\n                            // fields within mutation result objects.\n                            var diff = cache.diff({\n                                id: \"ROOT_MUTATION\",\n                                // The cache complains if passed a mutation where it expects a\n                                // query, so we transform mutations and subscriptions to queries\n                                // (only once, thanks to this.transformCache).\n                                query: _this.getDocumentInfo(mutation.document).asQuery,\n                                variables: mutation.variables,\n                                optimistic: false,\n                                returnPartialData: true,\n                            });\n                            if (diff.complete) {\n                                result = __assign(__assign({}, result), { data: diff.result });\n                                if (\"incremental\" in result) {\n                                    delete result.incremental;\n                                }\n                                if (\"hasNext\" in result) {\n                                    delete result.hasNext;\n                                }\n                            }\n                        }\n                        // If we've received the whole response,\n                        // either a SingleExecutionResult or the final ExecutionPatchResult,\n                        // call the update function.\n                        if (isFinalResult) {\n                            update(cache, result, {\n                                context: mutation.context,\n                                variables: mutation.variables,\n                            });\n                        }\n                    }\n                    // TODO Do this with cache.evict({ id: 'ROOT_MUTATION' }) but make it\n                    // shallow to allow rolling back optimistic evictions.\n                    if (!skipCache && !mutation.keepRootFields && isFinalResult) {\n                        cache.modify({\n                            id: \"ROOT_MUTATION\",\n                            fields: function (value, _a) {\n                                var fieldName = _a.fieldName, DELETE = _a.DELETE;\n                                return fieldName === \"__typename\" ? value : DELETE;\n                            },\n                        });\n                    }\n                },\n                include: mutation.refetchQueries,\n                // Write the final mutation.result to the root layer of the cache.\n                optimistic: false,\n                // Remove the corresponding optimistic layer at the same time as we\n                // write the final non-optimistic result.\n                removeOptimistic: mutation.removeOptimistic,\n                // Let the caller of client.mutate optionally determine the refetching\n                // behavior for watched queries after the mutation.update function runs.\n                // If no onQueryUpdated function was provided for this mutation, pass\n                // null instead of undefined to disable the default refetching behavior.\n                onQueryUpdated: mutation.onQueryUpdated || null,\n            }).forEach(function (result) { return results_1.push(result); });\n            if (mutation.awaitRefetchQueries || mutation.onQueryUpdated) {\n                // Returning a promise here makes the mutation await that promise, so we\n                // include results in that promise's work if awaitRefetchQueries or an\n                // onQueryUpdated function was specified.\n                return Promise.all(results_1).then(function () { return result; });\n            }\n        }\n        return Promise.resolve(result);\n    };\n    QueryManager.prototype.markMutationOptimistic = function (optimisticResponse, mutation) {\n        var _this = this;\n        var data = typeof optimisticResponse === \"function\" ?\n            optimisticResponse(mutation.variables, { IGNORE: IGNORE })\n            : optimisticResponse;\n        if (data === IGNORE) {\n            return false;\n        }\n        this.cache.recordOptimisticTransaction(function (cache) {\n            try {\n                _this.markMutationResult(__assign(__assign({}, mutation), { result: { data: data } }), cache);\n            }\n            catch (error) {\n                globalThis.__DEV__ !== false && invariant.error(error);\n            }\n        }, mutation.mutationId);\n        return true;\n    };\n    QueryManager.prototype.fetchQuery = function (queryId, options, networkStatus) {\n        return this.fetchConcastWithInfo(queryId, options, networkStatus).concast\n            .promise;\n    };\n    QueryManager.prototype.getQueryStore = function () {\n        var store = Object.create(null);\n        this.queries.forEach(function (info, queryId) {\n            store[queryId] = {\n                variables: info.variables,\n                networkStatus: info.networkStatus,\n                networkError: info.networkError,\n                graphQLErrors: info.graphQLErrors,\n            };\n        });\n        return store;\n    };\n    QueryManager.prototype.resetErrors = function (queryId) {\n        var queryInfo = this.queries.get(queryId);\n        if (queryInfo) {\n            queryInfo.networkError = undefined;\n            queryInfo.graphQLErrors = [];\n        }\n    };\n    QueryManager.prototype.transform = function (document) {\n        return this.documentTransform.transformDocument(document);\n    };\n    QueryManager.prototype.getDocumentInfo = function (document) {\n        var transformCache = this.transformCache;\n        if (!transformCache.has(document)) {\n            var cacheEntry = {\n                // TODO These three calls (hasClientExports, shouldForceResolvers, and\n                // usesNonreactiveDirective) are performing independent full traversals\n                // of the transformed document. We should consider merging these\n                // traversals into a single pass in the future, though the work is\n                // cached after the first time.\n                hasClientExports: hasClientExports(document),\n                hasForcedResolvers: this.localState.shouldForceResolvers(document),\n                hasNonreactiveDirective: hasDirectives([\"nonreactive\"], document),\n                nonReactiveQuery: addNonReactiveToNamedFragments(document),\n                clientQuery: this.localState.clientQuery(document),\n                serverQuery: removeDirectivesFromDocument([\n                    { name: \"client\", remove: true },\n                    { name: \"connection\" },\n                    { name: \"nonreactive\" },\n                    { name: \"unmask\" },\n                ], document),\n                defaultVars: getDefaultValues(getOperationDefinition(document)),\n                // Transform any mutation or subscription operations to query operations\n                // so we can read/write them from/to the cache.\n                asQuery: __assign(__assign({}, document), { definitions: document.definitions.map(function (def) {\n                        if (def.kind === \"OperationDefinition\" &&\n                            def.operation !== \"query\") {\n                            return __assign(__assign({}, def), { operation: \"query\" });\n                        }\n                        return def;\n                    }) }),\n            };\n            transformCache.set(document, cacheEntry);\n        }\n        return transformCache.get(document);\n    };\n    QueryManager.prototype.getVariables = function (document, variables) {\n        return __assign(__assign({}, this.getDocumentInfo(document).defaultVars), variables);\n    };\n    QueryManager.prototype.watchQuery = function (options) {\n        var query = this.transform(options.query);\n        // assign variable default values if supplied\n        // NOTE: We don't modify options.query here with the transformed query to\n        // ensure observable.options.query is set to the raw untransformed query.\n        options = __assign(__assign({}, options), { variables: this.getVariables(query, options.variables) });\n        if (typeof options.notifyOnNetworkStatusChange === \"undefined\") {\n            options.notifyOnNetworkStatusChange = false;\n        }\n        var queryInfo = new QueryInfo(this);\n        var observable = new ObservableQuery({\n            queryManager: this,\n            queryInfo: queryInfo,\n            options: options,\n        });\n        observable[\"lastQuery\"] = query;\n        this.queries.set(observable.queryId, queryInfo);\n        // We give queryInfo the transformed query to ensure the first cache diff\n        // uses the transformed query instead of the raw query\n        queryInfo.init({\n            document: query,\n            observableQuery: observable,\n            variables: observable.variables,\n        });\n        return observable;\n    };\n    QueryManager.prototype.query = function (options, queryId) {\n        var _this = this;\n        if (queryId === void 0) { queryId = this.generateQueryId(); }\n        invariant(options.query, 30);\n        invariant(options.query.kind === \"Document\", 31);\n        invariant(!options.returnPartialData, 32);\n        invariant(!options.pollInterval, 33);\n        var query = this.transform(options.query);\n        return this.fetchQuery(queryId, __assign(__assign({}, options), { query: query }))\n            .then(function (result) {\n            return result && __assign(__assign({}, result), { data: _this.maskOperation({\n                    document: query,\n                    data: result.data,\n                    fetchPolicy: options.fetchPolicy,\n                    id: queryId,\n                }) });\n        })\n            .finally(function () { return _this.stopQuery(queryId); });\n    };\n    QueryManager.prototype.generateQueryId = function () {\n        return String(this.queryIdCounter++);\n    };\n    QueryManager.prototype.generateRequestId = function () {\n        return this.requestIdCounter++;\n    };\n    QueryManager.prototype.generateMutationId = function () {\n        return String(this.mutationIdCounter++);\n    };\n    QueryManager.prototype.stopQueryInStore = function (queryId) {\n        this.stopQueryInStoreNoBroadcast(queryId);\n        this.broadcastQueries();\n    };\n    QueryManager.prototype.stopQueryInStoreNoBroadcast = function (queryId) {\n        var queryInfo = this.queries.get(queryId);\n        if (queryInfo)\n            queryInfo.stop();\n    };\n    QueryManager.prototype.clearStore = function (options) {\n        if (options === void 0) { options = {\n            discardWatches: true,\n        }; }\n        // Before we have sent the reset action to the store, we can no longer\n        // rely on the results returned by in-flight requests since these may\n        // depend on values that previously existed in the data portion of the\n        // store. So, we cancel the promises and observers that we have issued\n        // so far and not yet resolved (in the case of queries).\n        this.cancelPendingFetches(newInvariantError(34));\n        this.queries.forEach(function (queryInfo) {\n            if (queryInfo.observableQuery) {\n                // Set loading to true so listeners don't trigger unless they want\n                // results with partial data.\n                queryInfo.networkStatus = NetworkStatus.loading;\n            }\n            else {\n                queryInfo.stop();\n            }\n        });\n        if (this.mutationStore) {\n            this.mutationStore = Object.create(null);\n        }\n        // begin removing data from the store\n        return this.cache.reset(options);\n    };\n    QueryManager.prototype.getObservableQueries = function (include) {\n        var _this = this;\n        if (include === void 0) { include = \"active\"; }\n        var queries = new Map();\n        var queryNames = new Map();\n        var queryNamesAndQueryStrings = new Map();\n        var legacyQueryOptions = new Set();\n        if (Array.isArray(include)) {\n            include.forEach(function (desc) {\n                if (typeof desc === \"string\") {\n                    queryNames.set(desc, desc);\n                    queryNamesAndQueryStrings.set(desc, false);\n                }\n                else if (isDocumentNode(desc)) {\n                    var queryString = print(_this.transform(desc));\n                    queryNames.set(queryString, getOperationName(desc));\n                    queryNamesAndQueryStrings.set(queryString, false);\n                }\n                else if (isNonNullObject(desc) && desc.query) {\n                    legacyQueryOptions.add(desc);\n                }\n            });\n        }\n        this.queries.forEach(function (_a, queryId) {\n            var oq = _a.observableQuery, document = _a.document;\n            if (oq) {\n                if (include === \"all\") {\n                    queries.set(queryId, oq);\n                    return;\n                }\n                var queryName = oq.queryName, fetchPolicy = oq.options.fetchPolicy;\n                if (fetchPolicy === \"standby\" ||\n                    (include === \"active\" && !oq.hasObservers())) {\n                    return;\n                }\n                if (include === \"active\" ||\n                    (queryName && queryNamesAndQueryStrings.has(queryName)) ||\n                    (document && queryNamesAndQueryStrings.has(print(document)))) {\n                    queries.set(queryId, oq);\n                    if (queryName)\n                        queryNamesAndQueryStrings.set(queryName, true);\n                    if (document)\n                        queryNamesAndQueryStrings.set(print(document), true);\n                }\n            }\n        });\n        if (legacyQueryOptions.size) {\n            legacyQueryOptions.forEach(function (options) {\n                // We will be issuing a fresh network request for this query, so we\n                // pre-allocate a new query ID here, using a special prefix to enable\n                // cleaning up these temporary queries later, after fetching.\n                var queryId = makeUniqueId(\"legacyOneTimeQuery\");\n                var queryInfo = _this.getQuery(queryId).init({\n                    document: options.query,\n                    variables: options.variables,\n                });\n                var oq = new ObservableQuery({\n                    queryManager: _this,\n                    queryInfo: queryInfo,\n                    options: __assign(__assign({}, options), { fetchPolicy: \"network-only\" }),\n                });\n                invariant(oq.queryId === queryId);\n                queryInfo.setObservableQuery(oq);\n                queries.set(queryId, oq);\n            });\n        }\n        if (globalThis.__DEV__ !== false && queryNamesAndQueryStrings.size) {\n            queryNamesAndQueryStrings.forEach(function (included, nameOrQueryString) {\n                if (!included) {\n                    var queryName = queryNames.get(nameOrQueryString);\n                    if (queryName) {\n                        globalThis.__DEV__ !== false && invariant.warn(35, queryName);\n                    }\n                    else {\n                        globalThis.__DEV__ !== false && invariant.warn(36);\n                    }\n                }\n            });\n        }\n        return queries;\n    };\n    QueryManager.prototype.reFetchObservableQueries = function (includeStandby) {\n        var _this = this;\n        if (includeStandby === void 0) { includeStandby = false; }\n        var observableQueryPromises = [];\n        this.getObservableQueries(includeStandby ? \"all\" : \"active\").forEach(function (observableQuery, queryId) {\n            var fetchPolicy = observableQuery.options.fetchPolicy;\n            observableQuery.resetLastResults();\n            if (includeStandby ||\n                (fetchPolicy !== \"standby\" && fetchPolicy !== \"cache-only\")) {\n                observableQueryPromises.push(observableQuery.refetch());\n            }\n            _this.getQuery(queryId).setDiff(null);\n        });\n        this.broadcastQueries();\n        return Promise.all(observableQueryPromises);\n    };\n    QueryManager.prototype.setObservableQuery = function (observableQuery) {\n        this.getQuery(observableQuery.queryId).setObservableQuery(observableQuery);\n    };\n    QueryManager.prototype.startGraphQLSubscription = function (options) {\n        var _this = this;\n        var query = options.query, variables = options.variables;\n        var fetchPolicy = options.fetchPolicy, _a = options.errorPolicy, errorPolicy = _a === void 0 ? \"none\" : _a, _b = options.context, context = _b === void 0 ? {} : _b, _c = options.extensions, extensions = _c === void 0 ? {} : _c;\n        query = this.transform(query);\n        variables = this.getVariables(query, variables);\n        var makeObservable = function (variables) {\n            return _this.getObservableFromLink(query, context, variables, extensions).map(function (result) {\n                if (fetchPolicy !== \"no-cache\") {\n                    // the subscription interface should handle not sending us results we no longer subscribe to.\n                    // XXX I don't think we ever send in an object with errors, but we might in the future...\n                    if (shouldWriteResult(result, errorPolicy)) {\n                        _this.cache.write({\n                            query: query,\n                            result: result.data,\n                            dataId: \"ROOT_SUBSCRIPTION\",\n                            variables: variables,\n                        });\n                    }\n                    _this.broadcastQueries();\n                }\n                var hasErrors = graphQLResultHasError(result);\n                var hasProtocolErrors = graphQLResultHasProtocolErrors(result);\n                if (hasErrors || hasProtocolErrors) {\n                    var errors = {};\n                    if (hasErrors) {\n                        errors.graphQLErrors = result.errors;\n                    }\n                    if (hasProtocolErrors) {\n                        errors.protocolErrors = result.extensions[PROTOCOL_ERRORS_SYMBOL];\n                    }\n                    // `errorPolicy` is a mechanism for handling GraphQL errors, according\n                    // to our documentation, so we throw protocol errors regardless of the\n                    // set error policy.\n                    if (errorPolicy === \"none\" || hasProtocolErrors) {\n                        throw new ApolloError(errors);\n                    }\n                }\n                if (errorPolicy === \"ignore\") {\n                    delete result.errors;\n                }\n                return result;\n            });\n        };\n        if (this.getDocumentInfo(query).hasClientExports) {\n            var observablePromise_1 = this.localState\n                .addExportedVariables(query, variables, context)\n                .then(makeObservable);\n            return new Observable(function (observer) {\n                var sub = null;\n                observablePromise_1.then(function (observable) { return (sub = observable.subscribe(observer)); }, observer.error);\n                return function () { return sub && sub.unsubscribe(); };\n            });\n        }\n        return makeObservable(variables);\n    };\n    QueryManager.prototype.stopQuery = function (queryId) {\n        this.stopQueryNoBroadcast(queryId);\n        this.broadcastQueries();\n    };\n    QueryManager.prototype.stopQueryNoBroadcast = function (queryId) {\n        this.stopQueryInStoreNoBroadcast(queryId);\n        this.removeQuery(queryId);\n    };\n    QueryManager.prototype.removeQuery = function (queryId) {\n        // teardown all links\n        // Both `QueryManager.fetchRequest` and `QueryManager.query` create separate promises\n        // that each add their reject functions to fetchCancelFns.\n        // A query created with `QueryManager.query()` could trigger a `QueryManager.fetchRequest`.\n        // The same queryId could have two rejection fns for two promises\n        this.fetchCancelFns.delete(queryId);\n        if (this.queries.has(queryId)) {\n            this.getQuery(queryId).stop();\n            this.queries.delete(queryId);\n        }\n    };\n    QueryManager.prototype.broadcastQueries = function () {\n        if (this.onBroadcast)\n            this.onBroadcast();\n        this.queries.forEach(function (info) { return info.notify(); });\n    };\n    QueryManager.prototype.getLocalState = function () {\n        return this.localState;\n    };\n    QueryManager.prototype.getObservableFromLink = function (query, context, variables, extensions, \n    // Prefer context.queryDeduplication if specified.\n    deduplication) {\n        var _this = this;\n        var _a;\n        if (deduplication === void 0) { deduplication = (_a = context === null || context === void 0 ? void 0 : context.queryDeduplication) !== null && _a !== void 0 ? _a : this.queryDeduplication; }\n        var observable;\n        var _b = this.getDocumentInfo(query), serverQuery = _b.serverQuery, clientQuery = _b.clientQuery;\n        if (serverQuery) {\n            var _c = this, inFlightLinkObservables_1 = _c.inFlightLinkObservables, link = _c.link;\n            var operation = {\n                query: serverQuery,\n                variables: variables,\n                operationName: getOperationName(serverQuery) || void 0,\n                context: this.prepareContext(__assign(__assign({}, context), { forceFetch: !deduplication })),\n                extensions: extensions,\n            };\n            context = operation.context;\n            if (deduplication) {\n                var printedServerQuery_1 = print(serverQuery);\n                var varJson_1 = canonicalStringify(variables);\n                var entry = inFlightLinkObservables_1.lookup(printedServerQuery_1, varJson_1);\n                observable = entry.observable;\n                if (!observable) {\n                    var concast = new Concast([\n                        execute(link, operation),\n                    ]);\n                    observable = entry.observable = concast;\n                    concast.beforeNext(function () {\n                        inFlightLinkObservables_1.remove(printedServerQuery_1, varJson_1);\n                    });\n                }\n            }\n            else {\n                observable = new Concast([\n                    execute(link, operation),\n                ]);\n            }\n        }\n        else {\n            observable = new Concast([Observable.of({ data: {} })]);\n            context = this.prepareContext(context);\n        }\n        if (clientQuery) {\n            observable = asyncMap(observable, function (result) {\n                return _this.localState.runResolvers({\n                    document: clientQuery,\n                    remoteResult: result,\n                    context: context,\n                    variables: variables,\n                });\n            });\n        }\n        return observable;\n    };\n    QueryManager.prototype.getResultsFromLink = function (queryInfo, cacheWriteBehavior, options) {\n        var requestId = (queryInfo.lastRequestId = this.generateRequestId());\n        // Performing transformForLink here gives this.cache a chance to fill in\n        // missing fragment definitions (for example) before sending this document\n        // through the link chain.\n        var linkDocument = this.cache.transformForLink(options.query);\n        return asyncMap(this.getObservableFromLink(linkDocument, options.context, options.variables), function (result) {\n            var graphQLErrors = getGraphQLErrorsFromResult(result);\n            var hasErrors = graphQLErrors.length > 0;\n            var errorPolicy = options.errorPolicy;\n            // If we interrupted this request by calling getResultsFromLink again\n            // with the same QueryInfo object, we ignore the old results.\n            if (requestId >= queryInfo.lastRequestId) {\n                if (hasErrors && errorPolicy === \"none\") {\n                    // Throwing here effectively calls observer.error.\n                    throw queryInfo.markError(new ApolloError({\n                        graphQLErrors: graphQLErrors,\n                    }));\n                }\n                // Use linkDocument rather than queryInfo.document so the\n                // operation/fragments used to write the result are the same as the\n                // ones used to obtain it from the link.\n                queryInfo.markResult(result, linkDocument, options, cacheWriteBehavior);\n                queryInfo.markReady();\n            }\n            var aqr = {\n                data: result.data,\n                loading: false,\n                networkStatus: NetworkStatus.ready,\n            };\n            // In the case we start multiple network requests simulatenously, we\n            // want to ensure we properly set `data` if we're reporting on an old\n            // result which will not be caught by the conditional above that ends up\n            // throwing the markError result.\n            if (hasErrors && errorPolicy === \"none\") {\n                aqr.data = void 0;\n            }\n            if (hasErrors && errorPolicy !== \"ignore\") {\n                aqr.errors = graphQLErrors;\n                aqr.networkStatus = NetworkStatus.error;\n            }\n            return aqr;\n        }, function (networkError) {\n            var error = isApolloError(networkError) ? networkError : (new ApolloError({ networkError: networkError }));\n            // Avoid storing errors from older interrupted queries.\n            if (requestId >= queryInfo.lastRequestId) {\n                queryInfo.markError(error);\n            }\n            throw error;\n        });\n    };\n    QueryManager.prototype.fetchConcastWithInfo = function (queryId, options, \n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus, query) {\n        var _this = this;\n        if (networkStatus === void 0) { networkStatus = NetworkStatus.loading; }\n        if (query === void 0) { query = options.query; }\n        var variables = this.getVariables(query, options.variables);\n        var queryInfo = this.getQuery(queryId);\n        var defaults = this.defaultOptions.watchQuery;\n        var _a = options.fetchPolicy, fetchPolicy = _a === void 0 ? (defaults && defaults.fetchPolicy) || \"cache-first\" : _a, _b = options.errorPolicy, errorPolicy = _b === void 0 ? (defaults && defaults.errorPolicy) || \"none\" : _b, _c = options.returnPartialData, returnPartialData = _c === void 0 ? false : _c, _d = options.notifyOnNetworkStatusChange, notifyOnNetworkStatusChange = _d === void 0 ? false : _d, _e = options.context, context = _e === void 0 ? {} : _e;\n        var normalized = Object.assign({}, options, {\n            query: query,\n            variables: variables,\n            fetchPolicy: fetchPolicy,\n            errorPolicy: errorPolicy,\n            returnPartialData: returnPartialData,\n            notifyOnNetworkStatusChange: notifyOnNetworkStatusChange,\n            context: context,\n        });\n        var fromVariables = function (variables) {\n            // Since normalized is always a fresh copy of options, it's safe to\n            // modify its properties here, rather than creating yet another new\n            // WatchQueryOptions object.\n            normalized.variables = variables;\n            var sourcesWithInfo = _this.fetchQueryByPolicy(queryInfo, normalized, networkStatus);\n            if (\n            // If we're in standby, postpone advancing options.fetchPolicy using\n            // applyNextFetchPolicy.\n            normalized.fetchPolicy !== \"standby\" &&\n                // The \"standby\" policy currently returns [] from fetchQueryByPolicy, so\n                // this is another way to detect when nothing was done/fetched.\n                sourcesWithInfo.sources.length > 0 &&\n                queryInfo.observableQuery) {\n                queryInfo.observableQuery[\"applyNextFetchPolicy\"](\"after-fetch\", options);\n            }\n            return sourcesWithInfo;\n        };\n        // This cancel function needs to be set before the concast is created,\n        // in case concast creation synchronously cancels the request.\n        var cleanupCancelFn = function () { return _this.fetchCancelFns.delete(queryId); };\n        this.fetchCancelFns.set(queryId, function (reason) {\n            cleanupCancelFn();\n            // This delay ensures the concast variable has been initialized.\n            setTimeout(function () { return concast.cancel(reason); });\n        });\n        var concast, containsDataFromLink;\n        // If the query has @export(as: ...) directives, then we need to\n        // process those directives asynchronously. When there are no\n        // @export directives (the common case), we deliberately avoid\n        // wrapping the result of this.fetchQueryByPolicy in a Promise,\n        // since the timing of result delivery is (unfortunately) important\n        // for backwards compatibility. TODO This code could be simpler if\n        // we deprecated and removed LocalState.\n        if (this.getDocumentInfo(normalized.query).hasClientExports) {\n            concast = new Concast(this.localState\n                .addExportedVariables(normalized.query, normalized.variables, normalized.context)\n                .then(fromVariables)\n                .then(function (sourcesWithInfo) { return sourcesWithInfo.sources; }));\n            // there is just no way we can synchronously get the *right* value here,\n            // so we will assume `true`, which is the behaviour before the bug fix in\n            // #10597. This means that bug is not fixed in that case, and is probably\n            // un-fixable with reasonable effort for the edge case of @export as\n            // directives.\n            containsDataFromLink = true;\n        }\n        else {\n            var sourcesWithInfo = fromVariables(normalized.variables);\n            containsDataFromLink = sourcesWithInfo.fromLink;\n            concast = new Concast(sourcesWithInfo.sources);\n        }\n        concast.promise.then(cleanupCancelFn, cleanupCancelFn);\n        return {\n            concast: concast,\n            fromLink: containsDataFromLink,\n        };\n    };\n    QueryManager.prototype.refetchQueries = function (_a) {\n        var _this = this;\n        var updateCache = _a.updateCache, include = _a.include, _b = _a.optimistic, optimistic = _b === void 0 ? false : _b, _c = _a.removeOptimistic, removeOptimistic = _c === void 0 ? optimistic ? makeUniqueId(\"refetchQueries\") : void 0 : _c, onQueryUpdated = _a.onQueryUpdated;\n        var includedQueriesById = new Map();\n        if (include) {\n            this.getObservableQueries(include).forEach(function (oq, queryId) {\n                includedQueriesById.set(queryId, {\n                    oq: oq,\n                    lastDiff: _this.getQuery(queryId).getDiff(),\n                });\n            });\n        }\n        var results = new Map();\n        if (updateCache) {\n            this.cache.batch({\n                update: updateCache,\n                // Since you can perform any combination of cache reads and/or writes in\n                // the cache.batch update function, its optimistic option can be either\n                // a boolean or a string, representing three distinct modes of\n                // operation:\n                //\n                // * false: read/write only the root layer\n                // * true: read/write the topmost layer\n                // * string: read/write a fresh optimistic layer with that ID string\n                //\n                // When typeof optimistic === \"string\", a new optimistic layer will be\n                // temporarily created within cache.batch with that string as its ID. If\n                // we then pass that same string as the removeOptimistic option, we can\n                // make cache.batch immediately remove the optimistic layer after\n                // running the updateCache function, triggering only one broadcast.\n                //\n                // However, the refetchQueries method accepts only true or false for its\n                // optimistic option (not string). We interpret true to mean a temporary\n                // optimistic layer should be created, to allow efficiently rolling back\n                // the effect of the updateCache function, which involves passing a\n                // string instead of true as the optimistic option to cache.batch, when\n                // refetchQueries receives optimistic: true.\n                //\n                // In other words, we are deliberately not supporting the use case of\n                // writing to an *existing* optimistic layer (using the refetchQueries\n                // updateCache function), since that would potentially interfere with\n                // other optimistic updates in progress. Instead, you can read/write\n                // only the root layer by passing optimistic: false to refetchQueries,\n                // or you can read/write a brand new optimistic layer that will be\n                // automatically removed by passing optimistic: true.\n                optimistic: (optimistic && removeOptimistic) || false,\n                // The removeOptimistic option can also be provided by itself, even if\n                // optimistic === false, to remove some previously-added optimistic\n                // layer safely and efficiently, like we do in markMutationResult.\n                //\n                // If an explicit removeOptimistic string is provided with optimistic:\n                // true, the removeOptimistic string will determine the ID of the\n                // temporary optimistic layer, in case that ever matters.\n                removeOptimistic: removeOptimistic,\n                onWatchUpdated: function (watch, diff, lastDiff) {\n                    var oq = watch.watcher instanceof QueryInfo && watch.watcher.observableQuery;\n                    if (oq) {\n                        if (onQueryUpdated) {\n                            // Since we're about to handle this query now, remove it from\n                            // includedQueriesById, in case it was added earlier because of\n                            // options.include.\n                            includedQueriesById.delete(oq.queryId);\n                            var result = onQueryUpdated(oq, diff, lastDiff);\n                            if (result === true) {\n                                // The onQueryUpdated function requested the default refetching\n                                // behavior by returning true.\n                                result = oq.refetch();\n                            }\n                            // Record the result in the results Map, as long as onQueryUpdated\n                            // did not return false to skip/ignore this result.\n                            if (result !== false) {\n                                results.set(oq, result);\n                            }\n                            // Allow the default cache broadcast to happen, except when\n                            // onQueryUpdated returns false.\n                            return result;\n                        }\n                        if (onQueryUpdated !== null) {\n                            // If we don't have an onQueryUpdated function, and onQueryUpdated\n                            // was not disabled by passing null, make sure this query is\n                            // \"included\" like any other options.include-specified query.\n                            includedQueriesById.set(oq.queryId, { oq: oq, lastDiff: lastDiff, diff: diff });\n                        }\n                    }\n                },\n            });\n        }\n        if (includedQueriesById.size) {\n            includedQueriesById.forEach(function (_a, queryId) {\n                var oq = _a.oq, lastDiff = _a.lastDiff, diff = _a.diff;\n                var result;\n                // If onQueryUpdated is provided, we want to use it for all included\n                // queries, even the QueryOptions ones.\n                if (onQueryUpdated) {\n                    if (!diff) {\n                        var info = oq[\"queryInfo\"];\n                        info.reset(); // Force info.getDiff() to read from cache.\n                        diff = info.getDiff();\n                    }\n                    result = onQueryUpdated(oq, diff, lastDiff);\n                }\n                // Otherwise, we fall back to refetching.\n                if (!onQueryUpdated || result === true) {\n                    result = oq.refetch();\n                }\n                if (result !== false) {\n                    results.set(oq, result);\n                }\n                if (queryId.indexOf(\"legacyOneTimeQuery\") >= 0) {\n                    _this.stopQueryNoBroadcast(queryId);\n                }\n            });\n        }\n        if (removeOptimistic) {\n            // In case no updateCache callback was provided (so cache.batch was not\n            // called above, and thus did not already remove the optimistic layer),\n            // remove it here. Since this is a no-op when the layer has already been\n            // removed, we do it even if we called cache.batch above, since it's\n            // possible this.cache is an instance of some ApolloCache subclass other\n            // than InMemoryCache, and does not fully support the removeOptimistic\n            // option for cache.batch.\n            this.cache.removeOptimistic(removeOptimistic);\n        }\n        return results;\n    };\n    QueryManager.prototype.maskOperation = function (options) {\n        var _a, _b, _c;\n        var document = options.document, data = options.data;\n        if (globalThis.__DEV__ !== false) {\n            var fetchPolicy = options.fetchPolicy, id = options.id;\n            var operationType = (_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation;\n            var operationId = ((_b = operationType === null || operationType === void 0 ? void 0 : operationType[0]) !== null && _b !== void 0 ? _b : \"o\") + id;\n            if (this.dataMasking &&\n                fetchPolicy === \"no-cache\" &&\n                !isFullyUnmaskedOperation(document) &&\n                !this.noCacheWarningsByQueryId.has(operationId)) {\n                this.noCacheWarningsByQueryId.add(operationId);\n                globalThis.__DEV__ !== false && invariant.warn(\n                    37,\n                    (_c = getOperationName(document)) !== null && _c !== void 0 ? _c : \"Unnamed \".concat(operationType !== null && operationType !== void 0 ? operationType : \"operation\")\n                );\n            }\n        }\n        return (this.dataMasking ?\n            maskOperation(data, document, this.cache)\n            : data);\n    };\n    QueryManager.prototype.maskFragment = function (options) {\n        var data = options.data, fragment = options.fragment, fragmentName = options.fragmentName;\n        return this.dataMasking ?\n            maskFragment(data, fragment, this.cache, fragmentName)\n            : data;\n    };\n    QueryManager.prototype.fetchQueryByPolicy = function (queryInfo, _a, \n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus) {\n        var _this = this;\n        var query = _a.query, variables = _a.variables, fetchPolicy = _a.fetchPolicy, refetchWritePolicy = _a.refetchWritePolicy, errorPolicy = _a.errorPolicy, returnPartialData = _a.returnPartialData, context = _a.context, notifyOnNetworkStatusChange = _a.notifyOnNetworkStatusChange;\n        var oldNetworkStatus = queryInfo.networkStatus;\n        queryInfo.init({\n            document: query,\n            variables: variables,\n            networkStatus: networkStatus,\n        });\n        var readCache = function () { return queryInfo.getDiff(); };\n        var resultsFromCache = function (diff, networkStatus) {\n            if (networkStatus === void 0) { networkStatus = queryInfo.networkStatus || NetworkStatus.loading; }\n            var data = diff.result;\n            if (globalThis.__DEV__ !== false && !returnPartialData && !equal(data, {})) {\n                logMissingFieldErrors(diff.missing);\n            }\n            var fromData = function (data) {\n                return Observable.of(__assign({ data: data, loading: isNetworkRequestInFlight(networkStatus), networkStatus: networkStatus }, (diff.complete ? null : { partial: true })));\n            };\n            if (data && _this.getDocumentInfo(query).hasForcedResolvers) {\n                return _this.localState\n                    .runResolvers({\n                    document: query,\n                    remoteResult: { data: data },\n                    context: context,\n                    variables: variables,\n                    onlyRunForcedResolvers: true,\n                })\n                    .then(function (resolved) { return fromData(resolved.data || void 0); });\n            }\n            // Resolves https://github.com/apollographql/apollo-client/issues/10317.\n            // If errorPolicy is 'none' and notifyOnNetworkStatusChange is true,\n            // data was incorrectly returned from the cache on refetch:\n            // if diff.missing exists, we should not return cache data.\n            if (errorPolicy === \"none\" &&\n                networkStatus === NetworkStatus.refetch &&\n                Array.isArray(diff.missing)) {\n                return fromData(void 0);\n            }\n            return fromData(data);\n        };\n        var cacheWriteBehavior = fetchPolicy === \"no-cache\" ? 0 /* CacheWriteBehavior.FORBID */\n            // Watched queries must opt into overwriting existing data on refetch,\n            // by passing refetchWritePolicy: \"overwrite\" in their WatchQueryOptions.\n            : (networkStatus === NetworkStatus.refetch &&\n                refetchWritePolicy !== \"merge\") ?\n                1 /* CacheWriteBehavior.OVERWRITE */\n                : 2 /* CacheWriteBehavior.MERGE */;\n        var resultsFromLink = function () {\n            return _this.getResultsFromLink(queryInfo, cacheWriteBehavior, {\n                query: query,\n                variables: variables,\n                context: context,\n                fetchPolicy: fetchPolicy,\n                errorPolicy: errorPolicy,\n            });\n        };\n        var shouldNotify = notifyOnNetworkStatusChange &&\n            typeof oldNetworkStatus === \"number\" &&\n            oldNetworkStatus !== networkStatus &&\n            isNetworkRequestInFlight(networkStatus);\n        switch (fetchPolicy) {\n            default:\n            case \"cache-first\": {\n                var diff = readCache();\n                if (diff.complete) {\n                    return {\n                        fromLink: false,\n                        sources: [resultsFromCache(diff, queryInfo.markReady())],\n                    };\n                }\n                if (returnPartialData || shouldNotify) {\n                    return {\n                        fromLink: true,\n                        sources: [resultsFromCache(diff), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            }\n            case \"cache-and-network\": {\n                var diff = readCache();\n                if (diff.complete || returnPartialData || shouldNotify) {\n                    return {\n                        fromLink: true,\n                        sources: [resultsFromCache(diff), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            }\n            case \"cache-only\":\n                return {\n                    fromLink: false,\n                    sources: [resultsFromCache(readCache(), queryInfo.markReady())],\n                };\n            case \"network-only\":\n                if (shouldNotify) {\n                    return {\n                        fromLink: true,\n                        sources: [resultsFromCache(readCache()), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            case \"no-cache\":\n                if (shouldNotify) {\n                    return {\n                        fromLink: true,\n                        // Note that queryInfo.getDiff() for no-cache queries does not call\n                        // cache.diff, but instead returns a { complete: false } stub result\n                        // when there is no queryInfo.diff already defined.\n                        sources: [resultsFromCache(queryInfo.getDiff()), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            case \"standby\":\n                return { fromLink: false, sources: [] };\n        }\n    };\n    QueryManager.prototype.getQuery = function (queryId) {\n        if (queryId && !this.queries.has(queryId)) {\n            this.queries.set(queryId, new QueryInfo(this, queryId));\n        }\n        return this.queries.get(queryId);\n    };\n    QueryManager.prototype.prepareContext = function (context) {\n        if (context === void 0) { context = {}; }\n        var newContext = this.localState.prepareContext(context);\n        return __assign(__assign(__assign({}, this.defaultContext), newContext), { clientAwareness: this.clientAwareness });\n    };\n    return QueryManager;\n}());\nexport { QueryManager };\n//# sourceMappingURL=QueryManager.js.map", "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { visit, BREAK, isSelectionNode } from \"graphql\";\nimport { argumentsObjectFromField, buildQueryFromSelectionSet, createFragmentMap, getFragmentDefinitions, getMainDefinition, hasDirectives, isField, isInlineFragment, mergeDeep, mergeDeepArray, removeClientSetsFromDocument, resultKeyNameFromField, shouldInclude, } from \"../utilities/index.js\";\nimport { cacheSlot } from \"../cache/index.js\";\nvar LocalState = /** @class */ (function () {\n    function LocalState(_a) {\n        var cache = _a.cache, client = _a.client, resolvers = _a.resolvers, fragmentMatcher = _a.fragmentMatcher;\n        this.selectionsToResolveCache = new WeakMap();\n        this.cache = cache;\n        if (client) {\n            this.client = client;\n        }\n        if (resolvers) {\n            this.addResolvers(resolvers);\n        }\n        if (fragmentMatcher) {\n            this.setFragmentMatcher(fragmentMatcher);\n        }\n    }\n    LocalState.prototype.addResolvers = function (resolvers) {\n        var _this = this;\n        this.resolvers = this.resolvers || {};\n        if (Array.isArray(resolvers)) {\n            resolvers.forEach(function (resolverGroup) {\n                _this.resolvers = mergeDeep(_this.resolvers, resolverGroup);\n            });\n        }\n        else {\n            this.resolvers = mergeDeep(this.resolvers, resolvers);\n        }\n    };\n    LocalState.prototype.setResolvers = function (resolvers) {\n        this.resolvers = {};\n        this.addResolvers(resolvers);\n    };\n    LocalState.prototype.getResolvers = function () {\n        return this.resolvers || {};\n    };\n    // Run local client resolvers against the incoming query and remote data.\n    // Locally resolved field values are merged with the incoming remote data,\n    // and returned. Note that locally resolved fields will overwrite\n    // remote data using the same field name.\n    LocalState.prototype.runResolvers = function (_a) {\n        return __awaiter(this, arguments, void 0, function (_b) {\n            var document = _b.document, remoteResult = _b.remoteResult, context = _b.context, variables = _b.variables, _c = _b.onlyRunForcedResolvers, onlyRunForcedResolvers = _c === void 0 ? false : _c;\n            return __generator(this, function (_d) {\n                if (document) {\n                    return [2 /*return*/, this.resolveDocument(document, remoteResult.data, context, variables, this.fragmentMatcher, onlyRunForcedResolvers).then(function (localResult) { return (__assign(__assign({}, remoteResult), { data: localResult.result })); })];\n                }\n                return [2 /*return*/, remoteResult];\n            });\n        });\n    };\n    LocalState.prototype.setFragmentMatcher = function (fragmentMatcher) {\n        this.fragmentMatcher = fragmentMatcher;\n    };\n    LocalState.prototype.getFragmentMatcher = function () {\n        return this.fragmentMatcher;\n    };\n    // Client queries contain everything in the incoming document (if a @client\n    // directive is found).\n    LocalState.prototype.clientQuery = function (document) {\n        if (hasDirectives([\"client\"], document)) {\n            if (this.resolvers) {\n                return document;\n            }\n        }\n        return null;\n    };\n    // Server queries are stripped of all @client based selection sets.\n    LocalState.prototype.serverQuery = function (document) {\n        return removeClientSetsFromDocument(document);\n    };\n    LocalState.prototype.prepareContext = function (context) {\n        var cache = this.cache;\n        return __assign(__assign({}, context), { cache: cache, \n            // Getting an entry's cache key is useful for local state resolvers.\n            getCacheKey: function (obj) {\n                return cache.identify(obj);\n            } });\n    };\n    // To support `@client @export(as: \"someVar\")` syntax, we'll first resolve\n    // @client @export fields locally, then pass the resolved values back to be\n    // used alongside the original operation variables.\n    LocalState.prototype.addExportedVariables = function (document_1) {\n        return __awaiter(this, arguments, void 0, function (document, variables, context) {\n            if (variables === void 0) { variables = {}; }\n            if (context === void 0) { context = {}; }\n            return __generator(this, function (_a) {\n                if (document) {\n                    return [2 /*return*/, this.resolveDocument(document, this.buildRootValueFromCache(document, variables) || {}, this.prepareContext(context), variables).then(function (data) { return (__assign(__assign({}, variables), data.exportedVariables)); })];\n                }\n                return [2 /*return*/, __assign({}, variables)];\n            });\n        });\n    };\n    LocalState.prototype.shouldForceResolvers = function (document) {\n        var forceResolvers = false;\n        visit(document, {\n            Directive: {\n                enter: function (node) {\n                    if (node.name.value === \"client\" && node.arguments) {\n                        forceResolvers = node.arguments.some(function (arg) {\n                            return arg.name.value === \"always\" &&\n                                arg.value.kind === \"BooleanValue\" &&\n                                arg.value.value === true;\n                        });\n                        if (forceResolvers) {\n                            return BREAK;\n                        }\n                    }\n                },\n            },\n        });\n        return forceResolvers;\n    };\n    // Query the cache and return matching data.\n    LocalState.prototype.buildRootValueFromCache = function (document, variables) {\n        return this.cache.diff({\n            query: buildQueryFromSelectionSet(document),\n            variables: variables,\n            returnPartialData: true,\n            optimistic: false,\n        }).result;\n    };\n    LocalState.prototype.resolveDocument = function (document_1, rootValue_1) {\n        return __awaiter(this, arguments, void 0, function (document, rootValue, context, variables, fragmentMatcher, onlyRunForcedResolvers) {\n            var mainDefinition, fragments, fragmentMap, selectionsToResolve, definitionOperation, defaultOperationType, _a, cache, client, execContext, isClientFieldDescendant;\n            if (context === void 0) { context = {}; }\n            if (variables === void 0) { variables = {}; }\n            if (fragmentMatcher === void 0) { fragmentMatcher = function () { return true; }; }\n            if (onlyRunForcedResolvers === void 0) { onlyRunForcedResolvers = false; }\n            return __generator(this, function (_b) {\n                mainDefinition = getMainDefinition(document);\n                fragments = getFragmentDefinitions(document);\n                fragmentMap = createFragmentMap(fragments);\n                selectionsToResolve = this.collectSelectionsToResolve(mainDefinition, fragmentMap);\n                definitionOperation = mainDefinition.operation;\n                defaultOperationType = definitionOperation ?\n                    definitionOperation.charAt(0).toUpperCase() +\n                        definitionOperation.slice(1)\n                    : \"Query\";\n                _a = this, cache = _a.cache, client = _a.client;\n                execContext = {\n                    fragmentMap: fragmentMap,\n                    context: __assign(__assign({}, context), { cache: cache, client: client }),\n                    variables: variables,\n                    fragmentMatcher: fragmentMatcher,\n                    defaultOperationType: defaultOperationType,\n                    exportedVariables: {},\n                    selectionsToResolve: selectionsToResolve,\n                    onlyRunForcedResolvers: onlyRunForcedResolvers,\n                };\n                isClientFieldDescendant = false;\n                return [2 /*return*/, this.resolveSelectionSet(mainDefinition.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (result) { return ({\n                        result: result,\n                        exportedVariables: execContext.exportedVariables,\n                    }); })];\n            });\n        });\n    };\n    LocalState.prototype.resolveSelectionSet = function (selectionSet, isClientFieldDescendant, rootValue, execContext) {\n        return __awaiter(this, void 0, void 0, function () {\n            var fragmentMap, context, variables, resultsToMerge, execute;\n            var _this = this;\n            return __generator(this, function (_a) {\n                fragmentMap = execContext.fragmentMap, context = execContext.context, variables = execContext.variables;\n                resultsToMerge = [rootValue];\n                execute = function (selection) { return __awaiter(_this, void 0, void 0, function () {\n                    var fragment, typeCondition;\n                    return __generator(this, function (_a) {\n                        if (!isClientFieldDescendant &&\n                            !execContext.selectionsToResolve.has(selection)) {\n                            // Skip selections without @client directives\n                            // (still processing if one of the ancestors or one of the child fields has @client directive)\n                            return [2 /*return*/];\n                        }\n                        if (!shouldInclude(selection, variables)) {\n                            // Skip this entirely.\n                            return [2 /*return*/];\n                        }\n                        if (isField(selection)) {\n                            return [2 /*return*/, this.resolveField(selection, isClientFieldDescendant, rootValue, execContext).then(function (fieldResult) {\n                                    var _a;\n                                    if (typeof fieldResult !== \"undefined\") {\n                                        resultsToMerge.push((_a = {},\n                                            _a[resultKeyNameFromField(selection)] = fieldResult,\n                                            _a));\n                                    }\n                                })];\n                        }\n                        if (isInlineFragment(selection)) {\n                            fragment = selection;\n                        }\n                        else {\n                            // This is a named fragment.\n                            fragment = fragmentMap[selection.name.value];\n                            invariant(fragment, 19, selection.name.value);\n                        }\n                        if (fragment && fragment.typeCondition) {\n                            typeCondition = fragment.typeCondition.name.value;\n                            if (execContext.fragmentMatcher(rootValue, typeCondition, context)) {\n                                return [2 /*return*/, this.resolveSelectionSet(fragment.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (fragmentResult) {\n                                        resultsToMerge.push(fragmentResult);\n                                    })];\n                            }\n                        }\n                        return [2 /*return*/];\n                    });\n                }); };\n                return [2 /*return*/, Promise.all(selectionSet.selections.map(execute)).then(function () {\n                        return mergeDeepArray(resultsToMerge);\n                    })];\n            });\n        });\n    };\n    LocalState.prototype.resolveField = function (field, isClientFieldDescendant, rootValue, execContext) {\n        return __awaiter(this, void 0, void 0, function () {\n            var variables, fieldName, aliasedFieldName, aliasUsed, defaultResult, resultPromise, resolverType, resolverMap, resolve;\n            var _this = this;\n            return __generator(this, function (_a) {\n                if (!rootValue) {\n                    return [2 /*return*/, null];\n                }\n                variables = execContext.variables;\n                fieldName = field.name.value;\n                aliasedFieldName = resultKeyNameFromField(field);\n                aliasUsed = fieldName !== aliasedFieldName;\n                defaultResult = rootValue[aliasedFieldName] || rootValue[fieldName];\n                resultPromise = Promise.resolve(defaultResult);\n                // Usually all local resolvers are run when passing through here, but\n                // if we've specifically identified that we only want to run forced\n                // resolvers (that is, resolvers for fields marked with\n                // `@client(always: true)`), then we'll skip running non-forced resolvers.\n                if (!execContext.onlyRunForcedResolvers ||\n                    this.shouldForceResolvers(field)) {\n                    resolverType = rootValue.__typename || execContext.defaultOperationType;\n                    resolverMap = this.resolvers && this.resolvers[resolverType];\n                    if (resolverMap) {\n                        resolve = resolverMap[aliasUsed ? fieldName : aliasedFieldName];\n                        if (resolve) {\n                            resultPromise = Promise.resolve(\n                            // In case the resolve function accesses reactive variables,\n                            // set cacheSlot to the current cache instance.\n                            cacheSlot.withValue(this.cache, resolve, [\n                                rootValue,\n                                argumentsObjectFromField(field, variables),\n                                execContext.context,\n                                { field: field, fragmentMap: execContext.fragmentMap },\n                            ]));\n                        }\n                    }\n                }\n                return [2 /*return*/, resultPromise.then(function (result) {\n                        var _a, _b;\n                        if (result === void 0) { result = defaultResult; }\n                        // If an @export directive is associated with the current field, store\n                        // the `as` export variable name and current result for later use.\n                        if (field.directives) {\n                            field.directives.forEach(function (directive) {\n                                if (directive.name.value === \"export\" && directive.arguments) {\n                                    directive.arguments.forEach(function (arg) {\n                                        if (arg.name.value === \"as\" && arg.value.kind === \"StringValue\") {\n                                            execContext.exportedVariables[arg.value.value] = result;\n                                        }\n                                    });\n                                }\n                            });\n                        }\n                        // Handle all scalar types here.\n                        if (!field.selectionSet) {\n                            return result;\n                        }\n                        // From here down, the field has a selection set, which means it's trying\n                        // to query a GraphQLObjectType.\n                        if (result == null) {\n                            // Basically any field in a GraphQL response can be null, or missing\n                            return result;\n                        }\n                        var isClientField = (_b = (_a = field.directives) === null || _a === void 0 ? void 0 : _a.some(function (d) { return d.name.value === \"client\"; })) !== null && _b !== void 0 ? _b : false;\n                        if (Array.isArray(result)) {\n                            return _this.resolveSubSelectedArray(field, isClientFieldDescendant || isClientField, result, execContext);\n                        }\n                        // Returned value is an object, and the query has a sub-selection. Recurse.\n                        if (field.selectionSet) {\n                            return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant || isClientField, result, execContext);\n                        }\n                    })];\n            });\n        });\n    };\n    LocalState.prototype.resolveSubSelectedArray = function (field, isClientFieldDescendant, result, execContext) {\n        var _this = this;\n        return Promise.all(result.map(function (item) {\n            if (item === null) {\n                return null;\n            }\n            // This is a nested array, recurse.\n            if (Array.isArray(item)) {\n                return _this.resolveSubSelectedArray(field, isClientFieldDescendant, item, execContext);\n            }\n            // This is an object, run the selection set on it.\n            if (field.selectionSet) {\n                return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant, item, execContext);\n            }\n        }));\n    };\n    // Collect selection nodes on paths from document root down to all @client directives.\n    // This function takes into account transitive fragment spreads.\n    // Complexity equals to a single `visit` over the full document.\n    LocalState.prototype.collectSelectionsToResolve = function (mainDefinition, fragmentMap) {\n        var isSingleASTNode = function (node) { return !Array.isArray(node); };\n        var selectionsToResolveCache = this.selectionsToResolveCache;\n        function collectByDefinition(definitionNode) {\n            if (!selectionsToResolveCache.has(definitionNode)) {\n                var matches_1 = new Set();\n                selectionsToResolveCache.set(definitionNode, matches_1);\n                visit(definitionNode, {\n                    Directive: function (node, _, __, ___, ancestors) {\n                        if (node.name.value === \"client\") {\n                            ancestors.forEach(function (node) {\n                                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                                    matches_1.add(node);\n                                }\n                            });\n                        }\n                    },\n                    FragmentSpread: function (spread, _, __, ___, ancestors) {\n                        var fragment = fragmentMap[spread.name.value];\n                        invariant(fragment, 20, spread.name.value);\n                        var fragmentSelections = collectByDefinition(fragment);\n                        if (fragmentSelections.size > 0) {\n                            // Fragment for this spread contains @client directive (either directly or transitively)\n                            // Collect selection nodes on paths from the root down to fields with the @client directive\n                            ancestors.forEach(function (node) {\n                                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                                    matches_1.add(node);\n                                }\n                            });\n                            matches_1.add(spread);\n                            fragmentSelections.forEach(function (selection) {\n                                matches_1.add(selection);\n                            });\n                        }\n                    },\n                });\n            }\n            return selectionsToResolveCache.get(definitionNode);\n        }\n        return collectByDefinition(mainDefinition);\n    };\n    return LocalState;\n}());\nexport { LocalState };\n//# sourceMappingURL=LocalState.js.map", "import { __assign } from \"tslib\";\nimport { global } from \"../globals/index.js\";\nvar cacheSizeSymbol = Symbol.for(\"apollo.cacheSize\");\n/**\n *\n * The global cache size configuration for Apollo Client.\n *\n * @remarks\n *\n * You can directly modify this object, but any modification will\n * only have an effect on caches that are created after the modification.\n *\n * So for global caches, such as `parser`, `canonicalStringify` and `print`,\n * you might need to call `.reset` on them, which will essentially re-create them.\n *\n * Alternatively, you can set `globalThis[Symbol.for(\"apollo.cacheSize\")]` before\n * you load the Apollo Client package:\n *\n * @example\n * ```ts\n * globalThis[Symbol.for(\"apollo.cacheSize\")] = {\n *   parser: 100\n * } satisfies Partial<CacheSizes> // the `satisfies` is optional if using TypeScript\n * ```\n */\nexport var cacheSizes = __assign({}, global[cacheSizeSymbol]);\n//# sourceMappingURL=sizes.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { cacheSizes } from \"./sizes.js\";\nvar globalCaches = {};\nexport function registerGlobalCache(name, getSize) {\n    globalCaches[name] = getSize;\n}\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloClientMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloClientMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getInMemoryCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getInMemoryCacheMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloCacheMemoryInternals\n    : undefined;\nfunction getCurrentCacheSizes() {\n    // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n    var defaults = {\n        parser: 1000 /* defaultCacheSizes[\"parser\"] */,\n        canonicalStringify: 1000 /* defaultCacheSizes[\"canonicalStringify\"] */,\n        print: 2000 /* defaultCacheSizes[\"print\"] */,\n        \"documentTransform.cache\": 2000 /* defaultCacheSizes[\"documentTransform.cache\"] */,\n        \"queryManager.getDocumentInfo\": 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */,\n        \"PersistedQueryLink.persistedQueryHashes\": 2000 /* defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] */,\n        \"fragmentRegistry.transform\": 2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n        \"fragmentRegistry.lookup\": 1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n        \"fragmentRegistry.findFragmentSpreads\": 4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n        \"cache.fragmentQueryDocuments\": 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n        \"removeTypenameFromVariables.getVariableDefinitions\": 2000 /* defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] */,\n        \"inMemoryCache.maybeBroadcastWatch\": 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n        \"inMemoryCache.executeSelectionSet\": 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n        \"inMemoryCache.executeSubSelectedArray\": 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n    };\n    return Object.fromEntries(Object.entries(defaults).map(function (_a) {\n        var k = _a[0], v = _a[1];\n        return [\n            k,\n            cacheSizes[k] || v,\n        ];\n    }));\n}\nfunction _getApolloClientMemoryInternals() {\n    var _a, _b, _c, _d, _e;\n    if (!(globalThis.__DEV__ !== false))\n        throw new Error(\"only supported in development mode\");\n    return {\n        limits: getCurrentCacheSizes(),\n        sizes: __assign({ print: (_a = globalCaches.print) === null || _a === void 0 ? void 0 : _a.call(globalCaches), parser: (_b = globalCaches.parser) === null || _b === void 0 ? void 0 : _b.call(globalCaches), canonicalStringify: (_c = globalCaches.canonicalStringify) === null || _c === void 0 ? void 0 : _c.call(globalCaches), links: linkInfo(this.link), queryManager: {\n                getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n                documentTransforms: transformInfo(this[\"queryManager\"].documentTransform),\n            } }, (_e = (_d = this.cache).getMemoryInternals) === null || _e === void 0 ? void 0 : _e.call(_d)),\n    };\n}\nfunction _getApolloCacheMemoryInternals() {\n    return {\n        cache: {\n            fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n        },\n    };\n}\nfunction _getInMemoryCacheMemoryInternals() {\n    var fragments = this.config.fragments;\n    return __assign(__assign({}, _getApolloCacheMemoryInternals.apply(this)), { addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]), inMemoryCache: {\n            executeSelectionSet: getWrapperInformation(this[\"storeReader\"][\"executeSelectionSet\"]),\n            executeSubSelectedArray: getWrapperInformation(this[\"storeReader\"][\"executeSubSelectedArray\"]),\n            maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n        }, fragmentRegistry: {\n            findFragmentSpreads: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.findFragmentSpreads),\n            lookup: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.lookup),\n            transform: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.transform),\n        } });\n}\nfunction isWrapper(f) {\n    return !!f && \"dirtyKey\" in f;\n}\nfunction getWrapperInformation(f) {\n    return isWrapper(f) ? f.size : undefined;\n}\nfunction isDefined(value) {\n    return value != null;\n}\nfunction transformInfo(transform) {\n    return recurseTransformInfo(transform).map(function (cache) { return ({ cache: cache }); });\n}\nfunction recurseTransformInfo(transform) {\n    return transform ?\n        __spreadArray(__spreadArray([\n            getWrapperInformation(transform === null || transform === void 0 ? void 0 : transform[\"performWork\"])\n        ], recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"left\"]), true), recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"right\"]), true).filter(isDefined)\n        : [];\n}\nfunction linkInfo(link) {\n    var _a;\n    return link ?\n        __spreadArray(__spreadArray([\n            (_a = link === null || link === void 0 ? void 0 : link.getMemoryInternals) === null || _a === void 0 ? void 0 : _a.call(link)\n        ], linkInfo(link === null || link === void 0 ? void 0 : link.left), true), linkInfo(link === null || link === void 0 ? void 0 : link.right), true).filter(isDefined)\n        : [];\n}\n//# sourceMappingURL=getMemoryInternals.js.map", "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { ApolloLink, execute } from \"../link/core/index.js\";\nimport { version } from \"../version.js\";\nimport { HttpLink } from \"../link/http/index.js\";\nimport { QueryManager } from \"./QueryManager.js\";\nimport { LocalState } from \"./LocalState.js\";\nvar hasSuggestedDevtools = false;\n// Though mergeOptions now resides in @apollo/client/utilities, it was\n// previously declared and exported from this module, and then reexported from\n// @apollo/client/core. Since we need to preserve that API anyway, the easiest\n// solution is to reexport mergeOptions where it was previously declared (here).\nimport { mergeOptions } from \"../utilities/index.js\";\nimport { getApolloClientMemoryInternals } from \"../utilities/caching/getMemoryInternals.js\";\nexport { mergeOptions };\n/**\n * This is the primary Apollo Client class. It is used to send GraphQL documents (i.e. queries\n * and mutations) to a GraphQL spec-compliant server over an `ApolloLink` instance,\n * receive results from the server and cache the results in a store. It also delivers updates\n * to GraphQL queries through `Observable` instances.\n */\nvar ApolloClient = /** @class */ (function () {\n    /**\n     * Constructs an instance of `ApolloClient`.\n     *\n     * @example\n     * ```js\n     * import { ApolloClient, InMemoryCache } from '@apollo/client';\n     *\n     * const cache = new InMemoryCache();\n     *\n     * const client = new ApolloClient({\n     *   // Provide required constructor fields\n     *   cache: cache,\n     *   uri: 'http://localhost:4000/',\n     *\n     *   // Provide some optional constructor fields\n     *   name: 'react-web-client',\n     *   version: '1.3',\n     *   queryDeduplication: false,\n     *   defaultOptions: {\n     *     watchQuery: {\n     *       fetchPolicy: 'cache-and-network',\n     *     },\n     *   },\n     * });\n     * ```\n     */\n    function ApolloClient(options) {\n        var _this = this;\n        var _a;\n        this.resetStoreCallbacks = [];\n        this.clearStoreCallbacks = [];\n        if (!options.cache) {\n            throw newInvariantError(16);\n        }\n        var uri = options.uri, credentials = options.credentials, headers = options.headers, cache = options.cache, documentTransform = options.documentTransform, _b = options.ssrMode, ssrMode = _b === void 0 ? false : _b, _c = options.ssrForceFetchDelay, ssrForceFetchDelay = _c === void 0 ? 0 : _c, \n        // Expose the client instance as window.__APOLLO_CLIENT__ and call\n        // onBroadcast in queryManager.broadcastQueries to enable browser\n        // devtools, but disable them by default in production.\n        connectToDevTools = options.connectToDevTools, _d = options.queryDeduplication, queryDeduplication = _d === void 0 ? true : _d, defaultOptions = options.defaultOptions, defaultContext = options.defaultContext, _e = options.assumeImmutableResults, assumeImmutableResults = _e === void 0 ? cache.assumeImmutableResults : _e, resolvers = options.resolvers, typeDefs = options.typeDefs, fragmentMatcher = options.fragmentMatcher, clientAwarenessName = options.name, clientAwarenessVersion = options.version, devtools = options.devtools, dataMasking = options.dataMasking;\n        var link = options.link;\n        if (!link) {\n            link =\n                uri ? new HttpLink({ uri: uri, credentials: credentials, headers: headers }) : ApolloLink.empty();\n        }\n        this.link = link;\n        this.cache = cache;\n        this.disableNetworkFetches = ssrMode || ssrForceFetchDelay > 0;\n        this.queryDeduplication = queryDeduplication;\n        this.defaultOptions = defaultOptions || Object.create(null);\n        this.typeDefs = typeDefs;\n        this.devtoolsConfig = __assign(__assign({}, devtools), { enabled: (_a = devtools === null || devtools === void 0 ? void 0 : devtools.enabled) !== null && _a !== void 0 ? _a : connectToDevTools });\n        if (this.devtoolsConfig.enabled === undefined) {\n            this.devtoolsConfig.enabled = globalThis.__DEV__ !== false;\n        }\n        if (ssrForceFetchDelay) {\n            setTimeout(function () { return (_this.disableNetworkFetches = false); }, ssrForceFetchDelay);\n        }\n        this.watchQuery = this.watchQuery.bind(this);\n        this.query = this.query.bind(this);\n        this.mutate = this.mutate.bind(this);\n        this.watchFragment = this.watchFragment.bind(this);\n        this.resetStore = this.resetStore.bind(this);\n        this.reFetchObservableQueries = this.reFetchObservableQueries.bind(this);\n        this.version = version;\n        this.localState = new LocalState({\n            cache: cache,\n            client: this,\n            resolvers: resolvers,\n            fragmentMatcher: fragmentMatcher,\n        });\n        this.queryManager = new QueryManager({\n            cache: this.cache,\n            link: this.link,\n            defaultOptions: this.defaultOptions,\n            defaultContext: defaultContext,\n            documentTransform: documentTransform,\n            queryDeduplication: queryDeduplication,\n            ssrMode: ssrMode,\n            dataMasking: !!dataMasking,\n            clientAwareness: {\n                name: clientAwarenessName,\n                version: clientAwarenessVersion,\n            },\n            localState: this.localState,\n            assumeImmutableResults: assumeImmutableResults,\n            onBroadcast: this.devtoolsConfig.enabled ?\n                function () {\n                    if (_this.devToolsHookCb) {\n                        _this.devToolsHookCb({\n                            action: {},\n                            state: {\n                                queries: _this.queryManager.getQueryStore(),\n                                mutations: _this.queryManager.mutationStore || {},\n                            },\n                            dataWithOptimisticResults: _this.cache.extract(true),\n                        });\n                    }\n                }\n                : void 0,\n        });\n        if (this.devtoolsConfig.enabled)\n            this.connectToDevTools();\n    }\n    ApolloClient.prototype.connectToDevTools = function () {\n        if (typeof window === \"undefined\") {\n            return;\n        }\n        var windowWithDevTools = window;\n        var devtoolsSymbol = Symbol.for(\"apollo.devtools\");\n        (windowWithDevTools[devtoolsSymbol] =\n            windowWithDevTools[devtoolsSymbol] || []).push(this);\n        windowWithDevTools.__APOLLO_CLIENT__ = this;\n        /**\n         * Suggest installing the devtools for developers who don't have them\n         */\n        if (!hasSuggestedDevtools && globalThis.__DEV__ !== false) {\n            hasSuggestedDevtools = true;\n            if (window.document &&\n                window.top === window.self &&\n                /^(https?|file):$/.test(window.location.protocol)) {\n                setTimeout(function () {\n                    if (!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__) {\n                        var nav = window.navigator;\n                        var ua = nav && nav.userAgent;\n                        var url = void 0;\n                        if (typeof ua === \"string\") {\n                            if (ua.indexOf(\"Chrome/\") > -1) {\n                                url =\n                                    \"https://chrome.google.com/webstore/detail/\" +\n                                        \"apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm\";\n                            }\n                            else if (ua.indexOf(\"Firefox/\") > -1) {\n                                url =\n                                    \"https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/\";\n                            }\n                        }\n                        if (url) {\n                            globalThis.__DEV__ !== false && invariant.log(\"Download the Apollo DevTools for a better development \" +\n                                \"experience: %s\", url);\n                        }\n                    }\n                }, 10000);\n            }\n        }\n    };\n    Object.defineProperty(ApolloClient.prototype, \"documentTransform\", {\n        /**\n         * The `DocumentTransform` used to modify GraphQL documents before a request\n         * is made. If a custom `DocumentTransform` is not provided, this will be the\n         * default document transform.\n         */\n        get: function () {\n            return this.queryManager.documentTransform;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Call this method to terminate any active client processes, making it safe\n     * to dispose of this `ApolloClient` instance.\n     */\n    ApolloClient.prototype.stop = function () {\n        this.queryManager.stop();\n    };\n    /**\n     * This watches the cache store of the query according to the options specified and\n     * returns an `ObservableQuery`. We can subscribe to this `ObservableQuery` and\n     * receive updated results through an observer when the cache store changes.\n     *\n     * Note that this method is not an implementation of GraphQL subscriptions. Rather,\n     * it uses Apollo's store in order to reactively deliver updates to your query results.\n     *\n     * For example, suppose you call watchQuery on a GraphQL query that fetches a person's\n     * first and last name and this person has a particular object identifier, provided by\n     * dataIdFromObject. Later, a different query fetches that same person's\n     * first and last name and the first name has now changed. Then, any observers associated\n     * with the results of the first query will be updated with a new result object.\n     *\n     * Note that if the cache does not change, the subscriber will *not* be notified.\n     *\n     * See [here](https://medium.com/apollo-stack/the-concepts-of-graphql-bc68bd819be3#.3mb0cbcmc) for\n     * a description of store reactivity.\n     */\n    ApolloClient.prototype.watchQuery = function (options) {\n        if (this.defaultOptions.watchQuery) {\n            options = mergeOptions(this.defaultOptions.watchQuery, options);\n        }\n        // XXX Overwriting options is probably not the best way to do this long term...\n        if (this.disableNetworkFetches &&\n            (options.fetchPolicy === \"network-only\" ||\n                options.fetchPolicy === \"cache-and-network\")) {\n            options = __assign(__assign({}, options), { fetchPolicy: \"cache-first\" });\n        }\n        return this.queryManager.watchQuery(options);\n    };\n    /**\n     * This resolves a single query according to the options specified and\n     * returns a `Promise` which is either resolved with the resulting data\n     * or rejected with an error.\n     *\n     * @param options - An object of type `QueryOptions` that allows us to\n     * describe how this query should be treated e.g. whether it should hit the\n     * server at all or just resolve from the cache, etc.\n     */\n    ApolloClient.prototype.query = function (options) {\n        if (this.defaultOptions.query) {\n            options = mergeOptions(this.defaultOptions.query, options);\n        }\n        invariant(options.fetchPolicy !== \"cache-and-network\", 17);\n        if (this.disableNetworkFetches && options.fetchPolicy === \"network-only\") {\n            options = __assign(__assign({}, options), { fetchPolicy: \"cache-first\" });\n        }\n        return this.queryManager.query(options);\n    };\n    /**\n     * This resolves a single mutation according to the options specified and returns a\n     * Promise which is either resolved with the resulting data or rejected with an\n     * error. In some cases both `data` and `errors` might be undefined, for example\n     * when `errorPolicy` is set to `'ignore'`.\n     *\n     * It takes options as an object with the following keys and values:\n     */\n    ApolloClient.prototype.mutate = function (options) {\n        if (this.defaultOptions.mutate) {\n            options = mergeOptions(this.defaultOptions.mutate, options);\n        }\n        return this.queryManager.mutate(options);\n    };\n    /**\n     * This subscribes to a graphql subscription according to the options specified and returns an\n     * `Observable` which either emits received data or an error.\n     */\n    ApolloClient.prototype.subscribe = function (options) {\n        var _this = this;\n        var id = this.queryManager.generateQueryId();\n        return this.queryManager\n            .startGraphQLSubscription(options)\n            .map(function (result) { return (__assign(__assign({}, result), { data: _this.queryManager.maskOperation({\n                document: options.query,\n                data: result.data,\n                fetchPolicy: options.fetchPolicy,\n                id: id,\n            }) })); });\n    };\n    /**\n     * Tries to read some data from the store in the shape of the provided\n     * GraphQL query without making a network request. This method will start at\n     * the root query. To start at a specific id returned by `dataIdFromObject`\n     * use `readFragment`.\n     *\n     * @param optimistic - Set to `true` to allow `readQuery` to return\n     * optimistic results. Is `false` by default.\n     */\n    ApolloClient.prototype.readQuery = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = false; }\n        return this.cache.readQuery(options, optimistic);\n    };\n    /**\n     * Watches the cache store of the fragment according to the options specified\n     * and returns an `Observable`. We can subscribe to this\n     * `Observable` and receive updated results through an\n     * observer when the cache store changes.\n     *\n     * You must pass in a GraphQL document with a single fragment or a document\n     * with multiple fragments that represent what you are reading. If you pass\n     * in a document with multiple fragments then you must also specify a\n     * `fragmentName`.\n     *\n     * @since 3.10.0\n     * @param options - An object of type `WatchFragmentOptions` that allows\n     * the cache to identify the fragment and optionally specify whether to react\n     * to optimistic updates.\n     */\n    ApolloClient.prototype.watchFragment = function (options) {\n        var _a;\n        return this.cache.watchFragment(__assign(__assign({}, options), (_a = {}, _a[Symbol.for(\"apollo.dataMasking\")] = this.queryManager.dataMasking, _a)));\n    };\n    /**\n     * Tries to read some data from the store in the shape of the provided\n     * GraphQL fragment without making a network request. This method will read a\n     * GraphQL fragment from any arbitrary id that is currently cached, unlike\n     * `readQuery` which will only read from the root query.\n     *\n     * You must pass in a GraphQL document with a single fragment or a document\n     * with multiple fragments that represent what you are reading. If you pass\n     * in a document with multiple fragments then you must also specify a\n     * `fragmentName`.\n     *\n     * @param optimistic - Set to `true` to allow `readFragment` to return\n     * optimistic results. Is `false` by default.\n     */\n    ApolloClient.prototype.readFragment = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = false; }\n        return this.cache.readFragment(options, optimistic);\n    };\n    /**\n     * Writes some data in the shape of the provided GraphQL query directly to\n     * the store. This method will start at the root query. To start at a\n     * specific id returned by `dataIdFromObject` then use `writeFragment`.\n     */\n    ApolloClient.prototype.writeQuery = function (options) {\n        var ref = this.cache.writeQuery(options);\n        if (options.broadcast !== false) {\n            this.queryManager.broadcastQueries();\n        }\n        return ref;\n    };\n    /**\n     * Writes some data in the shape of the provided GraphQL fragment directly to\n     * the store. This method will write to a GraphQL fragment from any arbitrary\n     * id that is currently cached, unlike `writeQuery` which will only write\n     * from the root query.\n     *\n     * You must pass in a GraphQL document with a single fragment or a document\n     * with multiple fragments that represent what you are writing. If you pass\n     * in a document with multiple fragments then you must also specify a\n     * `fragmentName`.\n     */\n    ApolloClient.prototype.writeFragment = function (options) {\n        var ref = this.cache.writeFragment(options);\n        if (options.broadcast !== false) {\n            this.queryManager.broadcastQueries();\n        }\n        return ref;\n    };\n    ApolloClient.prototype.__actionHookForDevTools = function (cb) {\n        this.devToolsHookCb = cb;\n    };\n    ApolloClient.prototype.__requestRaw = function (payload) {\n        return execute(this.link, payload);\n    };\n    /**\n     * Resets your entire store by clearing out your cache and then re-executing\n     * all of your active queries. This makes it so that you may guarantee that\n     * there is no data left in your store from a time before you called this\n     * method.\n     *\n     * `resetStore()` is useful when your user just logged out. You’ve removed the\n     * user session, and you now want to make sure that any references to data you\n     * might have fetched while the user session was active is gone.\n     *\n     * It is important to remember that `resetStore()` *will* refetch any active\n     * queries. This means that any components that might be mounted will execute\n     * their queries again using your network interface. If you do not want to\n     * re-execute any queries then you should make sure to stop watching any\n     * active queries.\n     */\n    ApolloClient.prototype.resetStore = function () {\n        var _this = this;\n        return Promise.resolve()\n            .then(function () {\n            return _this.queryManager.clearStore({\n                discardWatches: false,\n            });\n        })\n            .then(function () { return Promise.all(_this.resetStoreCallbacks.map(function (fn) { return fn(); })); })\n            .then(function () { return _this.reFetchObservableQueries(); });\n    };\n    /**\n     * Remove all data from the store. Unlike `resetStore`, `clearStore` will\n     * not refetch any active queries.\n     */\n    ApolloClient.prototype.clearStore = function () {\n        var _this = this;\n        return Promise.resolve()\n            .then(function () {\n            return _this.queryManager.clearStore({\n                discardWatches: true,\n            });\n        })\n            .then(function () { return Promise.all(_this.clearStoreCallbacks.map(function (fn) { return fn(); })); });\n    };\n    /**\n     * Allows callbacks to be registered that are executed when the store is\n     * reset. `onResetStore` returns an unsubscribe function that can be used\n     * to remove registered callbacks.\n     */\n    ApolloClient.prototype.onResetStore = function (cb) {\n        var _this = this;\n        this.resetStoreCallbacks.push(cb);\n        return function () {\n            _this.resetStoreCallbacks = _this.resetStoreCallbacks.filter(function (c) { return c !== cb; });\n        };\n    };\n    /**\n     * Allows callbacks to be registered that are executed when the store is\n     * cleared. `onClearStore` returns an unsubscribe function that can be used\n     * to remove registered callbacks.\n     */\n    ApolloClient.prototype.onClearStore = function (cb) {\n        var _this = this;\n        this.clearStoreCallbacks.push(cb);\n        return function () {\n            _this.clearStoreCallbacks = _this.clearStoreCallbacks.filter(function (c) { return c !== cb; });\n        };\n    };\n    /**\n     * Refetches all of your active queries.\n     *\n     * `reFetchObservableQueries()` is useful if you want to bring the client back to proper state in case of a network outage\n     *\n     * It is important to remember that `reFetchObservableQueries()` *will* refetch any active\n     * queries. This means that any components that might be mounted will execute\n     * their queries again using your network interface. If you do not want to\n     * re-execute any queries then you should make sure to stop watching any\n     * active queries.\n     * Takes optional parameter `includeStandby` which will include queries in standby-mode when refetching.\n     */\n    ApolloClient.prototype.reFetchObservableQueries = function (includeStandby) {\n        return this.queryManager.reFetchObservableQueries(includeStandby);\n    };\n    /**\n     * Refetches specified active queries. Similar to \"reFetchObservableQueries()\" but with a specific list of queries.\n     *\n     * `refetchQueries()` is useful for use cases to imperatively refresh a selection of queries.\n     *\n     * It is important to remember that `refetchQueries()` *will* refetch specified active\n     * queries. This means that any components that might be mounted will execute\n     * their queries again using your network interface. If you do not want to\n     * re-execute any queries then you should make sure to stop watching any\n     * active queries.\n     */\n    ApolloClient.prototype.refetchQueries = function (options) {\n        var map = this.queryManager.refetchQueries(options);\n        var queries = [];\n        var results = [];\n        map.forEach(function (result, obsQuery) {\n            queries.push(obsQuery);\n            results.push(result);\n        });\n        var result = Promise.all(results);\n        // In case you need the raw results immediately, without awaiting\n        // Promise.all(results):\n        result.queries = queries;\n        result.results = results;\n        // If you decide to ignore the result Promise because you're using\n        // result.queries and result.results instead, you shouldn't have to worry\n        // about preventing uncaught rejections for the Promise.all result.\n        result.catch(function (error) {\n            globalThis.__DEV__ !== false && invariant.debug(18, error);\n        });\n        return result;\n    };\n    /**\n     * Get all currently active `ObservableQuery` objects, in a `Map` keyed by\n     * query ID strings.\n     *\n     * An \"active\" query is one that has observers and a `fetchPolicy` other than\n     * \"standby\" or \"cache-only\".\n     *\n     * You can include all `ObservableQuery` objects (including the inactive ones)\n     * by passing \"all\" instead of \"active\", or you can include just a subset of\n     * active queries by passing an array of query names or DocumentNode objects.\n     */\n    ApolloClient.prototype.getObservableQueries = function (include) {\n        if (include === void 0) { include = \"active\"; }\n        return this.queryManager.getObservableQueries(include);\n    };\n    /**\n     * Exposes the cache's complete state, in a serializable format for later restoration.\n     */\n    ApolloClient.prototype.extract = function (optimistic) {\n        return this.cache.extract(optimistic);\n    };\n    /**\n     * Replaces existing state in the cache (if any) with the values expressed by\n     * `serializedState`.\n     *\n     * Called when hydrating a cache (server side rendering, or offline storage),\n     * and also (potentially) during hot reloads.\n     */\n    ApolloClient.prototype.restore = function (serializedState) {\n        return this.cache.restore(serializedState);\n    };\n    /**\n     * Add additional local resolvers.\n     */\n    ApolloClient.prototype.addResolvers = function (resolvers) {\n        this.localState.addResolvers(resolvers);\n    };\n    /**\n     * Set (override existing) local resolvers.\n     */\n    ApolloClient.prototype.setResolvers = function (resolvers) {\n        this.localState.setResolvers(resolvers);\n    };\n    /**\n     * Get all registered local resolvers.\n     */\n    ApolloClient.prototype.getResolvers = function () {\n        return this.localState.getResolvers();\n    };\n    /**\n     * Set a custom local state fragment matcher.\n     */\n    ApolloClient.prototype.setLocalStateFragmentMatcher = function (fragmentMatcher) {\n        this.localState.setFragmentMatcher(fragmentMatcher);\n    };\n    /**\n     * Define a new ApolloLink (or link chain) that Apollo Client will use.\n     */\n    ApolloClient.prototype.setLink = function (newLink) {\n        this.link = this.queryManager.link = newLink;\n    };\n    Object.defineProperty(ApolloClient.prototype, \"defaultContext\", {\n        get: function () {\n            return this.queryManager.defaultContext;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ApolloClient;\n}());\nexport { ApolloClient };\nif (globalThis.__DEV__ !== false) {\n    ApolloClient.prototype.getMemoryInternals = getApolloClientMemoryInternals;\n}\n//# sourceMappingURL=ApolloClient.js.map", "/* Core */\nexport { ApolloClient, mergeOptions } from \"./ApolloClient.js\";\nexport { ObservableQuery } from \"./ObservableQuery.js\";\nexport { NetworkStatus, isNetworkRequestSettled } from \"./networkStatus.js\";\nexport { isApolloError, ApolloError } from \"../errors/index.js\";\nexport { Cache, ApolloCache, InMemoryCache, MissingFieldError, defaultDataIdFromObject, makeVar, } from \"../cache/index.js\";\n/* Link */\nexport * from \"../link/core/index.js\";\nexport * from \"../link/http/index.js\";\nexport { fromError, toPromise, fromPromise, throwServerError, } from \"../link/utils/index.js\";\nexport { DocumentTransform, Observable, isReference, makeReference, } from \"../utilities/index.js\";\n/* Supporting */\n// The verbosity of invariant.{log,warn,error} can be controlled globally\n// (for anyone using the same ts-invariant package) by passing \"log\",\n// \"warn\", \"error\", or \"silent\" to setVerbosity (\"log\" is the default).\n// Note that all invariant.* logging is hidden in production.\nimport { setVerbosity } from \"ts-invariant\";\nexport { setVerbosity as setLogVerbosity };\nsetVerbosity(globalThis.__DEV__ !== false ? \"log\" : \"silent\");\n// Note that importing `gql` by itself, then destructuring\n// additional properties separately before exporting, is intentional.\n// Due to the way the `graphql-tag` library is setup, certain bundlers\n// can't find the properties added to the exported `gql` function without\n// additional guidance (e.g. Rollup - see\n// https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module).\n// Instead of having people that are using bundlers with `@apollo/client` add\n// extra bundler config to help `graphql-tag` exports be found (which would be\n// awkward since they aren't importing `graphql-tag` themselves), this\n// workaround of pulling the extra properties off the `gql` function,\n// then re-exporting them separately, helps keeps bundlers happy without any\n// additional config changes.\nexport { gql, resetCaches, disableFragmentWarnings, enableExperimentalFragmentVariables, disableExperimentalFragmentVariables, } from \"graphql-tag\";\n//# sourceMappingURL=index.js.map"], "names": ["hasOwnProperty", "__spread<PERSON><PERSON>y", "__assign", "NetworkStatus", "__rest", "equal", "getMainDefinition", "createFragmentMap", "getFragmentDefinitions", "shouldInclude", "isField", "resultKeyNameFromField", "getFragmentFromSelection", "__extends", "getOperationDefinition", "getQueryDefinition", "invariant", "compact", "cloneDeep", "isApolloError", "ApolloError", "preventUnhandledRejection", "iterateObserversSafely", "Observable", "fixObservableSubclass", "canUseWeakMap", "DeepMerger", "isNonEmptyArray", "mergeIncrementalData", "graphQLResultHasError", "AutoCleanedWeakCache", "cacheSizes", "<PERSON><PERSON>", "DocumentTransform", "newInvariantError", "__awaiter", "__generator", "asyncMap", "getGraphQLErrorsFromResult", "isExecutionPatchIncrementalResult", "getOperationName", "isExecutionPatchResult", "hasClientExports", "hasDirectives", "addNonReactiveToNamedFragments", "removeDirectivesFromDocument", "getDefaultValues", "isDocumentNode", "print", "isNonNullObject", "makeUniqueId", "graphQLResultHasProtocolErrors", "errors", "PROTOCOL_ERRORS_SYMBOL", "canonicalStringify", "Concast", "execute", "isFullyUnmaskedOperation", "maskOperation", "maskFragment", "mergeDeep", "removeClientSetsFromDocument", "visit", "BREAK", "buildQueryFromSelectionSet", "isInlineFragment", "mergeDeepArray", "cacheSlot", "argumentsObjectFromField", "isSelectionNode", "global", "HttpLink", "ApolloLink", "mergeOptions", "setVerbosity"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAI,OAAO,GAAG,QAAQ;;ACAtB,SAAS,eAAe,CAAC,GAAG,EAAE;AACrC,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AACnD;;ACAO,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD;;ACFA,IAAIA,gBAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAyBrD,IAAI,iBAAiB,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC5D,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC;AACF,IAAI,UAAU,KAAkB,YAAY;AAC5C,IAAI,SAAS,UAAU,CAAC,UAAU,EAAE;AACpC,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,iBAAiB,CAAC,EAAE;AACtE,QAAQ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACrC,QAAQ,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;AACxC,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;AACpC,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC;AACzB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE;AAChE,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC7D,gBAAgB,IAAIA,gBAAc,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AAC5D,oBAAoB,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACxD,oBAAoB,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,WAAW,EAAE;AAC3D,wBAAwB,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAEC,mBAAa,CAAC,CAAC,MAAM;AACxF,4BAA4B,MAAM;AAClC,4BAA4B,SAAS,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AAGzD,wBAAwB,IAAI,MAAM,KAAK,WAAW,EAAE;AACpD,4BAA4B,MAAM,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACvE,4BAA4B,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;AACvD,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB;AAGrB,oBAAoB,MAAM,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC/D,oBAAoB,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1D,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AAET,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,KAAK,EAAE;AAChE,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;AACpC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC7C,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,oBAAoB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3C,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,KAAK,GAAGC,cAAQ,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACzF,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,EAAE,CAAC;;ACnFG,SAAS,iCAAiC,CAAC,KAAK,EAAE;AACzD,IAAI,OAAO,aAAa,IAAI,KAAK,CAAC;AAClC,CAAC;AAcM,SAAS,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE;AACzD,IAAI,IAAI,UAAU,GAAG,UAAU,CAAC;AAChC,IAAI,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAClC,IAAI,IAAI,iCAAiC,CAAC,MAAM,CAAC;AACjD,QAAQ,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAC7C,QAAQ,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AACjD,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC/C,YAAY,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACvD,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC,gBAAgB,IAAI,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,gBAAgB,IAAI,QAAQ,GAAG,YAAY,GAAG,EAAE,GAAG,EAAE,CAAC;AACtD,gBAAgB,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACrC,gBAAgB,IAAI,GAAG,QAAQ,CAAC;AAChC,aAAa;AACb,YAAY,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACxD,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB;;AClCWC,+BAAc;AACzB,CAAC,UAAU,aAAa,EAAE;AAM1B,IAAI,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;AAK5D,IAAI,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC;AAKtE,IAAI,aAAa,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;AAKhE,IAAI,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;AAM5D,IAAI,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;AAItD,IAAI,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AAIxD,IAAI,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AACxD,CAAC,EAAEA,qBAAa,KAAKA,qBAAa,GAAG,EAAE,CAAC,CAAC,CAAC;AAKnC,SAAS,wBAAwB,CAAC,aAAa,EAAE;AACxD,IAAI,OAAO,aAAa,GAAG,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC;AACrD,CAAC;AAKM,SAAS,uBAAuB,CAAC,aAAa,EAAE;AACvD,IAAI,OAAO,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,CAAC;AACtD;;ACjDO,SAAS,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;AACvD,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAGC,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAGA,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,IAAI,QAAQC,cAAK,CAAC,KAAK,EAAE,KAAK,CAAC;AAC/B,QAAQ,mBAAmB,CAACC,2BAAiB,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE;AACjF,YAAY,WAAW,EAAEC,2BAAiB,CAACC,gCAAsB,CAAC,KAAK,CAAC,CAAC;AACzE,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,CAAC,EAAE;AACZ,CAAC;AACD,SAAS,mBAAmB,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE;AACtE,IAAI,IAAI,OAAO,KAAK,OAAO,EAAE;AAC7B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;AAInC,IAAI,OAAO,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,SAAS,EAAE;AAG9D,QAAQ,IAAI,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC;AACzC,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAEtC,QAAQ,IAAI,CAACC,uBAAa,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;AACxD,YAAY,OAAO,IAAI,CAAC;AAGxB,QAAQ,IAAI,gCAAgC,CAAC,SAAS,CAAC;AACvD,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,IAAIC,iBAAO,CAAC,SAAS,CAAC,EAAE;AAChC,YAAY,IAAI,SAAS,GAAGC,gCAAsB,CAAC,SAAS,CAAC,CAAC;AAC9D,YAAY,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7D,YAAY,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7D,YAAY,IAAI,iBAAiB,GAAG,SAAS,CAAC,YAAY,CAAC;AAC3D,YAAY,IAAI,CAAC,iBAAiB,EAAE;AAGpC,gBAAgB,OAAON,cAAK,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACzD,aAAa;AACb,YAAY,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC5D,YAAY,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC5D,YAAY,IAAI,aAAa,KAAK,aAAa;AAC/C,gBAAgB,OAAO,KAAK,CAAC;AAC7B,YAAY,IAAI,aAAa,IAAI,aAAa,EAAE;AAChD,gBAAgB,IAAI,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC;AACnD,gBAAgB,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ,EAAE;AACtD,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB;AACjB,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnD,oBAAoB,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;AAC5G,wBAAwB,OAAO,KAAK,CAAC;AACrC,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,YAAY,OAAO,mBAAmB,CAAC,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAC/F,SAAS;AACT,aAAa;AACb,YAAY,IAAI,QAAQ,GAAGO,kCAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;AACpF,YAAY,IAAI,QAAQ,EAAE;AAG1B,gBAAgB,IAAI,gCAAgC,CAAC,QAAQ,CAAC;AAC9D,oBAAoB,OAAO,IAAI,CAAC;AAChC,gBAAgB,OAAO,mBAAmB,CAAC,QAAQ,CAAC,YAAY;AAKhE,gBAAgB,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,gCAAgC,CAAC,SAAS,EAAE;AACrD,IAAI,QAAQ,CAAC,CAAC,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE;AACzF,CAAC;AACD,SAAS,sBAAsB,CAAC,GAAG,EAAE;AACrC,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC;AAC5C;;AC9EA,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,EAAEZ,gBAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AAChE,IAAC,eAAe,KAAkB,UAAU,MAAM,EAAE;AACvD,IAAIa,eAAS,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AACvC,IAAI,SAAS,eAAe,CAAC,EAAE,EAAE;AACjC,QAAQ,IAAI,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AAC3F,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,QAAQ,EAAE;AAG1D,YAAY,IAAI;AAChB,gBAAgB,IAAI,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC;AACnE,gBAAgB,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AACvD,oBAAoB,WAAW,CAAC,KAAK,GAAG,wCAAwC,CAAC;AACjF,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,GAAG;AAC1B,YAAY,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;AAC9C,YAAY,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAE1C,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAClC,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpC,gBAAgB,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7D,aAAa;AACb,iBAAiB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AAC1C,gBAAgB,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9E,aAAa;AAGb,YAAY,IAAI,KAAK,EAAE;AAKvB,gBAAgB,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AACzD,aAAa;AACb,YAAY,OAAO,YAAY;AAC/B,gBAAgB,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;AAC/E,oBAAoB,KAAK,CAAC,aAAa,EAAE,CAAC;AAC1C,iBAAiB;AACjB,aAAa,CAAC;AACd,SAAS,CAAC,IAAI,IAAI,CAAC;AACnB,QAAQ,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AACpC,QAAQ,KAAK,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;AAExC,QAAQ,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AACpC,QAAQ,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;AAE1C,QAAQ,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACvE,QAAQ,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;AACjC,QAAQ,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClE,QAAQ,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxD,QAAQ,IAAI,EAAE,GAAG,YAAY,CAAC,cAAc,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,kBAAkB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG,EAAE,CAAC;AACpK,QAAQ,IAAI,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAAG,EAAE;AAE3F,QAAQ,EAAE,GAAG,OAAO,CAAC,kBAAkB;AAEvC,QAAQ,kBAAkB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,WAAW,KAAK,SAAS,GAAG,kBAAkB,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;AACjH,QAAQ,KAAK,CAAC,OAAO,GAAGX,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;AAIxD,YAAY,kBAAkB,EAAE,kBAAkB;AAGlD,YAAY,WAAW,EAAE,WAAW,EAAE,CAAC,CAAC;AACxC,QAAQ,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;AAC5E,QAAQ,IAAI,KAAK,GAAGY,gCAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACxD,QAAQ,KAAK,CAAC,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AAClE,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAK9D,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACxD,SAAS;AACT,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,WAAW,EAAE;AAMlE,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AAC1C,SAAS;AACT,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,eAAe,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACnD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAItD,YAAY,IAAI,QAAQ,GAAG;AAC3B,gBAAgB,IAAI,EAAE,UAAU,MAAM,EAAE;AACxC,oBAAoB,OAAO,CAAC,MAAM,CAAC,CAAC;AAWpC,oBAAoB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrD,oBAAoB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;AAC/C,wBAAwB,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACtE,qBAAqB;AACrB,oBAAoB,UAAU,CAAC,YAAY;AAC3C,wBAAwB,YAAY,CAAC,WAAW,EAAE,CAAC;AACnD,qBAAqB,EAAE,CAAC,CAAC,CAAC;AAC1B,iBAAiB;AACjB,gBAAgB,KAAK,EAAE,MAAM;AAC7B,aAAa,CAAC;AACd,YAAY,IAAI,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACzD,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AAEN,IAAI,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY;AACtD,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,gBAAgB,EAAE;AACjF,QAAQ,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,IAAI,CAAC,EAAE;AAErE,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAClD,QAAQ,IAAI,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa;AACxD,aAAa,UAAU,IAAI,UAAU,CAAC,aAAa,CAAC;AACpD,YAAYX,qBAAa,CAAC,KAAK,CAAC;AAChC,QAAQ,IAAI,MAAM,GAAGD,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,wBAAwB,CAAC,aAAa,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE,CAAC,CAAC;AAC5I,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG,EAAE,CAAC;AAC5F,QAAQ;AAGR,QAAQ,gBAAgB,CAAC,WAAW,CAAC;AAKrC,YAAY,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,CAErE;AACT,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAIxC,YAAY,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;AAC5C,SAAS;AACT,aAAa;AACb,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;AAChD,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;AACjE,gBAAgB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC1C,aAAa;AACb,YAAY,IAAIG,WAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;AACxC,gBAAgB,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACrC,aAAa;AACb,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AAG/B,gBAAgB,OAAO,MAAM,CAAC,OAAO,CAAC;AAItC,gBAAgB,IAAI,IAAI,CAAC,QAAQ;AACjC,oBAAoB,MAAM,CAAC,aAAa,KAAKF,qBAAa,CAAC,OAAO;AAClE,qBAAqB,WAAW,KAAK,aAAa,IAAI,WAAW,KAAK,YAAY,CAAC,EAAE;AACrF,oBAAoB,MAAM,CAAC,aAAa,GAAGA,qBAAa,CAAC,KAAK,CAAC;AAC/D,oBAAoB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAC3C,iBAAiB;AACjB,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACtC,aAAa;AACb,YAAY,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK;AAC5C,gBAAgB,CAAC,IAAI,CAAC,QAAQ;AAC9B,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc;AAC5C,gBAAgB,CAAC,MAAM,CAAC,OAAO;AAC/B,gBAAgB,CAAC,MAAM,CAAC,IAAI;AAC5B,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE;AAC/B,gBAAgB,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpD,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,gBAAgB,EAAE;AAC9B,YAAY,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,gBAAgB,EAAE;AAC7E,QAAQ,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,IAAI,CAAC,EAAE;AACrE,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC5E,KAAK,CAAC;AAGN,IAAI,eAAe,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,SAAS,EAAE,SAAS,EAAE;AAC1F,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACxB,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzE,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AACxD,QAAQ,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7E,QAAQ,IAAI,iBAAiB,GAAG,WAAW,IAAI,YAAY,CAAC,uBAAuB;AACnF,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;AAC7E,cAAc,CAACE,WAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAClD,QAAQ,QAAQ,iBAAiB,KAAK,SAAS,IAAI,CAACA,WAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;AAC5F,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,GAAG,EAAE,kBAAkB,EAAE;AAC3E,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC7B,QAAQ,IAAI,IAAI;AAChB,YAAY,IAAI,CAAC,GAAG,CAAC;AACrB,aAAa,CAAC,kBAAkB,IAAIA,WAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE;AAC5E,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAS;AACT,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,kBAAkB,EAAE;AAC5E,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;AAC1D,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,kBAAkB,EAAE;AAC3E,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;AACzD,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY;AAC7D,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,qBAAqB,GAAG,YAAY;AAClE,QAAQ,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpD,KAAK,CAAC;AAQN,IAAI,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AAC7D,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,gBAAgB,GAAG;AAE/B,YAAY,YAAY,EAAE,CAAC;AAC3B,SAAS,CAAC;AAIV,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AACnD,QAAQ,IAAI,WAAW,KAAK,mBAAmB,EAAE;AACjD,YAAY,gBAAgB,CAAC,WAAW,GAAG,WAAW,CAAC;AACvD,SAAS;AACT,aAAa,IAAI,WAAW,KAAK,UAAU,EAAE;AAC7C,YAAY,gBAAgB,CAAC,WAAW,GAAG,UAAU,CAAC;AACtD,SAAS;AACT,aAAa;AACb,YAAY,gBAAgB,CAAC,WAAW,GAAG,cAAc,CAAC;AAC1D,SAAS;AACT,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,SAAS,IAAIL,gBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE;AACtG,YAAY,IAAI,QAAQ,GAAGe,4BAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1D,YAAY,IAAI,IAAI,GAAG,QAAQ,CAAC,mBAAmB,CAAC;AACpD,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE;AACrG,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIC,iBAAS,CAAC,IAAI;AAC9D,oBAAoB,EAAE;AACtB,oBAAoB,SAAS;AAC7B,oBAAoB,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,QAAQ;AACpG,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,SAAS,IAAI,CAACX,WAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAEpE,YAAY,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAGH,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,CAAC;AAC5H,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;AACxC,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAEC,qBAAa,CAAC,OAAO,CAAC,CAAC;AACvE,KAAK,CAAC;AAIN,IAAI,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,gBAAgB,EAAE;AACtE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,eAAe,GAAGD,cAAQ,CAACA,cAAQ,CAAC,EAAE,GAAG,gBAAgB,CAAC,KAAK,GAAG,gBAAgB,IAAIA,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,gBAAgB,CAAC,EAAE,EAAE,SAAS,EAAEA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;AAM3S,YAAY,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC;AACvC,QAAQ,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC9E,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;AAMtD,QAAQ,IAAI,CAAC,SAAS;AACtB,YAAY,gBAAgB,CAAC,KAAK;AAClC,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1D,kBAAkB,eAAe,CAAC,KAAK,CAAC;AAGxC,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,QAAQ,IAAI,qBAAqB,GAAG,SAAS,CAAC,aAAa,CAAC;AAC5D,QAAQ,SAAS,CAAC,aAAa,GAAGC,qBAAa,CAAC,SAAS,CAAC;AAC1D,QAAQ,IAAI,eAAe,CAAC,2BAA2B,EAAE;AACzD,YAAY,IAAI,CAAC,OAAO,EAAE,CAAC;AAC3B,SAAS;AACT,QAAQ,IAAI,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AACxC,QAAQ,IAAI,WAAW,GAAG,gBAAgB,KAAK,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAC3H,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,CAAC;AAC/D,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,YAAYa,iBAAS,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACvC,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,YAAY;AAChC,aAAa,UAAU,CAAC,GAAG,EAAE,eAAe,EAAEb,qBAAa,CAAC,SAAS,CAAC;AACtE,aAAa,IAAI,CAAC,UAAU,eAAe,EAAE;AAC7C,YAAY,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,IAAI,SAAS,CAAC,aAAa,KAAKA,qBAAa,CAAC,SAAS,EAAE;AACrE,gBAAgB,SAAS,CAAC,aAAa,GAAG,qBAAqB,CAAC;AAChE,aAAa;AACb,YAAY,IAAI,QAAQ,EAAE;AAM1B,gBAAgB,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/C,oBAAoB,MAAM,EAAE,UAAU,KAAK,EAAE;AAC7C,wBAAwB,IAAI,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC;AACvE,wBAAwB,IAAI,WAAW,EAAE;AACzC,4BAA4B,KAAK,CAAC,WAAW,CAAC;AAC9C,gCAAgC,KAAK,EAAE,KAAK,CAAC,KAAK;AAClD,gCAAgC,SAAS,EAAE,KAAK,CAAC,SAAS;AAC1D,gCAAgC,iBAAiB,EAAE,IAAI;AACvD,gCAAgC,UAAU,EAAE,KAAK;AACjD,6BAA6B,EAAE,UAAU,QAAQ,EAAE;AACnD,gCAAgC,OAAO,WAAW,CAAC,QAAQ,EAAE;AAC7D,oCAAoC,eAAe,EAAE,eAAe,CAAC,IAAI;AACzE,oCAAoC,SAAS,EAAE,eAAe,CAAC,SAAS;AACxE,iCAAiC,CAAC,CAAC;AACnC,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,6BAA6B;AAM7B,4BAA4B,KAAK,CAAC,UAAU,CAAC;AAC7C,gCAAgC,KAAK,EAAE,eAAe,CAAC,KAAK;AAC5D,gCAAgC,SAAS,EAAE,eAAe,CAAC,SAAS;AACpE,gCAAgC,IAAI,EAAE,eAAe,CAAC,IAAI;AAC1D,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,cAAc,EAAE,UAAU,KAAK,EAAE;AAGrD,wBAAwB,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACzD,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,iBAAiB;AAejB,gBAAgB,IAAI,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACzD,gBAAgB,IAAI,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE;AACxD,oBAAoB,eAAe,EAAE,eAAe,CAAC,IAAI;AACzD,oBAAoB,SAAS,EAAE,eAAe,CAAC,SAAS;AACxD,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,KAAK,CAAC,YAAY,CAACD,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;AACxG,aAAa;AACb,YAAY,OAAO,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;AACrD,SAAS,CAAC;AACV,aAAa,OAAO,CAAC,YAAY;AAMjC,YAAY,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC/D,gBAAgB,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AASN,IAAI,eAAe,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,OAAO,EAAE;AACnE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY;AAC5C,aAAa,wBAAwB,CAAC;AACtC,YAAY,KAAK,EAAE,OAAO,CAAC,QAAQ;AACnC,YAAY,SAAS,EAAE,OAAO,CAAC,SAAS;AACxC,YAAY,OAAO,EAAE,OAAO,CAAC,OAAO;AACpC,SAAS,CAAC;AACV,aAAa,SAAS,CAAC;AACvB,YAAY,IAAI,EAAE,UAAU,gBAAgB,EAAE;AAC9C,gBAAgB,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACtD,gBAAgB,IAAI,WAAW,EAAE;AACjC,oBAAoB,KAAK,CAAC,WAAW,CAAC,UAAU,QAAQ,EAAE,EAAE,EAAE;AAC9D,wBAAwB,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACrD,wBAAwB,OAAO,WAAW,CAAC,QAAQ,EAAE;AACrD,4BAA4B,gBAAgB,EAAE,gBAAgB;AAC9D,4BAA4B,SAAS,EAAE,SAAS;AAChD,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,YAAY,KAAK,EAAE,UAAU,GAAG,EAAE;AAClC,gBAAgB,IAAI,OAAO,CAAC,OAAO,EAAE;AACrC,oBAAoB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACzC,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIc,iBAAS,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AACzE,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7C,QAAQ,OAAO,YAAY;AAC3B,YAAY,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;AAC1D,gBAAgB,YAAY,CAAC,WAAW,EAAE,CAAC;AAC3C,aAAa;AACb,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,UAAU,EAAE;AACjE,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,UAAU,EAAE;AACvE,QAAQ,IAAI,aAAa,GAAGC,iBAAO,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;AACpE,QAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAC5C,KAAK,CAAC;AAmBN,IAAI,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;AAClE,QAAQ,IAAIZ,WAAK,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAI9C,YAAY,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3E,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;AAE3C,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AAClC,YAAY,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC;AAE9B,YAAY,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;AACxD,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,EAAEF,qBAAa,CAAC,YAAY,CAAC,CAAC;AACvC,KAAK,CAAC;AAMN,IAAI,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,KAAK,EAAE;AAC7D,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC7C,QAAQ,IAAI,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC;AAC7C,YAAY,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AACrC,YAAY,SAAS,EAAE,IAAI,CAAC,SAAS;AACrC,YAAY,iBAAiB,EAAE,IAAI;AACnC,YAAY,UAAU,EAAE,KAAK;AAC7B,SAAS,CAAC,CAAC,MAAM,CAAC;AAClB,QAAQ,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE;AACtC,YAAY,SAAS,EAAE,IAAI,CAAC,SAAS;AACrC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC;AAC1C,gBAAgB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AACzC,gBAAgB,IAAI,EAAE,SAAS;AAC/B,gBAAgB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzC,aAAa,CAAC,CAAC;AACf,YAAY,YAAY,CAAC,gBAAgB,EAAE,CAAC;AAC5C,SAAS;AACT,KAAK,CAAC;AAIN,IAAI,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,YAAY,EAAE;AACrE,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;AACjD,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,KAAK,CAAC;AAIN,IAAI,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;AACxD,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;AACtC,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,KAAK,CAAC;AAEN,IAAI,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,MAAM;AAKrE,IAAI,OAAO,EAAE;AACb,QAAQ,IAAI,OAAO,CAAC,eAAe,EAAE;AACrC,YAAY,IAAI,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;AACnL,YAAY,IAAI,WAAW,KAAK,SAAS,EAAE,CAE9B;AACb,iBAAiB,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,UAAU,EAAE;AAWpE,gBAAgB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE;AAC3E,oBAAoB,MAAM,EAAE,MAAM;AAClC,oBAAoB,OAAO,EAAE,OAAO;AACpC,oBAAoB,UAAU,EAAE,IAAI;AACpC,oBAAoB,kBAAkB,EAAE,kBAAkB;AAC1D,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,iBAAiB,IAAI,MAAM,KAAK,mBAAmB,EAAE;AACrD,gBAAgB,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC;AACzD,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;AAC9D,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC,WAAW,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE;AAGlF,QAAQ,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACnD,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACzG,KAAK,CAAC;AAEN,IAAI,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,YAAY;AAC1D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AAEzB,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACvC,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,EAAE,GAAG,IAAI,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,YAAY,GAAG,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;AAC5F,QAAQ,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;AACnD,YAAY,IAAI,WAAW,EAAE;AAC7B,gBAAgB,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClD,gBAAgB,OAAO,IAAI,CAAC,WAAW,CAAC;AACxC,aAAa;AACb,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,KAAK,YAAY,EAAE;AAClE,YAAY,OAAO;AACnB,SAAS;AACT,QAAQa,iBAAS,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACpC,QAAQ,IAAI,IAAI,GAAG,WAAW,KAAK,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;AAC1D,QAAQ,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;AACrC,QAAQ,IAAI,UAAU,GAAG,YAAY;AACrC,YAAY,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,YAAY,IAAI,KAAK,CAAC,WAAW,EAAE;AACnC,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC;AAC5E,oBAAoB,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,eAAe,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;AACrH,oBAAoB,KAAK,CAAC,SAAS,CAAC;AAKpC,wBAAwB,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,kBAAkB,KAAK,UAAU;AACpF,4BAA4B,UAAU;AACtC,8BAA8B,cAAc;AAC5C,qBAAqB,EAAEb,qBAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,IAAI,EAAE,CAAC;AAC3B,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,IAAI,GAAG,YAAY;AAC/B,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC;AACzC,YAAY,IAAI,IAAI,EAAE;AACtB,gBAAgB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3C,gBAAgB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrE,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,EAAE,CAAC;AACf,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,SAAS,EAAE,SAAS,EAAE;AACjF,QAAQ,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE;AACjE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAExC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAACE,WAAK,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC1E,YAAY,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3B,SAAS;AACT,QAAQ,QAAQ,IAAI,CAAC,IAAI,GAAGH,cAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,sBAAsB;AACvF,gBAAgB,SAAS;AACzB,kBAAkBgB,mBAAS,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE;AACpG,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,UAAU,EAAE,gBAAgB,EAAE;AAC3F,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAChC,QAAQ,IAAI,oBAAoB;AAIhC,QAAQ,gBAAgB,KAAKf,qBAAa,CAAC,OAAO;AAGlD,YAAY,gBAAgB,KAAKA,qBAAa,CAAC,SAAS;AAGxD,YAAY,gBAAgB,KAAKA,qBAAa,CAAC,IAAI,CAAC;AAEpD,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AAClD,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AACtD,QAAQ,IAAI,aAAa,GAAGc,iBAAO,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;AACpE,QAAQ,IAAI,OAAO,GAAG,oBAAoB;AAG1C,YAAY,aAAa;AACzB,cAAc,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAKlD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1D,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ,IAAI,CAAC,oBAAoB,EAAE;AAEnC,YAAY,IAAI,CAAC,aAAa,EAAE,CAAC;AAGjC,YAAY,IAAI,UAAU;AAC1B,gBAAgB,UAAU,CAAC,SAAS;AACpC,gBAAgB,CAACZ,WAAK,CAAC,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC;AAE1D,gBAAgB,OAAO,CAAC,WAAW,KAAK,SAAS;AAGjD,iBAAiB,OAAO,CAAC,WAAW,KAAK,cAAc;AAGvD,oBAAoB,OAAO,OAAO,CAAC,eAAe,KAAK,UAAU,CAAC,EAAE;AACpE,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;AACxE,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE;AACjD,oBAAoB,gBAAgB,GAAGF,qBAAa,CAAC,YAAY,CAAC;AAClE,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AACjG,QAAQ,IAAI,yBAAyB,GAAG,YAAY;AACpD,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,EAAE;AAC3C,gBAAgB,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAC/C,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,SAAS,GAAG,OAAO,CAAC,SAAS,IAAID,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7E,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,KAAK,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;AAC5G,QAAQ,IAAI,QAAQ,GAAG;AACvB,YAAY,IAAI,EAAE,UAAU,MAAM,EAAE;AACpC,gBAAgB,IAAIG,WAAK,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AACvD,oBAAoB,yBAAyB,EAAE,CAAC;AAChD,oBAAoB,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC1D,iBAAiB;AACjB,aAAa;AACb,YAAY,KAAK,EAAE,UAAU,KAAK,EAAE;AACpC,gBAAgB,IAAIA,WAAK,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAIvD,oBAAoB,IAAI,CAACc,oBAAa,CAAC,KAAK,CAAC,EAAE;AAC/C,wBAAwB,KAAK,GAAG,IAAIC,kBAAW,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;AACzE,qBAAqB;AACrB,oBAAoB,yBAAyB,EAAE,CAAC;AAChD,oBAAoB,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACxD,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,oBAAoB,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAGlE,YAAY,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/C,gBAAgB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3D,aAAa;AACb,YAAY,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACtC,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,UAAU,EAAE,gBAAgB,EAAE;AAClF,QAAQ,OAAOC,mCAAyB,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9H,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,qBAAqB,GAAG,YAAY;AAClE,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,SAAS;AAOT,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChC,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,YAAY,CAAC;AAC5B,KAAK,CAAC;AAGN,IAAI,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AACpD,QAAQ,IAAI,CAAC,YAAY;AAKzB,QAAQ,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1D,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,MAAM,EAAE,SAAS,EAAE;AAC1E,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5C,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAK5E,QAAQ,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;AAC5E,YAAY,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,SAAS,IAAI,WAAW,EAAE;AACtC,YAAYC,gCAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACpF,SAAS;AACT,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,KAAK,EAAE,SAAS,EAAE;AAGxE,QAAQ,IAAI,WAAW,GAAGpB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,aAAa,EAAEC,qBAAa,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1K,QAAQ,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACtD,QAAQmB,gCAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC;AACnF,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AACzD,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC;AACvC,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,YAAY;AAC1D,QAAQ,IAAI,IAAI,CAAC,UAAU;AAC3B,YAAY,OAAO;AACnB,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC3C,YAAY,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvD,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC;AAChC,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC;AACjC,SAAS;AACT,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;AAE3B,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AACjF,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACnC,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;AAC/B,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,QAAQ,EAAE;AACtE,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACrD,KAAK,CAAC;AACN,IAAI,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,MAAM,EAAE;AAC7D,QAAQ,OAAO,MAAM,IAAI,MAAM,IAAI,MAAM,GAAGpB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AACnH,gBAAgB,QAAQ,EAAE,IAAI,CAAC,KAAK;AACpC,gBAAgB,IAAI,EAAE,MAAM,CAAC,IAAI;AACjC,gBAAgB,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;AACrD,gBAAgB,EAAE,EAAE,IAAI,CAAC,OAAO;AAChC,aAAa,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,OAAO,eAAe,CAAC;AAC3B,CAAC,CAACqB,oBAAU,CAAC,EAAE;AAIfC,+BAAqB,CAAC,eAAe,CAAC,CAAC;AAQhC,SAAS,mBAAmB,CAAC,QAAQ,EAAE;AAC9C,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,eAAe,GAAG,EAAE,CAAC,eAAe,CAAC;AAClG,IAAI,IAAI,WAAW,KAAK,mBAAmB,IAAI,WAAW,KAAK,cAAc,EAAE;AAC/E,QAAQ,OAAO,QAAQ,CAAC,SAAS,CAAC;AAClC,YAAY,WAAW,EAAE,aAAa;AAGtC,YAAY,eAAe,EAAE,UAAU,kBAAkB,EAAE,OAAO,EAAE;AAGpE,gBAAgB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AAGvD,gBAAgB,IAAI,OAAO,IAAI,CAAC,eAAe,KAAK,UAAU,EAAE;AAChE,oBAAoB,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AAC7E,iBAAiB;AAEjB,gBAAgB,OAAO,WAAW,CAAC;AACnC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,SAAS,EAAE,CAAC;AAChC,CAAC;AACD,SAAS,wCAAwC,CAAC,KAAK,EAAE;AACzD,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIR,iBAAS,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACpF,CAAC;AACM,SAAS,qBAAqB,CAAC,OAAO,EAAE;AAC/C,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,OAAO,EAAE;AACjD,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIA,iBAAS,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AACrE,KAAK;AACL,CAAC;AACD,SAAS,gBAAgB,CAAC,WAAW,GAA+C;AACpF,IAAI,QAAQ,WAAW,KAAK,cAAc;AAC1C,QAAQ,WAAW,KAAK,UAAU;AAClC,QAAQ,WAAW,KAAK,SAAS,EAAE;AACnC;;ACj1BA,IAAI,uBAAuB,GAAG,KAAKS,uBAAa,GAAG,OAAO,GAAG,GAAG,GAAG,CAAC;AACpE,SAAS,0BAA0B,CAAC,KAAK,EAAE,UAAU,EAAE;AACvD,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACrC,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AAExC,QAAQ,KAAK,CAAC,UAAU,CAAC,GAAG,YAAY;AACxC,YAAY,uBAAuB,CAAC,GAAG,CAAC,KAAK;AAK7C,YAAY,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;AAE7D,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACnD,SAAS,CAAC;AACV,KAAK;AACL,CAAC;AACD,SAAS,mBAAmB,CAAC,IAAI,EAAE;AACnC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE;AAC/B,QAAQ,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;AAC5C,QAAQ,IAAI,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,CAAC;AACvC,KAAK;AACL,CAAC;AAaD,IAAI,SAAS,KAAkB,YAAY;AAC3C,IAAI,SAAS,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE;AAC9C,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE;AAC7E,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC/B,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC7B,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AACpC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAMtD,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACjD,YAAY,uBAAuB,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAClD,YAAY,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvD,YAAY,0BAA0B,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACxD,YAAY,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvD,SAAS;AACT,KAAK;AACL,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,KAAK,EAAE;AAChD,QAAQ,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,IAAItB,qBAAa,CAAC,OAAO,CAAC;AACzE,QAAQ,IAAI,IAAI,CAAC,SAAS;AAC1B,YAAY,IAAI,CAAC,aAAa,KAAKA,qBAAa,CAAC,OAAO;AACxD,YAAY,CAACE,WAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE;AACrD,YAAY,aAAa,GAAGF,qBAAa,CAAC,YAAY,CAAC;AACvD,SAAS;AACT,QAAQ,IAAI,CAACE,WAAK,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AACrD,YAAY,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;AACnC,SAAS;AACT,QAAQ,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;AAC5B,YAAY,QAAQ,EAAE,KAAK,CAAC,QAAQ;AACpC,YAAY,SAAS,EAAE,KAAK,CAAC,SAAS;AACtC,YAAY,YAAY,EAAE,IAAI;AAC9B,YAAY,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;AACnD,YAAY,aAAa,EAAE,aAAa;AACxC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,KAAK,CAAC,eAAe,EAAE;AACnC,YAAY,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE;AACjC,YAAY,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,KAAK,GAAG,YAAY;AAC5C,QAAQ,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY;AAChD,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AAC9C,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAC5C,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAIA,WAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACpE,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACtC,SAAS;AACT,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzC,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AACtC,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;AACzD,YAAY,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AACvC,SAAS;AACT,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3C,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,IAAI,EAAE,OAAO,EAAE;AAClE,QAAQ,IAAI,CAAC,QAAQ;AACrB,YAAY,IAAI;AAChB,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7D,iBAAiB;AACjB,kBAAkB,KAAK,CAAC,CAAC;AACzB,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,SAAS,EAAE;AAC9D,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE;AACjE,QAAQ,OAAO;AACf,YAAY,KAAK,EAAE,IAAI,CAAC,QAAQ;AAChC,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,iBAAiB,EAAE,IAAI;AACnC,YAAY,UAAU,EAAE,IAAI;AAC5B,YAAY,eAAe,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe;AACxH,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE;AAClD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAU1D,QAAQ,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE;AAC5H,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAACA,WAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;AACnF,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrC,gBAAgB,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3F,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,EAAE,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,EAAE,KAAK,IAAI,CAAC,eAAe;AACvC,YAAY,OAAO;AACnB,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;AAC7B,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAClC,QAAQ,IAAI,EAAE,EAAE;AAChB,YAAY,EAAE,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AACnC,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,GAAG,YAAY;AAC9D,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AAC3C,gBAAgB,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAMpD,oBAAoB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACpC,iBAAiB;AACjB,qBAAqB;AASrB,oBAAoB,mBAAmB,CAAC,EAAE,CAAC,CAAC;AAC5C,iBAAiB;AACjB,aAAa,EAAE,CAAC;AAChB,SAAS;AACT,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC;AACnC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AAC7C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AACjC,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACpF,SAAS;AACT,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AACnD,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AACjD,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;AAClF,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;AACvE,YAAY,IAAI,WAAW,KAAK,YAAY,IAAI,WAAW,KAAK,mBAAmB,EAAE;AACrF,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AAC3C,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC3B,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAEhC,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC;AAG1B,YAAY,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC;AACrD,YAAY,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1C,YAAY,IAAI,EAAE;AAClB,gBAAgB,EAAE,CAAC,WAAW,EAAE,CAAC;AACjC,SAAS;AACT,KAAK,CAAC;AAGN,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY,GAAG,CAAC;AACjD,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,SAAS,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE;AACjE,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AACtC,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;AACzD,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,YAAY,GAAGH,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAChK,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAACG,WAAK,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AACrE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,YAAY,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,GAAG,YAAY,EAAE,CAAC;AAC5E,SAAS;AACT,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,YAAY;AACrD,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,MAAM,EAAE,SAAS,EAAE;AACnE,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,QAAQ,OAAO,EAAE,SAAS;AAI1B,YAAY,SAAS,CAAC,OAAO,KAAK,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;AACzE,YAAYA,WAAK,CAAC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC;AACjD,YAAYA,WAAK,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACvD,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,kBAAkB,EAAE;AAC9F,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,MAAM,GAAG,IAAIqB,oBAAU,EAAE,CAAC;AACtC,QAAQ,IAAI,aAAa,GAAGC,yBAAe,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAGzF,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,QAAQ,IAAI,aAAa,IAAI,MAAM,IAAIA,yBAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAC5E,YAAY,IAAI,UAAU,GAAGC,8BAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjF,YAAY,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC;AAMrC,SAAS;AACT,aAAa,IAAI,SAAS,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AACxD,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACtC,YAAY,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;AACjE,SAAS;AACT,QAAQ,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAC3C,QAAQ,IAAI,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;AAChD,YAAY,IAAI,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AACjH,SAAS;AACT,aAAa,IAAI,kBAAkB,KAAK,CAAC,GAAkC;AAC3E,YAAY,IAAI,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE;AAKhE,gBAAgB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,UAAU,KAAK,EAAE;AAC/D,oBAAoB,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE;AACtE,wBAAwB,KAAK,CAAC,UAAU,CAAC;AACzC,4BAA4B,KAAK,EAAE,QAAQ;AAC3C,4BAA4B,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7C,4BAA4B,SAAS,EAAE,OAAO,CAAC,SAAS;AACxD,4BAA4B,SAAS,EAAE,kBAAkB,KAAK,CAAC;AAC/D,yBAAyB,CAAC,CAAC;AAC3B,wBAAwB,KAAK,CAAC,SAAS,GAAG;AAC1C,4BAA4B,MAAM,EAAE,MAAM;AAC1C,4BAA4B,SAAS,EAAE,OAAO,CAAC,SAAS;AACxD,4BAA4B,OAAO,EAAE,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7E,yBAAyB,CAAC;AAC1B,qBAAqB;AACrB,yBAAyB;AAiCzB,wBAAwB,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;AAG5E,4BAA4B,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AACrE,4BAA4B,OAAO;AACnC,yBAAyB;AAGzB,qBAAqB;AACrB,oBAAoB,IAAI,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC9E,oBAAoB,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAOvD,oBAAoB,IAAI,CAAC,KAAK,CAAC,OAAO,IAAIvB,WAAK,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE;AAGrF,wBAAwB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7D,qBAAqB;AAMrB,oBAAoB,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC5D,oBAAoB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvC,wBAAwB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAClD,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;AACxC,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY;AAChD,QAAQ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACjC,QAAQ,QAAQ,IAAI,CAAC,aAAa,GAAGF,qBAAa,CAAC,KAAK,EAAE;AAC1D,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,KAAK,EAAE;AACrD,QAAQ,IAAI,CAAC,aAAa,GAAGA,qBAAa,CAAC,KAAK,CAAC;AACjD,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;AAChC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE;AACjC,YAAY,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,YAAY,EAAE;AAChC,YAAY,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;AACnD,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,OAAO,SAAS,CAAC;AACrB,CAAC,EAAE,CAAC,CAAC;AAEE,SAAS,iBAAiB,CAAC,MAAM,EAAE,WAAW,EAAE;AACvD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,MAAM,CAAC,EAAE;AACzD,IAAI,IAAI,YAAY,GAAG,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,KAAK,CAAC;AACzE,IAAI,IAAI,eAAe,GAAG,CAAC0B,+BAAqB,CAAC,MAAM,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,eAAe,IAAI,YAAY,IAAI,MAAM,CAAC,IAAI,EAAE;AACzD,QAAQ,eAAe,GAAG,IAAI,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,eAAe,CAAC;AAC3B;;AC/XA,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACrD,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAIjC,IAAI,YAAY,KAAkB,YAAY;AAC9C,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;AACnC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAGlC,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAKjC,QAAQ,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;AACxC,QAAQ,IAAI,CAAC,cAAc,GAAG,IAAIC,8BAAoB,CAACC,oBAAU,CAAC,8BAA8B,CAAC;AACjG,YAAY,IAAI,EAAyD,CAAC;AAC1E,QAAQ,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAChC,QAAQ,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAGnC,QAAQ,IAAI,CAAC,uBAAuB,GAAG,IAAIC,SAAI,CAAC,KAAK,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;AAClD,QAAQ,IAAI,wBAAwB,GAAG,IAAIC,2BAAiB,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,EAAE;AAEpI,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AACnC,QAAQ,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACjC,QAAQ,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AACrD,QAAQ,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC7D,QAAQ,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACvD,QAAQ,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAC7C,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACvC,QAAQ,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;AACrE,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAC/C,QAAQ,IAAI,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;AAC1D,QAAQ,IAAI,CAAC,iBAAiB;AAC9B,YAAY,iBAAiB;AAC7B,gBAAgB,wBAAwB;AACxC,qBAAqB,MAAM,CAAC,iBAAiB,CAAC;AAK9C,qBAAqB,MAAM,CAAC,wBAAwB,CAAC;AACrD,kBAAkB,wBAAwB,CAAC;AAC3C,QAAQ,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5E,QAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,GAAG;AACtD,YAAY,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,SAAS;AACT,KAAK;AAKL,IAAI,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AAC9C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,OAAO,EAAE;AACvD,YAAY,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAChD,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,oBAAoB,CAACC,yBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;AACzD,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,KAAK,EAAE;AACnE,QAAQ,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACjF,QAAQ,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,EAAE,EAAE;AAClD,QAAQ,OAAOC,eAAS,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,UAAU,EAAE,EAAE;AAChE,YAAY,IAAI,UAAU,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,YAAY,EAAE,IAAI,CAAC;AACrF,YAAY,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,YAAY,IAAI,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,kBAAkB,GAAG,EAAE,CAAC,kBAAkB,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,CAAC,cAAc,EAAE,cAAc,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,iBAAiB,GAAG,EAAE,CAAC,MAAM,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,KAAK,cAAc,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,KAAK,MAAM,GAAG,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AAC3tB,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,QAAQ,EAAE,CAAC,KAAK;AAChC,oBAAoB,KAAK,CAAC;AAC1B,wBAAwBpB,iBAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAChD,wBAAwBA,iBAAS,CAAC,WAAW,KAAK,cAAc,IAAI,WAAW,KAAK,UAAU,EAAE,EAAE,CAAC,CAAC;AACpG,wBAAwB,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC/D,wBAAwB,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzF,wBAAwB,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,gBAAgB,CAAC;AAC3F,wBAAwB,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC3E,wBAAwB,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,GAAY,CAAC,CAAC,CAAC;AACvE,wBAAwB,OAAO,CAAC,CAAC,GAAY,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;AACjH,oBAAoB,KAAK,CAAC;AAC1B,wBAAwB,SAAS,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAChD,wBAAwB,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AACrC,oBAAoB,KAAK,CAAC;AAC1B,wBAAwB,kBAAkB,GAAG,IAAI,CAAC,aAAa;AAC/D,6BAA6B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG;AAC9D,gCAAgC,QAAQ,EAAE,QAAQ;AAClD,gCAAgC,SAAS,EAAE,SAAS;AACpD,gCAAgC,OAAO,EAAE,IAAI;AAC7C,gCAAgC,KAAK,EAAE,IAAI;AAC3C,6BAA6B,CAAC,CAAC;AAC/B,wBAAwB,YAAY,GAAG,kBAAkB;AACzD,4BAA4B,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE;AAC5E,gCAAgC,UAAU,EAAE,UAAU;AACtD,gCAAgC,QAAQ,EAAE,QAAQ;AAClD,gCAAgC,SAAS,EAAE,SAAS;AACpD,gCAAgC,WAAW,EAAE,WAAW;AACxD,gCAAgC,WAAW,EAAE,WAAW;AACxD,gCAAgC,OAAO,EAAE,OAAO;AAChD,gCAAgC,aAAa,EAAE,aAAa;AAC5D,gCAAgC,MAAM,EAAE,iBAAiB;AACzD,gCAAgC,cAAc,EAAE,cAAc;AAC9D,6BAA6B,CAAC,CAAC;AAC/B,wBAAwB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChD,wBAAwB,IAAI,GAAG,IAAI,CAAC;AACpC,wBAAwB,OAAO,CAAC,CAAC,GAAa,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AACrF,gCAAgC,OAAOqB,kBAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAEnC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,kBAAkB,EAAE,YAAY,GAAG,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,UAAU,MAAM,EAAE;AACnO,oCAAoC,IAAI2B,+BAAqB,CAAC,MAAM,CAAC,IAAI,WAAW,KAAK,MAAM,EAAE;AACjG,wCAAwC,MAAM,IAAIT,kBAAW,CAAC;AAC9D,4CAA4C,aAAa,EAAEkB,oCAA0B,CAAC,MAAM,CAAC;AAC7F,yCAAyC,CAAC,CAAC;AAC3C,qCAAqC;AACrC,oCAAoC,IAAI,kBAAkB,EAAE;AAC5D,wCAAwC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;AAC3E,wCAAwC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC;AACxE,qCAAqC;AACrC,oCAAoC,IAAI,WAAW,GAAGpC,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAC3E,oCAAoC,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;AAC9E,wCAAwC,cAAc,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;AACrF,qCAAqC;AACrC,oCAAoC,IAAI,WAAW,KAAK,QAAQ,IAAI2B,+BAAqB,CAAC,WAAW,CAAC,EAAE;AACxG,wCAAwC,OAAO,WAAW,CAAC,MAAM,CAAC;AAClE,qCAAqC;AACrC,oCAAoC,OAAO,IAAI,CAAC,kBAAkB,CAAC;AACnE,wCAAwC,UAAU,EAAE,UAAU;AAC9D,wCAAwC,MAAM,EAAE,WAAW;AAC3D,wCAAwC,QAAQ,EAAE,QAAQ;AAC1D,wCAAwC,SAAS,EAAE,SAAS;AAC5D,wCAAwC,WAAW,EAAE,WAAW;AAChE,wCAAwC,WAAW,EAAE,WAAW;AAChE,wCAAwC,OAAO,EAAE,OAAO;AACxD,wCAAwC,MAAM,EAAE,iBAAiB;AACjE,wCAAwC,aAAa,EAAE,aAAa;AACpE,wCAAwC,mBAAmB,EAAE,mBAAmB;AAChF,wCAAwC,cAAc,EAAE,cAAc;AACtE,wCAAwC,gBAAgB,EAAE,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;AAC5F,wCAAwC,cAAc,EAAE,cAAc;AACtE,wCAAwC,cAAc,EAAE,cAAc;AACtE,qCAAqC,CAAC,CAAC;AACvC,iCAAiC,CAAC,CAAC,SAAS,CAAC;AAC7C,oCAAoC,IAAI,EAAE,UAAU,WAAW,EAAE;AACjE,wCAAwC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAMhE,wCAAwC,IAAI,EAAE,SAAS,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1G,4CAA4C,OAAO,CAAC3B,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC;AACnH,oDAAoD,QAAQ,EAAE,QAAQ;AACtE,oDAAoD,IAAI,EAAE,WAAW,CAAC,IAAI;AAC1E,oDAAoD,WAAW,EAAE,WAAW;AAC5E,oDAAoD,EAAE,EAAE,UAAU;AAClE,iDAAiD,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,yCAAyC;AACzC,qCAAqC;AACrC,oCAAoC,KAAK,EAAE,UAAU,GAAG,EAAE;AAC1D,wCAAwC,IAAI,kBAAkB,EAAE;AAChE,4CAA4C,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/E,4CAA4C,kBAAkB,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3E,yCAAyC;AACzC,wCAAwC,IAAI,YAAY,EAAE;AAC1D,4CAA4C,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AACpF,yCAAyC;AACzC,wCAAwC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChE,wCAAwC,MAAM,CAAC,GAAG,YAAYkB,kBAAW,GAAG,GAAG,IAAI,IAAIA,kBAAW,CAAC;AACnG,4CAA4C,YAAY,EAAE,GAAG;AAC7D,yCAAyC,CAAC,CAAC,CAAC,CAAC;AAC7C,qCAAqC;AACrC,iCAAiC,CAAC,CAAC;AACnC,6BAA6B,CAAC,CAAC,CAAC;AAChC,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,QAAQ,EAAE,KAAK,EAAE;AAC3E,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;AACrD,QAAQ,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACrC,QAAQ,IAAI,WAAW,GAAG,EAAE,CAAC;AAC7B,QAAQ,IAAI,SAAS,GAAG,QAAQ,CAAC,WAAW,KAAK,UAAU,CAAC;AAC5D,QAAQ,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC3E,YAAY,IAAI,CAACmB,2CAAiC,CAAC,MAAM,CAAC,EAAE;AAC5D,gBAAgB,WAAW,CAAC,IAAI,CAAC;AACjC,oBAAoB,MAAM,EAAE,MAAM,CAAC,IAAI;AACvC,oBAAoB,MAAM,EAAE,eAAe;AAC3C,oBAAoB,KAAK,EAAE,QAAQ,CAAC,QAAQ;AAC5C,oBAAoB,SAAS,EAAE,QAAQ,CAAC,SAAS;AACjD,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,IAAIA,2CAAiC,CAAC,MAAM,CAAC;AACzD,gBAAgBZ,yBAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AACrD,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AACtC,oBAAoB,EAAE,EAAE,eAAe;AAIvC,oBAAoB,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO;AAC1E,oBAAoB,SAAS,EAAE,QAAQ,CAAC,SAAS;AACjD,oBAAoB,UAAU,EAAE,KAAK;AACrC,oBAAoB,iBAAiB,EAAE,IAAI;AAC3C,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC;AACxC,gBAAgB,IAAI,IAAI,CAAC,MAAM,EAAE;AACjC,oBAAoB,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3E,iBAAiB;AACjB,gBAAgB,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AAGvD,oBAAoB,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC;AAC7C,oBAAoB,WAAW,CAAC,IAAI,CAAC;AACrC,wBAAwB,MAAM,EAAE,UAAU;AAC1C,wBAAwB,MAAM,EAAE,eAAe;AAC/C,wBAAwB,KAAK,EAAE,QAAQ,CAAC,QAAQ;AAChD,wBAAwB,SAAS,EAAE,QAAQ,CAAC,SAAS;AACrD,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;AACzD,YAAY,IAAI,eAAe,EAAE;AACjC,gBAAgB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE;AAC5D,oBAAoB,IAAI,eAAe,GAAG,EAAE,CAAC,eAAe,CAAC;AAC7D,oBAAoB,IAAI,SAAS,GAAG,eAAe,IAAI,eAAe,CAAC,SAAS,CAAC;AACjF,oBAAoB,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE;AACxF,wBAAwB,OAAO;AAC/B,qBAAqB;AACrB,oBAAoB,IAAI,OAAO,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;AAC7D,oBAAoB,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AAE1G,oBAAoB,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;AACxC,wBAAwB,KAAK,EAAE,QAAQ;AACvC,wBAAwB,SAAS,EAAE,SAAS;AAC5C,wBAAwB,iBAAiB,EAAE,IAAI;AAC/C,wBAAwB,UAAU,EAAE,KAAK;AACzC,qBAAqB,CAAC,EAAE,kBAAkB,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;AAC/E,oBAAoB,IAAI,QAAQ,IAAI,kBAAkB,EAAE;AAExD,wBAAwB,IAAI,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE;AAC1E,4BAA4B,cAAc,EAAE,MAAM;AAClD,4BAA4B,SAAS,EAAE,CAAC,QAAQ,IAAIa,0BAAgB,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC;AACzF,4BAA4B,cAAc,EAAE,SAAS;AACrD,yBAAyB,CAAC,CAAC;AAE3B,wBAAwB,IAAI,eAAe,EAAE;AAC7C,4BAA4B,WAAW,CAAC,IAAI,CAAC;AAC7C,gCAAgC,MAAM,EAAE,eAAe;AACvD,gCAAgC,MAAM,EAAE,YAAY;AACpD,gCAAgC,KAAK,EAAE,QAAQ;AAC/C,gCAAgC,SAAS,EAAE,SAAS;AACpD,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC;AAClC,YAAY,CAAC,QAAQ,CAAC,cAAc,IAAI,EAAE,EAAE,MAAM,GAAG,CAAC;AACtD,YAAY,QAAQ,CAAC,MAAM;AAC3B,YAAY,QAAQ,CAAC,cAAc;AACnC,YAAY,QAAQ,CAAC,gBAAgB,EAAE;AACvC,YAAY,IAAI,SAAS,GAAG,EAAE,CAAC;AAC/B,YAAY,IAAI,CAAC,cAAc,CAAC;AAChC,gBAAgB,WAAW,EAAE,UAAU,KAAK,EAAE;AAC9C,oBAAoB,IAAI,CAAC,SAAS,EAAE;AACpC,wBAAwB,WAAW,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7F,qBAAqB;AAIrB,oBAAoB,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAGjD,oBAAoB,IAAI,aAAa,GAAG,CAACC,gCAAsB,CAAC,MAAM,CAAC;AACvE,yBAAyBF,2CAAiC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACvF,oBAAoB,IAAI,MAAM,EAAE;AAChC,wBAAwB,IAAI,CAAC,SAAS,EAAE;AAKxC,4BAA4B,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAClD,gCAAgC,EAAE,EAAE,eAAe;AAInD,gCAAgC,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO;AACvF,gCAAgC,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7D,gCAAgC,UAAU,EAAE,KAAK;AACjD,gCAAgC,iBAAiB,EAAE,IAAI;AACvD,6BAA6B,CAAC,CAAC;AAC/B,4BAA4B,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/C,gCAAgC,MAAM,GAAGrC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAC/F,gCAAgC,IAAI,aAAa,IAAI,MAAM,EAAE;AAC7D,oCAAoC,OAAO,MAAM,CAAC,WAAW,CAAC;AAC9D,iCAAiC;AACjC,gCAAgC,IAAI,SAAS,IAAI,MAAM,EAAE;AACzD,oCAAoC,OAAO,MAAM,CAAC,OAAO,CAAC;AAC1D,iCAAiC;AACjC,6BAA6B;AAC7B,yBAAyB;AAIzB,wBAAwB,IAAI,aAAa,EAAE;AAC3C,4BAA4B,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;AAClD,gCAAgC,OAAO,EAAE,QAAQ,CAAC,OAAO;AACzD,gCAAgC,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7D,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,qBAAqB;AAGrB,oBAAoB,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,aAAa,EAAE;AACjF,wBAAwB,KAAK,CAAC,MAAM,CAAC;AACrC,4BAA4B,EAAE,EAAE,eAAe;AAC/C,4BAA4B,MAAM,EAAE,UAAU,KAAK,EAAE,EAAE,EAAE;AACzD,gCAAgC,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;AACjF,gCAAgC,OAAO,SAAS,KAAK,YAAY,GAAG,KAAK,GAAG,MAAM,CAAC;AACnF,6BAA6B;AAC7B,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,QAAQ,CAAC,cAAc;AAEhD,gBAAgB,UAAU,EAAE,KAAK;AAGjC,gBAAgB,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;AAK3D,gBAAgB,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI,IAAI;AAC/D,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7E,YAAY,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,cAAc,EAAE;AAIzE,gBAAgB,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC;AACnF,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACvC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,kBAAkB,EAAE,QAAQ,EAAE;AAC5F,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,IAAI,GAAG,OAAO,kBAAkB,KAAK,UAAU;AAC3D,YAAY,kBAAkB,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AACtE,cAAc,kBAAkB,CAAC;AACjC,QAAQ,IAAI,IAAI,KAAK,MAAM,EAAE;AAC7B,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,UAAU,KAAK,EAAE;AAChE,YAAY,IAAI;AAChB,gBAAgB,KAAK,CAAC,kBAAkB,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9G,aAAa;AACb,YAAY,OAAO,KAAK,EAAE;AAC1B,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIc,iBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACvE,aAAa;AACb,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;AAChC,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE;AACnF,QAAQ,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,OAAO;AACjF,aAAa,OAAO,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,aAAa,GAAG,YAAY;AACvD,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxC,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,OAAO,EAAE;AACtD,YAAY,KAAK,CAAC,OAAO,CAAC,GAAG;AAC7B,gBAAgB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzC,gBAAgB,aAAa,EAAE,IAAI,CAAC,aAAa;AACjD,gBAAgB,YAAY,EAAE,IAAI,CAAC,YAAY;AAC/C,gBAAgB,aAAa,EAAE,IAAI,CAAC,aAAa;AACjD,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,OAAO,EAAE;AAC5D,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC;AAC/C,YAAY,SAAS,CAAC,aAAa,GAAG,EAAE,CAAC;AACzC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,QAAQ,EAAE;AAC3D,QAAQ,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAClE,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,QAAQ,EAAE;AACjE,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AACjD,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC3C,YAAY,IAAI,UAAU,GAAG;AAM7B,gBAAgB,gBAAgB,EAAE0B,0BAAgB,CAAC,QAAQ,CAAC;AAC5D,gBAAgB,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC;AAClF,gBAAgB,uBAAuB,EAAEC,uBAAa,CAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC;AACjF,gBAAgB,gBAAgB,EAAEC,wCAA8B,CAAC,QAAQ,CAAC;AAC1E,gBAAgB,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC;AAClE,gBAAgB,WAAW,EAAEC,sCAA4B,CAAC;AAC1D,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE;AACpD,oBAAoB,EAAE,IAAI,EAAE,YAAY,EAAE;AAC1C,oBAAoB,EAAE,IAAI,EAAE,aAAa,EAAE;AAC3C,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE;AACtC,iBAAiB,EAAE,QAAQ,CAAC;AAC5B,gBAAgB,WAAW,EAAEC,0BAAgB,CAAChC,gCAAsB,CAAC,QAAQ,CAAC,CAAC;AAG/E,gBAAgB,OAAO,EAAEZ,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AACjH,wBAAwB,IAAI,GAAG,CAAC,IAAI,KAAK,qBAAqB;AAC9D,4BAA4B,GAAG,CAAC,SAAS,KAAK,OAAO,EAAE;AACvD,4BAA4B,OAAOA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACvF,yBAAyB;AACzB,wBAAwB,OAAO,GAAG,CAAC;AACnC,qBAAqB,CAAC,EAAE,CAAC;AACzB,aAAa,CAAC;AACd,YAAY,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC5C,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,QAAQ,EAAE,SAAS,EAAE;AACzE,QAAQ,OAAOA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAC;AAC7F,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,OAAO,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAIlD,QAAQ,OAAO,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AAC9G,QAAQ,IAAI,OAAO,OAAO,CAAC,2BAA2B,KAAK,WAAW,EAAE;AACxE,YAAY,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;AACxD,SAAS;AACT,QAAQ,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAQ,IAAI,UAAU,GAAG,IAAI,eAAe,CAAC;AAC7C,YAAY,YAAY,EAAE,IAAI;AAC9B,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,OAAO,EAAE,OAAO;AAC5B,SAAS,CAAC,CAAC;AACX,QAAQ,UAAU,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;AACxC,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAGxD,QAAQ,SAAS,CAAC,IAAI,CAAC;AACvB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,eAAe,EAAE,UAAU;AACvC,YAAY,SAAS,EAAE,UAAU,CAAC,SAAS;AAC3C,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE;AAC/D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AACrE,QAAQc,iBAAS,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACrC,QAAQA,iBAAS,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC,CAAC;AACzD,QAAQA,iBAAS,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;AAClD,QAAQA,iBAAS,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAClD,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAEd,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1F,aAAa,IAAI,CAAC,UAAU,MAAM,EAAE;AACpC,YAAY,OAAO,MAAM,IAAIA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC;AACxF,oBAAoB,QAAQ,EAAE,KAAK;AACnC,oBAAoB,IAAI,EAAE,MAAM,CAAC,IAAI;AACrC,oBAAoB,WAAW,EAAE,OAAO,CAAC,WAAW;AACpD,oBAAoB,EAAE,EAAE,OAAO;AAC/B,iBAAiB,CAAC,EAAE,CAAC,CAAC;AACtB,SAAS,CAAC;AACV,aAAa,OAAO,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AACvE,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,eAAe,GAAG,YAAY;AACzD,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;AAC7C,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,iBAAiB,GAAG,YAAY;AAC3D,QAAQ,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACvC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,YAAY;AAC5D,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAChD,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,OAAO,EAAE;AACjE,QAAQ,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,OAAO,EAAE;AAC5E,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,IAAI,SAAS;AACrB,YAAY,SAAS,CAAC,IAAI,EAAE,CAAC;AAC7B,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,OAAO,EAAE;AAC3D,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG;AAC5C,YAAY,cAAc,EAAE,IAAI;AAChC,SAAS,CAAC,EAAE;AAMZ,QAAQ,IAAI,CAAC,oBAAoB,CAACgC,yBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;AACzD,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAClD,YAAY,IAAI,SAAS,CAAC,eAAe,EAAE;AAG3C,gBAAgB,SAAS,CAAC,aAAa,GAAG/B,qBAAa,CAAC,OAAO,CAAC;AAChE,aAAa;AACb,iBAAiB;AACjB,gBAAgB,SAAS,CAAC,IAAI,EAAE,CAAC;AACjC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;AAChC,YAAY,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,SAAS;AAET,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,OAAO,EAAE;AACrE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,QAAQ,CAAC,EAAE;AACvD,QAAQ,IAAI,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,QAAQ,IAAI,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC,QAAQ,IAAI,yBAAyB,GAAG,IAAI,GAAG,EAAE,CAAC;AAClD,QAAQ,IAAI,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3C,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACpC,YAAY,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAC5C,gBAAgB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC9C,oBAAoB,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/C,oBAAoB,yBAAyB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/D,iBAAiB;AACjB,qBAAqB,IAAI4C,wBAAc,CAAC,IAAI,CAAC,EAAE;AAC/C,oBAAoB,IAAI,WAAW,GAAGC,eAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACnE,oBAAoB,UAAU,CAAC,GAAG,CAAC,WAAW,EAAER,0BAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;AACxE,oBAAoB,yBAAyB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AACtE,iBAAiB;AACjB,qBAAqB,IAAIS,yBAAe,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9D,oBAAoB,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjD,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE;AACpD,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,eAAe,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;AAChE,YAAY,IAAI,EAAE,EAAE;AACpB,gBAAgB,IAAI,OAAO,KAAK,KAAK,EAAE;AACvC,oBAAoB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC7C,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;AACnF,gBAAgB,IAAI,WAAW,KAAK,SAAS;AAC7C,qBAAqB,OAAO,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE;AAClE,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,IAAI,OAAO,KAAK,QAAQ;AACxC,qBAAqB,SAAS,IAAI,yBAAyB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC3E,qBAAqB,QAAQ,IAAI,yBAAyB,CAAC,GAAG,CAACD,eAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAClF,oBAAoB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC7C,oBAAoB,IAAI,SAAS;AACjC,wBAAwB,yBAAyB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACvE,oBAAoB,IAAI,QAAQ;AAChC,wBAAwB,yBAAyB,CAAC,GAAG,CAACA,eAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;AAC7E,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,kBAAkB,CAAC,IAAI,EAAE;AACrC,YAAY,kBAAkB,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE;AAI1D,gBAAgB,IAAI,OAAO,GAAGE,sBAAY,CAAC,oBAAoB,CAAC,CAAC;AACjE,gBAAgB,IAAI,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AAC7D,oBAAoB,QAAQ,EAAE,OAAO,CAAC,KAAK;AAC3C,oBAAoB,SAAS,EAAE,OAAO,CAAC,SAAS;AAChD,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,EAAE,GAAG,IAAI,eAAe,CAAC;AAC7C,oBAAoB,YAAY,EAAE,KAAK;AACvC,oBAAoB,SAAS,EAAE,SAAS;AACxC,oBAAoB,OAAO,EAAEhD,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;AAC7F,iBAAiB,CAAC,CAAC;AACnB,gBAAgBc,iBAAS,CAAC,EAAE,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;AAClD,gBAAgB,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;AACjD,gBAAgB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACzC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,yBAAyB,CAAC,IAAI,EAAE;AAC5E,YAAY,yBAAyB,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,iBAAiB,EAAE;AACrF,gBAAgB,IAAI,CAAC,QAAQ,EAAE;AAC/B,oBAAoB,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACtE,oBAAoB,IAAI,SAAS,EAAE;AACnC,wBAAwB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIA,iBAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACtF,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIA,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3E,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,cAAc,EAAE;AAChF,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE,EAAE,cAAc,GAAG,KAAK,CAAC,EAAE;AAClE,QAAQ,IAAI,uBAAuB,GAAG,EAAE,CAAC;AACzC,QAAQ,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,eAAe,EAAE,OAAO,EAAE;AACjH,YAAY,IAAI,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;AAClE,YAAY,eAAe,CAAC,gBAAgB,EAAE,CAAC;AAC/C,YAAY,IAAI,cAAc;AAC9B,iBAAiB,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,YAAY,CAAC,EAAE;AAC7E,gBAAgB,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;AACxE,aAAa;AACb,YAAY,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAClD,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChC,QAAQ,OAAO,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AACpD,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,eAAe,EAAE;AAC3E,QAAQ,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;AACnF,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,OAAO,EAAE;AACzE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AACjE,QAAQ,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3O,QAAQ,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACtC,QAAQ,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACxD,QAAQ,IAAI,cAAc,GAAG,UAAU,SAAS,EAAE;AAClD,YAAY,OAAO,KAAK,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE;AAC5G,gBAAgB,IAAI,WAAW,KAAK,UAAU,EAAE;AAGhD,oBAAoB,IAAI,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;AAChE,wBAAwB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC1C,4BAA4B,KAAK,EAAE,KAAK;AACxC,4BAA4B,MAAM,EAAE,MAAM,CAAC,IAAI;AAC/C,4BAA4B,MAAM,EAAE,mBAAmB;AACvD,4BAA4B,SAAS,EAAE,SAAS;AAChD,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB,oBAAoB,KAAK,CAAC,gBAAgB,EAAE,CAAC;AAC7C,iBAAiB;AACjB,gBAAgB,IAAI,SAAS,GAAGa,+BAAqB,CAAC,MAAM,CAAC,CAAC;AAC9D,gBAAgB,IAAI,iBAAiB,GAAGsB,qCAA8B,CAAC,MAAM,CAAC,CAAC;AAC/E,gBAAgB,IAAI,SAAS,IAAI,iBAAiB,EAAE;AACpD,oBAAoB,IAAIC,QAAM,GAAG,EAAE,CAAC;AACpC,oBAAoB,IAAI,SAAS,EAAE;AACnC,wBAAwBA,QAAM,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7D,qBAAqB;AACrB,oBAAoB,IAAI,iBAAiB,EAAE;AAC3C,wBAAwBA,QAAM,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAACC,6BAAsB,CAAC,CAAC;AAC1F,qBAAqB;AAIrB,oBAAoB,IAAI,WAAW,KAAK,MAAM,IAAI,iBAAiB,EAAE;AACrE,wBAAwB,MAAM,IAAIjC,kBAAW,CAACgC,QAAM,CAAC,CAAC;AACtD,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,IAAI,WAAW,KAAK,QAAQ,EAAE;AAC9C,oBAAoB,OAAO,MAAM,CAAC,MAAM,CAAC;AACzC,iBAAiB;AACjB,gBAAgB,OAAO,MAAM,CAAC;AAC9B,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;AAC1D,YAAY,IAAI,mBAAmB,GAAG,IAAI,CAAC,UAAU;AACrD,iBAAiB,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC;AAChE,iBAAiB,IAAI,CAAC,cAAc,CAAC,CAAC;AACtC,YAAY,OAAO,IAAI7B,oBAAU,CAAC,UAAU,QAAQ,EAAE;AACtD,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC;AAC/B,gBAAgB,mBAAmB,CAAC,IAAI,CAAC,UAAU,UAAU,EAAE,EAAE,QAAQ,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnI,gBAAgB,OAAO,YAAY,EAAE,OAAO,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;AACxE,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;AACzC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,OAAO,EAAE;AAC1D,QAAQ,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAC3C,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,OAAO,EAAE;AACrE,QAAQ,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,OAAO,EAAE;AAM5D,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5C,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACvC,YAAY,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;AAC1C,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACzC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY;AAC1D,QAAQ,IAAI,IAAI,CAAC,WAAW;AAC5B,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;AAC/B,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACxE,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,aAAa,GAAG,YAAY;AACvD,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU;AAElG,IAAI,aAAa,EAAE;AACnB,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,kBAAkB,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE;AACvM,QAAQ,IAAI,UAAU,CAAC;AACvB,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC;AACzG,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,IAAI,EAAE,GAAG,IAAI,EAAE,yBAAyB,GAAG,EAAE,CAAC,uBAAuB,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAClG,YAAY,IAAI,SAAS,GAAG;AAC5B,gBAAgB,KAAK,EAAE,WAAW;AAClC,gBAAgB,SAAS,EAAE,SAAS;AACpC,gBAAgB,aAAa,EAAEiB,0BAAgB,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;AACtE,gBAAgB,OAAO,EAAE,IAAI,CAAC,cAAc,CAACtC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC;AAC7G,gBAAgB,UAAU,EAAE,UAAU;AACtC,aAAa,CAAC;AACd,YAAY,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;AACxC,YAAY,IAAI,aAAa,EAAE;AAC/B,gBAAgB,IAAI,oBAAoB,GAAG8C,eAAK,CAAC,WAAW,CAAC,CAAC;AAC9D,gBAAgB,IAAI,SAAS,GAAGM,wBAAkB,CAAC,SAAS,CAAC,CAAC;AAC9D,gBAAgB,IAAI,KAAK,GAAG,yBAAyB,CAAC,MAAM,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;AAC9F,gBAAgB,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;AAC9C,gBAAgB,IAAI,CAAC,UAAU,EAAE;AACjC,oBAAoB,IAAI,OAAO,GAAG,IAAIC,iBAAO,CAAC;AAC9C,wBAAwBC,YAAO,CAAC,IAAI,EAAE,SAAS,CAAC;AAChD,qBAAqB,CAAC,CAAC;AACvB,oBAAoB,UAAU,GAAG,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC;AAC5D,oBAAoB,OAAO,CAAC,UAAU,CAAC,YAAY;AACnD,wBAAwB,yBAAyB,CAAC,MAAM,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;AAC1F,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,iBAAiB;AACjB,gBAAgB,UAAU,GAAG,IAAID,iBAAO,CAAC;AACzC,oBAAoBC,YAAO,CAAC,IAAI,EAAE,SAAS,CAAC;AAC5C,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS;AACT,aAAa;AACb,YAAY,UAAU,GAAG,IAAID,iBAAO,CAAC,CAAChC,oBAAU,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACpE,YAAY,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,UAAU,GAAGc,kBAAQ,CAAC,UAAU,EAAE,UAAU,MAAM,EAAE;AAChE,gBAAgB,OAAO,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC;AACrD,oBAAoB,QAAQ,EAAE,WAAW;AACzC,oBAAoB,YAAY,EAAE,MAAM;AACxC,oBAAoB,OAAO,EAAE,OAAO;AACpC,oBAAoB,SAAS,EAAE,SAAS;AACxC,iBAAiB,CAAC,CAAC;AACnB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,SAAS,EAAE,kBAAkB,EAAE,OAAO,EAAE;AAClG,QAAQ,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAI7E,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACtE,QAAQ,OAAOA,kBAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE,UAAU,MAAM,EAAE;AACxH,YAAY,IAAI,aAAa,GAAGC,oCAA0B,CAAC,MAAM,CAAC,CAAC;AACnE,YAAY,IAAI,SAAS,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACrD,YAAY,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAGlD,YAAY,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,EAAE;AACtD,gBAAgB,IAAI,SAAS,IAAI,WAAW,KAAK,MAAM,EAAE;AAEzD,oBAAoB,MAAM,SAAS,CAAC,SAAS,CAAC,IAAIlB,kBAAW,CAAC;AAC9D,wBAAwB,aAAa,EAAE,aAAa;AACpD,qBAAqB,CAAC,CAAC,CAAC;AACxB,iBAAiB;AAIjB,gBAAgB,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;AACxF,gBAAgB,SAAS,CAAC,SAAS,EAAE,CAAC;AACtC,aAAa;AACb,YAAY,IAAI,GAAG,GAAG;AACtB,gBAAgB,IAAI,EAAE,MAAM,CAAC,IAAI;AACjC,gBAAgB,OAAO,EAAE,KAAK;AAC9B,gBAAgB,aAAa,EAAEjB,qBAAa,CAAC,KAAK;AAClD,aAAa,CAAC;AAKd,YAAY,IAAI,SAAS,IAAI,WAAW,KAAK,MAAM,EAAE;AACrD,gBAAgB,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AAClC,aAAa;AACb,YAAY,IAAI,SAAS,IAAI,WAAW,KAAK,QAAQ,EAAE;AACvD,gBAAgB,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC;AAC3C,gBAAgB,GAAG,CAAC,aAAa,GAAGA,qBAAa,CAAC,KAAK,CAAC;AACxD,aAAa;AACb,YAAY,OAAO,GAAG,CAAC;AACvB,SAAS,EAAE,UAAU,YAAY,EAAE;AACnC,YAAY,IAAI,KAAK,GAAGgB,oBAAa,CAAC,YAAY,CAAC,GAAG,YAAY,IAAI,IAAIC,kBAAW,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AAEvH,YAAY,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,EAAE;AACtD,gBAAgB,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC3C,aAAa;AACb,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,OAAO,EAAE,OAAO;AAI5E,IAAI,aAAa,EAAE,KAAK,EAAE;AAC1B,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAGjB,qBAAa,CAAC,OAAO,CAAC,EAAE;AAChF,QAAQ,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE;AACxD,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACpE,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC/C,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;AACtD,QAAQ,IAAI,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,KAAK,aAAa,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,KAAK,MAAM,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,iBAAiB,EAAE,iBAAiB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,2BAA2B,EAAE,2BAA2B,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACrd,QAAQ,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE;AACpD,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,WAAW,EAAE,WAAW;AACpC,YAAY,WAAW,EAAE,WAAW;AACpC,YAAY,iBAAiB,EAAE,iBAAiB;AAChD,YAAY,2BAA2B,EAAE,2BAA2B;AACpE,YAAY,OAAO,EAAE,OAAO;AAC5B,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,aAAa,GAAG,UAAU,SAAS,EAAE;AAIjD,YAAY,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;AAC7C,YAAY,IAAI,eAAe,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;AACjG,YAAY;AAGZ,YAAY,UAAU,CAAC,WAAW,KAAK,SAAS;AAGhD,gBAAgB,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;AAClD,gBAAgB,SAAS,CAAC,eAAe,EAAE;AAC3C,gBAAgB,SAAS,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAC1F,aAAa;AACb,YAAY,OAAO,eAAe,CAAC;AACnC,SAAS,CAAC;AAGV,QAAQ,IAAI,eAAe,GAAG,YAAY,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;AAC3F,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,MAAM,EAAE;AAC3D,YAAY,eAAe,EAAE,CAAC;AAE9B,YAAY,UAAU,CAAC,YAAY,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACvE,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,OAAO,EAAE,oBAAoB,CAAC;AAQ1C,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;AACrE,YAAY,OAAO,GAAG,IAAIoD,iBAAO,CAAC,IAAI,CAAC,UAAU;AACjD,iBAAiB,oBAAoB,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC;AACjG,iBAAiB,IAAI,CAAC,aAAa,CAAC;AACpC,iBAAiB,IAAI,CAAC,UAAU,eAAe,EAAE,EAAE,OAAO,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAMvF,YAAY,oBAAoB,GAAG,IAAI,CAAC;AACxC,SAAS;AACT,aAAa;AACb,YAAY,IAAI,eAAe,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACtE,YAAY,oBAAoB,GAAG,eAAe,CAAC,QAAQ,CAAC;AAC5D,YAAY,OAAO,GAAG,IAAIA,iBAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;AAC/D,QAAQ,OAAO;AACf,YAAY,OAAO,EAAE,OAAO;AAC5B,YAAY,QAAQ,EAAE,oBAAoB;AAC1C,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,EAAE,EAAE;AAC1D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGL,sBAAY,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,CAAC;AACxR,QAAQ,IAAI,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;AAC5C,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE;AAC9E,gBAAgB,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE;AACjD,oBAAoB,EAAE,EAAE,EAAE;AAC1B,oBAAoB,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE;AAC/D,iBAAiB,CAAC,CAAC;AACnB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7B,gBAAgB,MAAM,EAAE,WAAW;AA8BnC,gBAAgB,UAAU,EAAE,CAAC,UAAU,IAAI,gBAAgB,KAAK,KAAK;AAQrE,gBAAgB,gBAAgB,EAAE,gBAAgB;AAClD,gBAAgB,cAAc,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;AACjE,oBAAoB,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,YAAY,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;AACjG,oBAAoB,IAAI,EAAE,EAAE;AAC5B,wBAAwB,IAAI,cAAc,EAAE;AAI5C,4BAA4B,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACnE,4BAA4B,IAAI,MAAM,GAAG,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC5E,4BAA4B,IAAI,MAAM,KAAK,IAAI,EAAE;AAGjD,gCAAgC,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;AACtD,6BAA6B;AAG7B,4BAA4B,IAAI,MAAM,KAAK,KAAK,EAAE;AAClD,gCAAgC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACxD,6BAA6B;AAG7B,4BAA4B,OAAO,MAAM,CAAC;AAC1C,yBAAyB;AACzB,wBAAwB,IAAI,cAAc,KAAK,IAAI,EAAE;AAIrD,4BAA4B,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5G,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,mBAAmB,CAAC,IAAI,EAAE;AACtC,YAAY,mBAAmB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE;AAC/D,gBAAgB,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACvE,gBAAgB,IAAI,MAAM,CAAC;AAG3B,gBAAgB,IAAI,cAAc,EAAE;AACpC,oBAAoB,IAAI,CAAC,IAAI,EAAE;AAC/B,wBAAwB,IAAI,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC;AACnD,wBAAwB,IAAI,CAAC,KAAK,EAAE,CAAC;AACrC,wBAAwB,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAC9C,qBAAqB;AACrB,oBAAoB,MAAM,GAAG,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAChE,iBAAiB;AAEjB,gBAAgB,IAAI,CAAC,cAAc,IAAI,MAAM,KAAK,IAAI,EAAE;AACxD,oBAAoB,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;AAC1C,iBAAiB;AACjB,gBAAgB,IAAI,MAAM,KAAK,KAAK,EAAE;AACtC,oBAAoB,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAC5C,iBAAiB;AACjB,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AAChE,oBAAoB,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACxD,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,gBAAgB,EAAE;AAQ9B,YAAY,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,OAAO,EAAE;AAC9D,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,QAAQ,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7D,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAY,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;AACnE,YAAY,IAAI,aAAa,GAAG,CAAC,EAAE,GAAGpC,gCAAsB,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC;AAC1H,YAAY,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC;AAChK,YAAY,IAAI,IAAI,CAAC,WAAW;AAChC,gBAAgB,WAAW,KAAK,UAAU;AAC1C,gBAAgB,CAAC2C,kCAAwB,CAAC,QAAQ,CAAC;AACnD,gBAAgB,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AACjE,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC/D,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIzC,iBAAS,CAAC,IAAI;AAC9D,oBAAoB,EAAE;AACtB,oBAAoB,CAAC,EAAE,GAAGwB,0BAAgB,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW,CAAC;AAC1L,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS;AACT,QAAQ,QAAQ,IAAI,CAAC,WAAW;AAChC,YAAYkB,qBAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC;AACrD,cAAc,IAAI,EAAE;AACpB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,OAAO,EAAE;AAC7D,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;AAClG,QAAQ,OAAO,IAAI,CAAC,WAAW;AAC/B,YAAYC,oBAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;AAClE,cAAc,IAAI,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,SAAS,EAAE,EAAE;AAIvE,IAAI,aAAa,EAAE;AACnB,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,kBAAkB,GAAG,EAAE,CAAC,kBAAkB,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,2BAA2B,GAAG,EAAE,CAAC,2BAA2B,CAAC;AAC7R,QAAQ,IAAI,gBAAgB,GAAG,SAAS,CAAC,aAAa,CAAC;AACvD,QAAQ,SAAS,CAAC,IAAI,CAAC;AACvB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,aAAa,EAAE,aAAa;AACxC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,SAAS,GAAG,YAAY,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;AACpE,QAAQ,IAAI,gBAAgB,GAAG,UAAU,IAAI,EAAE,aAAa,EAAE;AAC9D,YAAY,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAG,SAAS,CAAC,aAAa,IAAIxD,qBAAa,CAAC,OAAO,CAAC,EAAE;AAC/G,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AACnC,YAAY,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,iBAAiB,IAAI,CAACE,WAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;AACxF,gBAAgB,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpD,aAAa;AACb,YAAY,IAAI,QAAQ,GAAG,UAAU,IAAI,EAAE;AAC3C,gBAAgB,OAAOkB,oBAAU,CAAC,EAAE,CAACrB,cAAQ,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,wBAAwB,CAAC,aAAa,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAC3L,aAAa,CAAC;AACd,YAAY,IAAI,IAAI,IAAI,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE;AACzE,gBAAgB,OAAO,KAAK,CAAC,UAAU;AACvC,qBAAqB,YAAY,CAAC;AAClC,oBAAoB,QAAQ,EAAE,KAAK;AACnC,oBAAoB,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAChD,oBAAoB,OAAO,EAAE,OAAO;AACpC,oBAAoB,SAAS,EAAE,SAAS;AACxC,oBAAoB,sBAAsB,EAAE,IAAI;AAChD,iBAAiB,CAAC;AAClB,qBAAqB,IAAI,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7F,aAAa;AAKb,YAAY,IAAI,WAAW,KAAK,MAAM;AACtC,gBAAgB,aAAa,KAAKC,qBAAa,CAAC,OAAO;AACvD,gBAAgB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAC7C,gBAAgB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,aAAa;AACb,YAAY,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;AAClC,SAAS,CAAC;AACV,QAAQ,IAAI,kBAAkB,GAAG,WAAW,KAAK,UAAU,GAAG,CAAC;AAG/D,cAAc,CAAC,aAAa,KAAKA,qBAAa,CAAC,OAAO;AACtD,gBAAgB,kBAAkB,KAAK,OAAO;AAC9C,gBAAgB,CAAC;AACjB,kBAAkB,CAAC,EAAgC;AACnD,QAAQ,IAAI,eAAe,GAAG,YAAY;AAC1C,YAAY,OAAO,KAAK,CAAC,kBAAkB,CAAC,SAAS,EAAE,kBAAkB,EAAE;AAC3E,gBAAgB,KAAK,EAAE,KAAK;AAC5B,gBAAgB,SAAS,EAAE,SAAS;AACpC,gBAAgB,OAAO,EAAE,OAAO;AAChC,gBAAgB,WAAW,EAAE,WAAW;AACxC,gBAAgB,WAAW,EAAE,WAAW;AACxC,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,QAAQ,IAAI,YAAY,GAAG,2BAA2B;AACtD,YAAY,OAAO,gBAAgB,KAAK,QAAQ;AAChD,YAAY,gBAAgB,KAAK,aAAa;AAC9C,YAAY,wBAAwB,CAAC,aAAa,CAAC,CAAC;AACpD,QAAQ,QAAQ,WAAW;AAC3B,YAAY,QAAQ;AACpB,YAAY,KAAK,aAAa,EAAE;AAChC,gBAAgB,IAAI,IAAI,GAAG,SAAS,EAAE,CAAC;AACvC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,oBAAoB,OAAO;AAC3B,wBAAwB,QAAQ,EAAE,KAAK;AACvC,wBAAwB,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;AAChF,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,gBAAgB,IAAI,iBAAiB,IAAI,YAAY,EAAE;AACvD,oBAAoB,OAAO;AAC3B,wBAAwB,QAAQ,EAAE,IAAI;AACtC,wBAAwB,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;AAC5E,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;AACxE,aAAa;AACb,YAAY,KAAK,mBAAmB,EAAE;AACtC,gBAAgB,IAAI,IAAI,GAAG,SAAS,EAAE,CAAC;AACvC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,YAAY,EAAE;AACxE,oBAAoB,OAAO;AAC3B,wBAAwB,QAAQ,EAAE,IAAI;AACtC,wBAAwB,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;AAC5E,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;AACxE,aAAa;AACb,YAAY,KAAK,YAAY;AAC7B,gBAAgB,OAAO;AACvB,oBAAoB,QAAQ,EAAE,KAAK;AACnC,oBAAoB,OAAO,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;AACnF,iBAAiB,CAAC;AAClB,YAAY,KAAK,cAAc;AAC/B,gBAAgB,IAAI,YAAY,EAAE;AAClC,oBAAoB,OAAO;AAC3B,wBAAwB,QAAQ,EAAE,IAAI;AACtC,wBAAwB,OAAO,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;AACnF,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;AACxE,YAAY,KAAK,UAAU;AAC3B,gBAAgB,IAAI,YAAY,EAAE;AAClC,oBAAoB,OAAO;AAC3B,wBAAwB,QAAQ,EAAE,IAAI;AAItC,wBAAwB,OAAO,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;AAC3F,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;AACxE,YAAY,KAAK,SAAS;AAC1B,gBAAgB,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;AACxD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,OAAO,EAAE;AACzD,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACnD,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AACpE,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACzC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,OAAO,EAAE;AAC/D,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AACjD,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACjE,QAAQ,OAAOD,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AAC5H,KAAK,CAAC;AACN,IAAI,OAAO,YAAY,CAAC;AACxB,CAAC,EAAE,CAAC;;AC9pCJ,IAAI,UAAU,KAAkB,YAAY;AAC5C,IAAI,SAAS,UAAU,CAAC,EAAE,EAAE;AAC5B,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,eAAe,GAAG,EAAE,CAAC,eAAe,CAAC;AACjH,QAAQ,IAAI,CAAC,wBAAwB,GAAG,IAAI,OAAO,EAAE,CAAC;AACtD,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACjC,SAAS;AACT,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,IAAI,eAAe,EAAE;AAC7B,YAAY,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;AACrD,SAAS;AACT,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;AAC7D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;AAC9C,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACtC,YAAY,SAAS,CAAC,OAAO,CAAC,UAAU,aAAa,EAAE;AACvD,gBAAgB,KAAK,CAAC,SAAS,GAAG0D,mBAAS,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC5E,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa;AACb,YAAY,IAAI,CAAC,SAAS,GAAGA,mBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAClE,SAAS;AACT,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;AAC7D,QAAQ,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAC5B,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AACpD,QAAQ,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;AACpC,KAAK,CAAC;AAKN,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,EAAE,EAAE;AACtD,QAAQ,OAAOzB,eAAS,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,UAAU,EAAE,EAAE;AAChE,YAAY,IAAI,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AAC5M,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,IAAI,QAAQ,EAAE;AAC9B,oBAAoB,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,sBAAsB,CAAC,CAAC,IAAI,CAAC,UAAU,WAAW,EAAE,EAAE,QAAQlC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7Q,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,CAAC,GAAa,YAAY,CAAC,CAAC;AACpD,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,eAAe,EAAE;AACzE,QAAQ,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AAC/C,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,YAAY;AAC1D,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC;AACpC,KAAK,CAAC;AAGN,IAAI,UAAU,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE;AAC3D,QAAQ,IAAIyC,uBAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE;AACjD,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE;AAChC,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AAEN,IAAI,UAAU,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE;AAC3D,QAAQ,OAAOkB,sCAA4B,CAAC,QAAQ,CAAC,CAAC;AACtD,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,OAAO,EAAE;AAC7D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,QAAQ,OAAO3D,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK;AAE7D,YAAY,WAAW,EAAE,UAAU,GAAG,EAAE;AACxC,gBAAgB,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC3C,aAAa,EAAE,CAAC,CAAC;AACjB,KAAK,CAAC;AAIN,IAAI,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,UAAU,EAAE;AACtE,QAAQ,OAAOiC,eAAS,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AAC1F,YAAY,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,EAAE;AACzD,YAAY,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AACrD,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,IAAI,QAAQ,EAAE;AAC9B,oBAAoB,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQlC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1Q,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,CAAC,GAAaA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC;AAC/D,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,QAAQ,EAAE;AACpE,QAAQ,IAAI,cAAc,GAAG,KAAK,CAAC;AACnC,QAAQ4D,aAAK,CAAC,QAAQ,EAAE;AACxB,YAAY,SAAS,EAAE;AACvB,gBAAgB,KAAK,EAAE,UAAU,IAAI,EAAE;AACvC,oBAAoB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;AACxE,wBAAwB,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE;AAC5E,4BAA4B,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ;AAC9D,gCAAgC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,cAAc;AACjE,gCAAgC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC;AACzD,yBAAyB,CAAC,CAAC;AAC3B,wBAAwB,IAAI,cAAc,EAAE;AAC5C,4BAA4B,OAAOC,aAAK,CAAC;AACzC,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,cAAc,CAAC;AAC9B,KAAK,CAAC;AAEN,IAAI,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,QAAQ,EAAE,SAAS,EAAE;AAClF,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAC/B,YAAY,KAAK,EAAEC,oCAA0B,CAAC,QAAQ,CAAC;AACvD,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,iBAAiB,EAAE,IAAI;AACnC,YAAY,UAAU,EAAE,KAAK;AAC7B,SAAS,CAAC,CAAC,MAAM,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,UAAU,EAAE,WAAW,EAAE;AAC9E,QAAQ,OAAO7B,eAAS,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,sBAAsB,EAAE;AAC9I,YAAY,IAAI,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,uBAAuB,CAAC;AAChL,YAAY,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AACrD,YAAY,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,EAAE;AACzD,YAAY,IAAI,eAAe,KAAK,KAAK,CAAC,EAAE,EAAE,eAAe,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE;AAC/F,YAAY,IAAI,sBAAsB,KAAK,KAAK,CAAC,EAAE,EAAE,sBAAsB,GAAG,KAAK,CAAC,EAAE;AACtF,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,cAAc,GAAG9B,2BAAiB,CAAC,QAAQ,CAAC,CAAC;AAC7D,gBAAgB,SAAS,GAAGE,gCAAsB,CAAC,QAAQ,CAAC,CAAC;AAC7D,gBAAgB,WAAW,GAAGD,2BAAiB,CAAC,SAAS,CAAC,CAAC;AAC3D,gBAAgB,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AACnG,gBAAgB,mBAAmB,GAAG,cAAc,CAAC,SAAS,CAAC;AAC/D,gBAAgB,oBAAoB,GAAG,mBAAmB;AAC1D,oBAAoB,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;AAC/D,wBAAwB,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;AACpD,sBAAsB,OAAO,CAAC;AAC9B,gBAAgB,EAAE,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;AAChE,gBAAgB,WAAW,GAAG;AAC9B,oBAAoB,WAAW,EAAE,WAAW;AAC5C,oBAAoB,OAAO,EAAEL,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9F,oBAAoB,SAAS,EAAE,SAAS;AACxC,oBAAoB,eAAe,EAAE,eAAe;AACpD,oBAAoB,oBAAoB,EAAE,oBAAoB;AAC9D,oBAAoB,iBAAiB,EAAE,EAAE;AACzC,oBAAoB,mBAAmB,EAAE,mBAAmB;AAC5D,oBAAoB,sBAAsB,EAAE,sBAAsB;AAClE,iBAAiB,CAAC;AAClB,gBAAgB,uBAAuB,GAAG,KAAK,CAAC;AAChD,gBAAgB,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,YAAY,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,MAAM,EAAE,EAAE,QAAQ;AAC9K,wBAAwB,MAAM,EAAE,MAAM;AACtC,wBAAwB,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;AACxE,qBAAqB,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5B,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,YAAY,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,EAAE;AACxH,QAAQ,OAAOiC,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AAC3D,YAAY,IAAI,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC;AACzE,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC;AAC7B,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,WAAW,GAAG,WAAW,CAAC,WAAW,EAAE,OAAO,GAAG,WAAW,CAAC,OAAO,EAAE,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AACxH,gBAAgB,cAAc,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7C,gBAAgB,OAAO,GAAG,UAAU,SAAS,EAAE,EAAE,OAAOD,eAAS,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACrG,oBAAoB,IAAI,QAAQ,EAAE,aAAa,CAAC;AAChD,oBAAoB,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC3D,wBAAwB,IAAI,CAAC,uBAAuB;AACpD,4BAA4B,CAAC,WAAW,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AAG7E,4BAA4B,OAAO,CAAC,CAAC,EAAY,CAAC;AAClD,yBAAyB;AACzB,wBAAwB,IAAI,CAAC3B,uBAAa,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAElE,4BAA4B,OAAO,CAAC,CAAC,EAAY,CAAC;AAClD,yBAAyB;AACzB,wBAAwB,IAAIC,iBAAO,CAAC,SAAS,CAAC,EAAE;AAChD,4BAA4B,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,WAAW,EAAE;AAC5J,oCAAoC,IAAI,EAAE,CAAC;AAC3C,oCAAoC,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AAC5E,wCAAwC,cAAc,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE;AACpE,4CAA4C,EAAE,CAACC,gCAAsB,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW;AAC/F,4CAA4C,EAAE,EAAE,CAAC;AACjD,qCAAqC;AACrC,iCAAiC,CAAC,CAAC,CAAC;AACpC,yBAAyB;AACzB,wBAAwB,IAAIsD,0BAAgB,CAAC,SAAS,CAAC,EAAE;AACzD,4BAA4B,QAAQ,GAAG,SAAS,CAAC;AACjD,yBAAyB;AACzB,6BAA6B;AAE7B,4BAA4B,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzE,4BAA4BjD,iBAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1E,yBAAyB;AACzB,wBAAwB,IAAI,QAAQ,IAAI,QAAQ,CAAC,aAAa,EAAE;AAChE,4BAA4B,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9E,4BAA4B,IAAI,WAAW,CAAC,eAAe,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE;AAChG,gCAAgC,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,YAAY,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,cAAc,EAAE;AACtL,wCAAwC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC5E,qCAAqC,CAAC,CAAC,CAAC;AACxC,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB,OAAO,CAAC,CAAC,EAAY,CAAC;AAC9C,qBAAqB,CAAC,CAAC;AACvB,iBAAiB,CAAC,CAAC,EAAE,CAAC;AACtB,gBAAgB,OAAO,CAAC,CAAC,GAAa,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY;AACzG,wBAAwB,OAAOkD,wBAAc,CAAC,cAAc,CAAC,CAAC;AAC9D,qBAAqB,CAAC,CAAC,CAAC;AACxB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,EAAE;AAC1G,QAAQ,OAAO/B,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AAC3D,YAAY,IAAI,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC;AACpI,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC;AAC7B,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,IAAI,CAAC,SAAS,EAAE;AAChC,oBAAoB,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,CAAC;AAChD,iBAAiB;AACjB,gBAAgB,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAClD,gBAAgB,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AAC7C,gBAAgB,gBAAgB,GAAGzB,gCAAsB,CAAC,KAAK,CAAC,CAAC;AACjE,gBAAgB,SAAS,GAAG,SAAS,KAAK,gBAAgB,CAAC;AAC3D,gBAAgB,aAAa,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;AACpF,gBAAgB,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAK/D,gBAAgB,IAAI,CAAC,WAAW,CAAC,sBAAsB;AACvD,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;AACtD,oBAAoB,YAAY,GAAG,SAAS,CAAC,UAAU,IAAI,WAAW,CAAC,oBAAoB,CAAC;AAC5F,oBAAoB,WAAW,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;AACjF,oBAAoB,IAAI,WAAW,EAAE;AACrC,wBAAwB,OAAO,GAAG,WAAW,CAAC,SAAS,GAAG,SAAS,GAAG,gBAAgB,CAAC,CAAC;AACxF,wBAAwB,IAAI,OAAO,EAAE;AACrC,4BAA4B,aAAa,GAAG,OAAO,CAAC,OAAO;AAG3D,4BAA4BwD,eAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE;AACrE,gCAAgC,SAAS;AACzC,gCAAgCC,kCAAwB,CAAC,KAAK,EAAE,SAAS,CAAC;AAC1E,gCAAgC,WAAW,CAAC,OAAO;AACnD,gCAAgC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE;AACtF,6BAA6B,CAAC,CAAC,CAAC;AAChC,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,CAAC,GAAa,aAAa,CAAC,IAAI,CAAC,UAAU,MAAM,EAAE;AAC3E,wBAAwB,IAAI,EAAE,EAAE,EAAE,CAAC;AACnC,wBAAwB,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,aAAa,CAAC,EAAE;AAG1E,wBAAwB,IAAI,KAAK,CAAC,UAAU,EAAE;AAC9C,4BAA4B,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC1E,gCAAgC,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,SAAS,EAAE;AAC9F,oCAAoC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAC/E,wCAAwC,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;AACzG,4CAA4C,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AACpG,yCAAyC;AACzC,qCAAqC,CAAC,CAAC;AACvC,iCAAiC;AACjC,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AAEzB,wBAAwB,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AACjD,4BAA4B,OAAO,MAAM,CAAC;AAC1C,yBAAyB;AAGzB,wBAAwB,IAAI,MAAM,IAAI,IAAI,EAAE;AAE5C,4BAA4B,OAAO,MAAM,CAAC;AAC1C,yBAAyB;AACzB,wBAAwB,IAAI,aAAa,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACnN,wBAAwB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACnD,4BAA4B,OAAO,KAAK,CAAC,uBAAuB,CAAC,KAAK,EAAE,uBAAuB,IAAI,aAAa,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AACvI,yBAAyB;AAEzB,wBAAwB,IAAI,KAAK,CAAC,YAAY,EAAE;AAChD,4BAA4B,OAAO,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,EAAE,uBAAuB,IAAI,aAAa,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AAChJ,yBAAyB;AACzB,qBAAqB,CAAC,CAAC,CAAC;AACxB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,KAAK,EAAE,uBAAuB,EAAE,MAAM,EAAE,WAAW,EAAE;AAClH,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;AACtD,YAAY,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/B,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AAEb,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACrC,gBAAgB,OAAO,KAAK,CAAC,uBAAuB,CAAC,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AACxG,aAAa;AAEb,YAAY,IAAI,KAAK,CAAC,YAAY,EAAE;AACpC,gBAAgB,OAAO,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,EAAE,uBAAuB,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AACjH,aAAa;AACb,SAAS,CAAC,CAAC,CAAC;AACZ,KAAK,CAAC;AAIN,IAAI,UAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,cAAc,EAAE,WAAW,EAAE;AAC7F,QAAQ,IAAI,eAAe,GAAG,UAAU,IAAI,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;AAC/E,QAAQ,IAAI,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;AACrE,QAAQ,SAAS,mBAAmB,CAAC,cAAc,EAAE;AACrD,YAAY,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AAC/D,gBAAgB,IAAI,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1C,gBAAgB,wBAAwB,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;AACxE,gBAAgBN,aAAK,CAAC,cAAc,EAAE;AACtC,oBAAoB,SAAS,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;AACtE,wBAAwB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;AAC1D,4BAA4B,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAC9D,gCAAgC,IAAI,eAAe,CAAC,IAAI,CAAC,IAAIO,uBAAe,CAAC,IAAI,CAAC,EAAE;AACpF,oCAAoC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxD,iCAAiC;AACjC,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,cAAc,EAAE,UAAU,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;AAC7E,wBAAwB,IAAI,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtE,wBAAwBrD,iBAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnE,wBAAwB,IAAI,kBAAkB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AAC/E,wBAAwB,IAAI,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE;AAGzD,4BAA4B,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAC9D,gCAAgC,IAAI,eAAe,CAAC,IAAI,CAAC,IAAIqD,uBAAe,CAAC,IAAI,CAAC,EAAE;AACpF,oCAAoC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxD,iCAAiC;AACjC,6BAA6B,CAAC,CAAC;AAC/B,4BAA4B,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClD,4BAA4B,kBAAkB,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC5E,gCAAgC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACzD,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,OAAO,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAChE,SAAS;AACT,QAAQ,OAAO,mBAAmB,CAAC,cAAc,CAAC,CAAC;AACnD,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,EAAE,CAAC;;AC/VJ,IAAI,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAuB9C,IAAI,UAAU,GAAGnE,cAAQ,CAAC,EAAE,EAAEoE,cAAM,CAAC,eAAe,CAAC,CAAC;;ACvB7D,IAAI,YAAY,GAAG,EAAE,CAAC;AAQf,IAAI,8BAA8B,GAAG,UAAU,CAAC,OAAO,KAAK,KAAK;AACxE,IAAI,+BAA+B;AACnC,MAAM,SAAS,CAAC;AAehB,SAAS,oBAAoB,GAAG;AAEhC,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,kBAAkB,EAAE,IAAI;AAChC,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,yBAAyB,EAAE,IAAI;AACvC,QAAQ,8BAA8B,EAAE,IAAI;AAC5C,QAAQ,yCAAyC,EAAE,IAAI;AACvD,QAAQ,4BAA4B,EAAE,IAAI;AAC1C,QAAQ,yBAAyB,EAAE,IAAI;AACvC,QAAQ,sCAAsC,EAAE,IAAI;AACpD,QAAQ,8BAA8B,EAAE,IAAI;AAC5C,QAAQ,oDAAoD,EAAE,IAAI;AAClE,QAAQ,mCAAmC,EAAE,IAAI;AACjD,QAAQ,mCAAmC,EAAE,KAAK;AAClD,QAAQ,uCAAuC,EAAE,KAAK;AACtD,KAAK,CAAC;AACN,IAAI,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE;AACzE,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,OAAO;AACf,YAAY,CAAC;AACb,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AAC9B,SAAS,CAAC;AACV,KAAK,CAAC,CAAC,CAAC;AACR,CAAC;AACD,SAAS,+BAA+B,GAAG;AAC3C,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,IAAI,IAAI,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC;AACvC,QAAQ,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC9D,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,oBAAoB,EAAE;AACtC,QAAQ,KAAK,EAAEpE,cAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,YAAY,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,kBAAkB,EAAE,CAAC,EAAE,GAAG,YAAY,CAAC,kBAAkB,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE;AACvX,gBAAgB,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI;AAC5E,gBAAgB,kBAAkB,EAAE,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,iBAAiB,CAAC;AACzF,aAAa,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,kBAAkB,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9G,KAAK,CAAC;AACN,CAAC;AAoBD,SAAS,SAAS,CAAC,CAAC,EAAE;AACtB,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,UAAU,IAAI,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,qBAAqB,CAAC,CAAC,EAAE;AAClC,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC;AAC7C,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AACzB,CAAC;AACD,SAAS,aAAa,CAAC,SAAS,EAAE;AAClC,IAAI,OAAO,oBAAoB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AAChG,CAAC;AACD,SAAS,oBAAoB,CAAC,SAAS,EAAE;AACzC,IAAI,OAAO,SAAS;AACpB,QAAQD,mBAAa,CAACA,mBAAa,CAAC;AACpC,YAAY,qBAAqB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;AACjH,SAAS,EAAE,oBAAoB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,oBAAoB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AACxO,UAAU,EAAE,CAAC;AACb,CAAC;AACD,SAAS,QAAQ,CAAC,IAAI,EAAE;AACxB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,IAAI;AACf,QAAQA,mBAAa,CAACA,mBAAa,CAAC;AACpC,YAAY,CAAC,EAAE,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,kBAAkB,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;AACzI,SAAS,EAAE,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AAC5K,UAAU,EAAE,CAAC;AACb;;ACvGA,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAc9B,IAAC,YAAY,KAAkB,YAAY;AA2B9C,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;AACnC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;AACtC,QAAQ,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;AACtC,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAC5B,YAAY,MAAMiC,yBAAiB,CAAC,EAAE,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,EAAE,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;AAI3S,QAAQ,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,EAAE,EAAE,GAAG,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,cAAc,GAAG,OAAO,CAAC,cAAc,EAAE,cAAc,GAAG,OAAO,CAAC,cAAc,EAAE,EAAE,GAAG,OAAO,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,sBAAsB,GAAG,EAAE,EAAE,SAAS,GAAG,OAAO,CAAC,SAAS,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,eAAe,GAAG,OAAO,CAAC,eAAe,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,sBAAsB,GAAG,OAAO,CAAC,OAAO,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAC/jB,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAChC,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY,IAAI;AAChB,gBAAgB,GAAG,GAAG,IAAIqC,aAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,GAAGC,eAAU,CAAC,KAAK,EAAE,CAAC;AAClH,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,CAAC,qBAAqB,GAAG,OAAO,IAAI,kBAAkB,GAAG,CAAC,CAAC;AACvE,QAAQ,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AACrD,QAAQ,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,cAAc,GAAGtE,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,iBAAiB,EAAE,CAAC,CAAC;AAC5M,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,SAAS,EAAE;AACvD,YAAY,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC;AACvE,SAAS;AACT,QAAQ,IAAI,kBAAkB,EAAE;AAChC,YAAY,UAAU,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,qBAAqB,GAAG,KAAK,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAC;AAC1G,SAAS;AACT,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3D,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,QAAQ,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjF,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC;AACzC,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,MAAM,EAAE,IAAI;AACxB,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,eAAe,EAAE,eAAe;AAC5C,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC;AAC7C,YAAY,KAAK,EAAE,IAAI,CAAC,KAAK;AAC7B,YAAY,IAAI,EAAE,IAAI,CAAC,IAAI;AAC3B,YAAY,cAAc,EAAE,IAAI,CAAC,cAAc;AAC/C,YAAY,cAAc,EAAE,cAAc;AAC1C,YAAY,iBAAiB,EAAE,iBAAiB;AAChD,YAAY,kBAAkB,EAAE,kBAAkB;AAClD,YAAY,OAAO,EAAE,OAAO;AAC5B,YAAY,WAAW,EAAE,CAAC,CAAC,WAAW;AACtC,YAAY,eAAe,EAAE;AAC7B,gBAAgB,IAAI,EAAE,mBAAmB;AACzC,gBAAgB,OAAO,EAAE,sBAAsB;AAC/C,aAAa;AACb,YAAY,UAAU,EAAE,IAAI,CAAC,UAAU;AACvC,YAAY,sBAAsB,EAAE,sBAAsB;AAC1D,YAAY,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO;AACpD,gBAAgB,YAAY;AAC5B,oBAAoB,IAAI,KAAK,CAAC,cAAc,EAAE;AAC9C,wBAAwB,KAAK,CAAC,cAAc,CAAC;AAC7C,4BAA4B,MAAM,EAAE,EAAE;AACtC,4BAA4B,KAAK,EAAE;AACnC,gCAAgC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE;AAC3E,gCAAgC,SAAS,EAAE,KAAK,CAAC,YAAY,CAAC,aAAa,IAAI,EAAE;AACjF,6BAA6B;AAC7B,4BAA4B,yBAAyB,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAChF,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB,iBAAiB;AACjB,kBAAkB,KAAK,CAAC;AACxB,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO;AACvC,YAAY,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACrC,KAAK;AACL,IAAI,YAAY,CAAC,SAAS,CAAC,iBAAiB,GAAG,YAAY;AAC3D,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC3C,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,kBAAkB,GAAG,MAAM,CAAC;AACxC,QAAQ,IAAI,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC3D,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC;AAC3C,YAAY,kBAAkB,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACjE,QAAQ,kBAAkB,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAIpD,QAAQ,IAAI,CAAC,oBAAoB,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AACnE,YAAY,oBAAoB,GAAG,IAAI,CAAC;AACxC,YAAY,IAAI,MAAM,CAAC,QAAQ;AAC/B,gBAAgB,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI;AAC1C,gBAAgB,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACnE,gBAAgB,UAAU,CAAC,YAAY;AACvC,oBAAoB,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE;AACjE,wBAAwB,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;AACnD,wBAAwB,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC;AACtD,wBAAwB,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC;AACzC,wBAAwB,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;AACpD,4BAA4B,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;AAC5D,gCAAgC,GAAG;AACnC,oCAAoC,4CAA4C;AAChF,wCAAwC,4DAA4D,CAAC;AACrG,6BAA6B;AAC7B,iCAAiC,IAAI,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;AAClE,gCAAgC,GAAG;AACnC,oCAAoC,wEAAwE,CAAC;AAC7G,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB,IAAI,GAAG,EAAE;AACjC,4BAA4B,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIc,iBAAS,CAAC,GAAG,CAAC,wDAAwD;AAClI,gCAAgC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AACvD,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAC1B,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,mBAAmB,EAAE;AAMvE,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;AACvD,SAAS;AACT,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AAKP,IAAI,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AAC9C,QAAQ,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AACjC,KAAK,CAAC;AAoBN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,OAAO,EAAE;AAC3D,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;AAC5C,YAAY,OAAO,GAAGyD,sBAAY,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC5E,SAAS;AAET,QAAQ,IAAI,IAAI,CAAC,qBAAqB;AACtC,aAAa,OAAO,CAAC,WAAW,KAAK,cAAc;AACnD,gBAAgB,OAAO,CAAC,WAAW,KAAK,mBAAmB,CAAC,EAAE;AAC9D,YAAY,OAAO,GAAGvE,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,CAAC;AACtF,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACrD,KAAK,CAAC;AAUN,IAAI,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACtD,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AACvC,YAAY,OAAO,GAAGuE,sBAAY,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvE,SAAS;AACT,QAAQzD,iBAAS,CAAC,OAAO,CAAC,WAAW,KAAK,mBAAmB,EAAE,EAAE,CAAC,CAAC;AACnE,QAAQ,IAAI,IAAI,CAAC,qBAAqB,IAAI,OAAO,CAAC,WAAW,KAAK,cAAc,EAAE;AAClF,YAAY,OAAO,GAAGd,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,CAAC;AACtF,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAChD,KAAK,CAAC;AASN,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;AACxC,YAAY,OAAO,GAAGuE,sBAAY,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACxE,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjD,KAAK,CAAC;AAKN,IAAI,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,OAAO,EAAE;AAC1D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;AACrD,QAAQ,OAAO,IAAI,CAAC,YAAY;AAChC,aAAa,wBAAwB,CAAC,OAAO,CAAC;AAC9C,aAAa,GAAG,CAAC,UAAU,MAAM,EAAE,EAAE,QAAQvE,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC;AACrH,gBAAgB,QAAQ,EAAE,OAAO,CAAC,KAAK;AACvC,gBAAgB,IAAI,EAAE,MAAM,CAAC,IAAI;AACjC,gBAAgB,WAAW,EAAE,OAAO,CAAC,WAAW;AAChD,gBAAgB,EAAE,EAAE,EAAE;AACtB,aAAa,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AACvB,KAAK,CAAC;AAUN,IAAI,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,OAAO,EAAE,UAAU,EAAE;AACtE,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE;AAC1D,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACzD,KAAK,CAAC;AAiBN,IAAI,YAAY,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,OAAO,EAAE;AAC9D,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC;AAC9J,KAAK,CAAC;AAeN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,OAAO,EAAE,UAAU,EAAE;AACzE,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE;AAC1D,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC5D,KAAK,CAAC;AAMN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,OAAO,EAAE;AAC3D,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACjD,QAAQ,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;AACzC,YAAY,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AACjD,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AAYN,IAAI,YAAY,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,OAAO,EAAE;AAC9D,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACpD,QAAQ,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;AACzC,YAAY,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AACjD,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,EAAE,EAAE;AACnE,QAAQ,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,OAAO,EAAE;AAC7D,QAAQ,OAAOsD,YAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3C,KAAK,CAAC;AAiBN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AACpD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,OAAO,CAAC,OAAO,EAAE;AAChC,aAAa,IAAI,CAAC,YAAY;AAC9B,YAAY,OAAO,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC;AACjD,gBAAgB,cAAc,EAAE,KAAK;AACrC,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,aAAa,IAAI,CAAC,YAAY,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACrH,aAAa,IAAI,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,wBAAwB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5E,KAAK,CAAC;AAKN,IAAI,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AACpD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,OAAO,CAAC,OAAO,EAAE;AAChC,aAAa,IAAI,CAAC,YAAY;AAC9B,YAAY,OAAO,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC;AACjD,gBAAgB,cAAc,EAAE,IAAI;AACpC,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,aAAa,IAAI,CAAC,YAAY,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACtH,KAAK,CAAC;AAMN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,EAAE,EAAE;AACxD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1C,QAAQ,OAAO,YAAY;AAC3B,YAAY,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5G,SAAS,CAAC;AACV,KAAK,CAAC;AAMN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,EAAE,EAAE;AACxD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1C,QAAQ,OAAO,YAAY;AAC3B,YAAY,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5G,SAAS,CAAC;AACV,KAAK,CAAC;AAaN,IAAI,YAAY,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,cAAc,EAAE;AAChF,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;AAC1E,KAAK,CAAC;AAYN,IAAI,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,OAAO,EAAE;AAC/D,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC5D,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC;AACzB,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC;AACzB,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,QAAQ,EAAE;AAChD,YAAY,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnC,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAG1C,QAAQ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,QAAQ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAIjC,QAAQ,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE;AACtC,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIxC,iBAAS,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACvE,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AAYN,IAAI,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,OAAO,EAAE;AACrE,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,QAAQ,CAAC,EAAE;AACvD,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAC/D,KAAK,CAAC;AAIN,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,UAAU,EAAE;AAC3D,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC9C,KAAK,CAAC;AAQN,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,eAAe,EAAE;AAChE,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AACnD,KAAK,CAAC;AAIN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;AAC/D,QAAQ,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAChD,KAAK,CAAC;AAIN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,EAAE;AAC/D,QAAQ,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAChD,KAAK,CAAC;AAIN,IAAI,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AACtD,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;AAC9C,KAAK,CAAC;AAIN,IAAI,YAAY,CAAC,SAAS,CAAC,4BAA4B,GAAG,UAAU,eAAe,EAAE;AACrF,QAAQ,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;AAC5D,KAAK,CAAC;AAIN,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,OAAO,EAAE;AACxD,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;AACrD,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,gBAAgB,EAAE;AACpE,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;AACpD,SAAS;AACT,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,YAAY,CAAC;AACxB,CAAC,EAAE,EAAE;AAEL,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,IAAI,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,8BAA8B,CAAC;AAC/E;;ACxgBA0D,wBAAY,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}