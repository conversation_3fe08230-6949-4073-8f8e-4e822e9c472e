{"version": 3, "file": "mockQueryManager.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockQueryManager.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAE7D,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AAEzD,MAAM,CAAC,IAAM,qCAAqC,GAAG,UACnD,OACsC,IACnC,OAAA,YACH,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EACnC,iBAAiB,EAAE,SAAS,EAC5B,kBAAkB,EAAE,KAAK,EACzB,WAAW,EAAE,SAAS,EACtB,OAAO,EAAE,KAAK,EACd,eAAe,EAAE,EAAE,EACnB,UAAU,EAAE,IAAI,UAAU,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EACpD,sBAAsB,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAC9D,cAAc,EAAE,SAAS,EACzB,WAAW,EAAE,KAAK,IACf,OAAO,EACV,EAZG,CAYH,CAAC;AAEH,sEAAsE;AACtE,6DAA6D;AAC7D,gBAAe;IAAC,yBAAoC;SAApC,UAAoC,EAApC,qBAAoC,EAApC,IAAoC;QAApC,oCAAoC;;IAClD,OAAO,IAAI,YAAY,CACrB,qCAAqC,CAAC;QACpC,IAAI,EAAE,cAAc,eAAI,eAAe,CAAC;QACxC,KAAK,EAAE,IAAI,aAAa,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;KACjD,CAAC,CACH,CAAC;AACJ,CAAC,EAAC", "sourcesContent": ["import type { QueryManagerOptions } from \"../../../core/QueryManager.js\";\nimport { QueryManager } from \"../../../core/QueryManager.js\";\nimport type { MockedResponse } from \"./mockLink.js\";\nimport { mockSingleLink } from \"./mockLink.js\";\nimport { InMemoryCache } from \"../../../cache/index.js\";\nimport { LocalState } from \"../../../core/LocalState.js\";\n\nexport const getDefaultOptionsForQueryManagerTests = <TStore>(\n  options: Pick<QueryManagerOptions<TStore>, \"cache\" | \"link\"> &\n    Partial<QueryManagerOptions<TStore>>\n) => ({\n  defaultOptions: Object.create(null),\n  documentTransform: undefined,\n  queryDeduplication: false,\n  onBroadcast: undefined,\n  ssrMode: false,\n  clientAwareness: {},\n  localState: new LocalState({ cache: options.cache }),\n  assumeImmutableResults: !!options.cache.assumeImmutableResults,\n  defaultContext: undefined,\n  dataMasking: false,\n  ...options,\n});\n\n// Helper method for the tests that construct a query manager out of a\n// a list of mocked responses for a mocked network interface.\nexport default (...mockedResponses: MockedResponse[]) => {\n  return new QueryManager(\n    getDefaultOptionsForQueryManagerTests({\n      link: mockSingleLink(...mockedResponses),\n      cache: new InMemoryCache({ addTypename: false }),\n    })\n  );\n};\n"]}