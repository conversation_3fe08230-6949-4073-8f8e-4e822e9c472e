{"version": 3, "file": "mockWatchQuery.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockWatchQuery.ts"], "names": [], "mappings": "AACA,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAGrD,gBAAe;IAAC,yBAAoC;SAApC,UAAoC,EAApC,qBAAoC,EAApC,IAAoC;QAApC,oCAAoC;;IAClD,IAAM,YAAY,GAAG,gBAAgB,eAAI,eAAe,CAAC,CAAC;IAC1D,IAAM,YAAY,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAChD,OAAO,YAAY,CAAC,UAAU,CAAC;QAC7B,KAAK,EAAE,YAAY,CAAC,KAAM;QAC1B,SAAS,EAAE,YAAY,CAAC,SAAS;QACjC,2BAA2B,EAAE,KAAK,EAAE,oEAAoE;KACzG,CAAC,CAAC;AACL,CAAC,EAAC", "sourcesContent": ["import type { MockedResponse } from \"./mockLink.js\";\nimport mockQueryManager from \"./mockQueryManager.js\";\nimport type { ObservableQuery } from \"../../../core/index.js\";\n\nexport default (...mockedResponses: MockedResponse[]): ObservableQuery<any> => {\n  const queryManager = mockQueryManager(...mockedResponses);\n  const firstRequest = mockedResponses[0].request;\n  return queryManager.watchQuery({\n    query: firstRequest.query!,\n    variables: firstRequest.variables,\n    notifyOnNetworkStatusChange: false, // XXX might not always be the right option. Set for legacy reasons.\n  });\n};\n"]}