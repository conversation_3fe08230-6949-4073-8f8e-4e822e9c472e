{"name": "ts-invariant", "version": "0.10.3", "author": "<PERSON> <<EMAIL>>", "description": "TypeScript implementation of invariant(condition, message)", "license": "MIT", "type": "module", "main": "lib/invariant.cjs", "module": "lib/invariant.js", "types": "lib/invariant.d.ts", "keywords": ["invariant", "assertion", "precondition", "TypeScript"], "homepage": "https://github.com/apollographql/invariant-packages", "repository": {"type": "git", "url": "git+https://github.com/apollographql/invariant-packages.git"}, "bugs": {"url": "https://github.com/apollographql/invariant-packages/issues"}, "scripts": {"build": "npm run tsc && npm run rollup", "tsc": "../../node_modules/.bin/tsc", "rollup": "../../node_modules/.bin/rollup -c", "prepare": "npm run build", "mocha": "npm run mocha:esm && npm run mocha:cjs", "mocha:esm": "../../node_modules/.bin/mocha --reporter spec --full-trace lib/tests.js", "mocha:cjs": "../../node_modules/.bin/mocha --reporter spec --full-trace lib/tests.bundle.cjs", "test": "npm run build && npm run mocha && npm run test:no-node", "test:no-node": "! grep -i node lib/invariant.d.ts"}, "dependencies": {"tslib": "^2.1.0"}, "devDependencies": {"@types/invariant": "^2.2.29", "invariant": "^2.2.4"}, "engines": {"node": ">=8"}, "gitHead": "c8a2d6d2e839e9bdc595ae5fd2a50331b2b01014"}