{"version": 3, "file": "invariant.js", "sourceRoot": "", "sources": ["../src/invariant.ts"], "names": [], "mappings": ";AAAA,IAAM,cAAc,GAAG,qBAAqB,CAAC;AAE3C,IAAA,KAIE,MAAa,eADd,EAHD,cAAc,mBAAG,UAAU,GAAQ,EAAE,KAAU;IAC7C,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;IACtB,OAAO,GAAG,CAAC;AACb,CAAC,KAAA,CACe;AAElB;IAAoC,kCAAK;IAGvC,wBAAY,OAAyC;QAAzC,wBAAA,EAAA,wBAAyC;QAArD,YACE,kBACE,OAAO,OAAO,KAAK,QAAQ;YACzB,CAAC,CAAI,cAAc,UAAK,OAAO,+DAA4D;YAC3F,CAAC,CAAC,OAAO,CACZ,SAEF;QATD,iBAAW,GAAG,CAAC,CAAC;QAChB,UAAI,GAAG,cAAc,CAAC;QAOpB,cAAc,CAAC,KAAI,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;;IACjD,CAAC;IACH,qBAAC;AAAD,CAAC,AAXD,CAAoC,KAAK,GAWxC;;AAED,MAAM,UAAU,SAAS,CACvB,SAAc,EACd,OAAyB;IAEzB,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;KACnC;AACH,CAAC;AAED,IAAM,eAAe,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAU,CAAC;AAG7E,IAAI,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAEpD,SAAS,iBAAiB,CAA8B,IAAO;IAC7D,OAAO;QACL,IAAI,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,cAAc,EAAE;YACnD,yEAAyE;YACzE,qCAAqC;YACrC,IAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC;YAC5C,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,SAAgB,CAAC,CAAC;SAChD;IACH,CAAwB,CAAC;AAC3B,CAAC;AAED,WAAiB,SAAS;IACX,eAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IACnC,aAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAC/B,cAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACjC,eAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAClD,CAAC,EALgB,SAAS,KAAT,SAAS,QAKzB;AAED,MAAM,UAAU,YAAY,CAAC,KAAqB;IAChD,IAAM,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;IAC5C,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,OAAO,GAAG,CAAC;AACb,CAAC;AAED,eAAe,SAAS,CAAC", "sourcesContent": ["const genericMessage = \"Invariant Violation\";\nconst {\n  setPrototypeOf = function (obj: any, proto: any) {\n    obj.__proto__ = proto;\n    return obj;\n  },\n} = Object as any;\n\nexport class InvariantError extends Error {\n  framesToPop = 1;\n  name = genericMessage;\n  constructor(message: string | number = genericMessage) {\n    super(\n      typeof message === \"number\"\n        ? `${genericMessage}: ${message} (see https://github.com/apollographql/invariant-packages)`\n        : message\n    );\n    setPrototypeOf(this, InvariantError.prototype);\n  }\n}\n\nexport function invariant(\n  condition: any,\n  message?: string | number,\n): asserts condition {\n  if (!condition) {\n    throw new InvariantError(message);\n  }\n}\n\nconst verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"] as const;\nexport type VerbosityLevel = (typeof verbosityLevels)[number];\nexport type ConsoleMethodName = Exclude<VerbosityLevel, \"silent\">;\nlet verbosityLevel = verbosityLevels.indexOf(\"log\");\n\nfunction wrapConsoleMethod<M extends ConsoleMethodName>(name: M) {\n  return function () {\n    if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n      // Default to console.log if this host environment happens not to provide\n      // all the console.* methods we need.\n      const method = console[name] || console.log;\n      return method.apply(console, arguments as any);\n    }\n  } as (typeof console)[M];\n}\n\nexport namespace invariant {\n  export const debug = wrapConsoleMethod(\"debug\");\n  export const log = wrapConsoleMethod(\"log\");\n  export const warn = wrapConsoleMethod(\"warn\");\n  export const error = wrapConsoleMethod(\"error\");\n}\n\nexport function setVerbosity(level: VerbosityLevel): VerbosityLevel {\n  const old = verbosityLevels[verbosityLevel];\n  verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n  return old;\n}\n\nexport default invariant;\n"]}