{"version": 3, "file": "invariant.cjs", "sources": ["invariant.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nvar genericMessage = \"Invariant Violation\";\nvar _a = Object.setPrototypeOf, setPrototypeOf = _a === void 0 ? function (obj, proto) {\n    obj.__proto__ = proto;\n    return obj;\n} : _a;\nvar InvariantError = /** @class */ (function (_super) {\n    __extends(InvariantError, _super);\n    function InvariantError(message) {\n        if (message === void 0) { message = genericMessage; }\n        var _this = _super.call(this, typeof message === \"number\"\n            ? genericMessage + \": \" + message + \" (see https://github.com/apollographql/invariant-packages)\"\n            : message) || this;\n        _this.framesToPop = 1;\n        _this.name = genericMessage;\n        setPrototypeOf(_this, InvariantError.prototype);\n        return _this;\n    }\n    return InvariantError;\n}(Error));\nexport { InvariantError };\nexport function invariant(condition, message) {\n    if (!condition) {\n        throw new InvariantError(message);\n    }\n}\nvar verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"];\nvar verbosityLevel = verbosityLevels.indexOf(\"log\");\nfunction wrapConsoleMethod(name) {\n    return function () {\n        if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n            // Default to console.log if this host environment happens not to provide\n            // all the console.* methods we need.\n            var method = console[name] || console.log;\n            return method.apply(console, arguments);\n        }\n    };\n}\n(function (invariant) {\n    invariant.debug = wrapConsoleMethod(\"debug\");\n    invariant.log = wrapConsoleMethod(\"log\");\n    invariant.warn = wrapConsoleMethod(\"warn\");\n    invariant.error = wrapConsoleMethod(\"error\");\n})(invariant || (invariant = {}));\nexport function setVerbosity(level) {\n    var old = verbosityLevels[verbosityLevel];\n    verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n    return old;\n}\nexport default invariant;\n//# sourceMappingURL=invariant.js.map"], "names": ["__extends"], "mappings": ";;;;;;AACA,IAAI,cAAc,GAAG,qBAAqB,CAAC;AAC3C,IAAI,EAAE,GAAG,MAAM,CAAC,cAAc,EAAE,cAAc,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;AACvF,IAAI,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAI,OAAO,GAAG,CAAC;AACf,CAAC,GAAG,EAAE,CAAC;AACJ,IAAC,cAAc,kBAAkB,UAAU,MAAM,EAAE;AACtD,IAAIA,eAAS,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AACtC,IAAI,SAAS,cAAc,CAAC,OAAO,EAAE;AACrC,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,cAAc,CAAC,EAAE;AAC7D,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,OAAO,KAAK,QAAQ;AACjE,cAAc,cAAc,GAAG,IAAI,GAAG,OAAO,GAAG,4DAA4D;AAC5G,cAAc,OAAO,CAAC,IAAI,IAAI,CAAC;AAC/B,QAAQ,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;AAC9B,QAAQ,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC;AACpC,QAAQ,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;AACxD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC,CAAC,KAAK,CAAC,EAAE;AAEH,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,QAAQ,MAAM,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;AAC1C,KAAK;AACL,CAAC;AACD,IAAI,eAAe,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AAClE,IAAI,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpD,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACjC,IAAI,OAAO,YAAY;AACvB,QAAQ,IAAI,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,cAAc,EAAE;AAC7D;AACA;AACA,YAAY,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC;AACtD,YAAY,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACpD,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD,CAAC,UAAU,SAAS,EAAE;AACtB,IAAI,SAAS,CAAC,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACjD,IAAI,SAAS,CAAC,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAI,SAAS,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,SAAS,CAAC,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACjD,CAAC,EAAE,SAAS,KAAK,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3B,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,IAAI,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;AAC9C,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD,kBAAe,SAAS;;;;;;;"}