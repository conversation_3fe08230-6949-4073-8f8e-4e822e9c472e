{"version": 3, "file": "bundle.cjs", "sources": ["es5/context.js", "es5/helpers.js", "es5/entry.js", "es5/dep.js", "es5/index.js"], "sourcesContent": ["import { Slot } from \"@wry/context\";\nexport var parentEntrySlot = new Slot();\nexport function nonReactive(fn) {\n    return parentEntrySlot.withValue(void 0, fn);\n}\nexport { Slot };\nexport { bind as bindContext, noContext, setTimeout, asyncFromGen, } from \"@wry/context\";\n//# sourceMappingURL=context.js.map", "export var hasOwnProperty = Object.prototype.hasOwnProperty;\nexport var arrayFromSet = Array.from ||\n    function (set) {\n        var array = [];\n        set.forEach(function (item) { return array.push(item); });\n        return array;\n    };\nexport function maybeUnsubscribe(entryOrDep) {\n    var unsubscribe = entryOrDep.unsubscribe;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\n//# sourceMappingURL=helpers.js.map", "import { parentEntrySlot } from \"./context.js\";\nimport { maybeUnsubscribe, arrayFromSet } from \"./helpers.js\";\nvar emptySetPool = [];\nvar POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    var len = a.length;\n    return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n        // Both values must be ordinary (or both exceptional) to be equal.\n        len === b.length &&\n        // The underlying value or exception must be the same.\n        a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch (value.length) {\n        case 0: throw new Error(\"unknown value\");\n        case 1: return value[0];\n        case 2: throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nexport var Entry = /** @class */ (function () {\n    function Entry(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    Entry.prototype.peek = function () {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    };\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    Entry.prototype.recompute = function (args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this)\n            ? reallyRecompute(this, args)\n            : valueGet(this.value);\n    };\n    Entry.prototype.setDirty = function () {\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        maybeUnsubscribe(this);\n    };\n    Entry.prototype.dispose = function () {\n        var _this = this;\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, function (parent, child) {\n            parent.setDirty();\n            forgetChild(parent, _this);\n        });\n    };\n    Entry.prototype.forget = function () {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    };\n    Entry.prototype.dependOn = function (dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    };\n    Entry.prototype.forgetDeps = function () {\n        var _this = this;\n        if (this.deps) {\n            arrayFromSet(this.deps).forEach(function (dep) { return dep.delete(_this); });\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    };\n    Entry.count = 0;\n    return Entry;\n}());\nfunction rememberParent(child) {\n    var parent = parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        }\n        else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    var normalizeResult = entry.normalizeResult;\n    var oldValueCopy;\n    if (normalizeResult && entry.value.length === 1) {\n        oldValueCopy = valueCopy(entry.value);\n    }\n    // Make entry.value an empty array, representing an unknown value.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n        // If we have a viable oldValueCopy to compare with the (successfully\n        // recomputed) new entry.value, and they are not already === identical, give\n        // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n        // and/or entry.value[0] to determine the final cached entry.value.\n        if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n            try {\n                entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n            }\n            catch (_a) {\n                // If normalizeResult throws, just use the newer value, rather than\n                // saving the exception as entry.value[1].\n            }\n        }\n    }\n    catch (e) {\n        // If entry.fn throws, entry.value will hold that exception.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    var parentCount = child.parents.size;\n    if (parentCount) {\n        var parents = arrayFromSet(child.parents);\n        for (var i = 0; i < parentCount; ++i) {\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    var parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    }\n    else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    var childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    }\n    else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    var dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach(function (_value, child) {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            maybeUnsubscribe(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        }\n        catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\n//# sourceMappingURL=entry.js.map", "import { parentEntrySlot } from \"./context.js\";\nimport { hasOwnProperty, maybeUnsubscribe, arrayFromSet, } from \"./helpers.js\";\nvar EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true, // Fully remove parent Entry from LRU cache and computation graph\n};\nexport function dep(options) {\n    var depsByKey = new Map();\n    var subscribe = options && options.subscribe;\n    function depend(key) {\n        var parent = parentEntrySlot.getValue();\n        if (parent) {\n            var dep_1 = depsByKey.get(key);\n            if (!dep_1) {\n                depsByKey.set(key, dep_1 = new Set);\n            }\n            parent.dependOn(dep_1);\n            if (typeof subscribe === \"function\") {\n                maybeUnsubscribe(dep_1);\n                dep_1.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        var dep = depsByKey.get(key);\n        if (dep) {\n            var m_1 = (entryMethodName &&\n                hasOwnProperty.call(EntryMethods, entryMethodName)) ? entryMethodName : \"setDirty\";\n            // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n            // because modifying a Set while iterating over it can cause elements in\n            // the Set to be removed from the Set before they've been iterated over.\n            arrayFromSet(dep).forEach(function (entry) { return entry[m_1](); });\n            depsByKey.delete(key);\n            maybeUnsubscribe(dep);\n        }\n    };\n    return depend;\n}\n//# sourceMappingURL=dep.js.map", "import { <PERSON><PERSON> } from \"@wry/trie\";\nimport { StrongCache } from \"@wry/caches\";\nimport { Entry } from \"./entry.js\";\nimport { parentEntrySlot } from \"./context.js\";\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\nexport { bindContext, noContext, nonReactive, setTimeout, asyncFromGen, Slot, } from \"./context.js\";\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\nexport { dep } from \"./dep.js\";\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nvar defaultKeyTrie;\nexport function defaultMakeCacheKey() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var trie = defaultKeyTrie || (defaultKeyTrie = new Trie(typeof WeakMap === \"function\"));\n    return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\nexport { Trie as KeyTrie };\n;\nvar caches = new Set();\nexport function wrap(originalFunction, _a) {\n    var _b = _a === void 0 ? Object.create(null) : _a, _c = _b.max, max = _c === void 0 ? Math.pow(2, 16) : _c, keyArgs = _b.keyArgs, _d = _b.makeCacheKey, makeCacheKey = _d === void 0 ? defaultMakeCacheKey : _d, normalizeResult = _b.normalizeResult, subscribe = _b.subscribe, _e = _b.cache, cacheOption = _e === void 0 ? StrongCache : _e;\n    var cache = typeof cacheOption === \"function\"\n        ? new cacheOption(max, function (entry) { return entry.dispose(); })\n        : cacheOption;\n    var optimistic = function () {\n        var key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        var entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new Entry(originalFunction));\n            entry.normalizeResult = normalizeResult;\n            entry.subscribe = subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = function () { return cache.delete(key); };\n        }\n        var value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!parentEntrySlot.hasValue()) {\n            caches.forEach(function (cache) { return cache.clean(); });\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: function () { return cache.size; },\n        configurable: false,\n        enumerable: false,\n    });\n    Object.freeze(optimistic.options = {\n        max: max,\n        keyArgs: keyArgs,\n        makeCacheKey: makeCacheKey,\n        normalizeResult: normalizeResult,\n        subscribe: subscribe,\n        cache: cache,\n    });\n    function dirtyKey(key) {\n        var entry = key && cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        var entry = key && cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return key ? cache.delete(key) : false;\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\n//# sourceMappingURL=index.js.map"], "names": ["Slot", "trie", "<PERSON><PERSON>", "StrongCache"], "mappings": ";;;;;;AACO,IAAI,eAAe,GAAG,IAAIA,YAAI,EAAE,CAAC;AACjC,SAAS,WAAW,CAAC,EAAE,EAAE;AAChC,IAAI,OAAO,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;AACjD;;ACJO,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACrD,IAAI,YAAY,GAAG,KAAK,CAAC,IAAI;AACpC,IAAI,UAAU,GAAG,EAAE;AACnB,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC;AACvB,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAClE,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACC,SAAS,gBAAgB,CAAC,UAAU,EAAE;AAC7C,IAAI,IAAI,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;AAC7C,IAAI,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;AAC3C,QAAQ,UAAU,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;AACxC,QAAQ,WAAW,EAAE,CAAC;AACtB,KAAK;AACL;;ACXA,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,IAAI,gBAAgB,GAAG,GAAG,CAAC;AAC3B;AACA;AACA,SAAS,MAAM,CAAC,SAAS,EAAE,eAAe,EAAE;AAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,QAAQ,MAAM,IAAI,KAAK,CAAC,eAAe,IAAI,mBAAmB,CAAC,CAAC;AAChE,KAAK;AACL,CAAC;AACD,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;AACvB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC;AACvB,IAAI;AACJ;AACA,IAAI,GAAG,GAAG,CAAC;AACX;AACA,QAAQ,GAAG,KAAK,CAAC,CAAC,MAAM;AACxB;AACA,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACnC,CAAC;AACD,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,IAAI,QAAQ,KAAK,CAAC,MAAM;AACxB,QAAQ,KAAK,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AACjD,QAAQ,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC,QAAQ,KAAK,CAAC,EAAE,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC;AACM,IAAI,KAAK,kBAAkB,YAAY;AAC9C,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;AACvB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACrB,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AACjC,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AACrC;AACA;AACA;AACA,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,QAAQ,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACxB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AACtB,KAAK;AACL,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AACvC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC5D,YAAY,cAAc,CAAC,IAAI,CAAC,CAAC;AACjC,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,SAAS;AACT,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,IAAI,EAAE;AAChD,QAAQ,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;AACzD,QAAQ,cAAc,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC;AACjC,cAAc,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;AACzC,cAAc,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AAC3C,QAAQ,IAAI,IAAI,CAAC,KAAK;AACtB,YAAY,OAAO;AACnB,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,QAAQ,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B;AACA;AACA;AACA,QAAQ,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AAC1C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;AACxB;AACA;AACA;AACA,QAAQ,cAAc,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,UAAU,CAAC,IAAI,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE;AAClD,YAAY,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC9B,YAAY,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACvC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACzC;AACA;AACA;AACA,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE;AAC9C,QAAQ,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtB,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACxB,YAAY,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AACxD,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AAC7C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE;AACvB,YAAY,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1F,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AAC9B,YAAY,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,SAAS;AACT,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AACpB,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC,EAAE,CAAC,CAAC;AACL,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;AAC5C,IAAI,IAAI,MAAM,EAAE;AAChB,QAAQ,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC5C,YAAY,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC9C,SAAS;AACT,QAAQ,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACjC,YAAY,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,SAAS;AACT,aAAa;AACb,YAAY,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL,CAAC;AACD,SAAS,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE;AACtC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA,IAAI,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,iBAAiB,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACvE,IAAI,IAAI,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;AACrC;AACA;AACA,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AACD,SAAS,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE;AACxC,IAAI,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC;AAChD,IAAI,IAAI,YAAY,CAAC;AACrB,IAAI,IAAI,eAAe,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACrD,QAAQ,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9C,KAAK;AACL;AACA,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI;AACR;AACA,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA,QAAQ,IAAI,eAAe,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AACpF,YAAY,IAAI;AAChB,gBAAgB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAClF,aAAa;AACb,YAAY,OAAO,EAAE,EAAE;AACvB;AACA;AACA,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,OAAO,CAAC,EAAE;AACd;AACA,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK;AACL;AACA,IAAI,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;AAC9B,CAAC;AACD,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,IAAI,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9E,CAAC;AACD,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAC7B;AACA;AACA,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,IAAI,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AACxC,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,IAAI,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AACxC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE;AACrC,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AACzC,IAAI,IAAI,WAAW,EAAE;AACrB,QAAQ,IAAI,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE;AAC9C,YAAY,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACxC,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE;AACzC;AACA;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AAChC,IAAI,IAAI,cAAc,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;AAC/B,QAAQ,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AAC7D,KAAK;AACL,SAAS,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC9C;AACA;AACA;AACA,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC;AACA;AACA,IAAI,IAAI,cAAc,EAAE;AACxB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC;AAC5B,KAAK;AACL,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE;AACzC;AACA;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,IAAI,MAAM,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,IAAI,IAAI,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,QAAQ,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9D,KAAK;AACL,SAAS,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AAChD,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;AAC9B,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE;AACzC,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,aAAa,CAAC;AAClC,IAAI,IAAI,EAAE,EAAE;AACZ,QAAQ,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzB,QAAQ,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;AAC3B,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,gBAAgB,EAAE;AACxD,gBAAgB,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,aAAa;AACb,YAAY,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AACxC,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA;AACA,SAAS,cAAc,CAAC,MAAM,EAAE;AAChC,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE;AACrC,QAAQ,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,KAAK,EAAE;AAC5D,YAAY,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACvC,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA;AACA,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;AACxB;AACA;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,KAAK,IAAI,CAAC,CAAC;AAC1C,CAAC;AACD,SAAS,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE;AACpC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrC,IAAI,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE;AACrC,IAAI,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU,EAAE;AAC/C,QAAQ,IAAI;AACZ,YAAY,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACpC,YAAY,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAClE,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAClB;AACA;AACA;AACA;AACA,YAAY,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC7B,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB;;AC5SA,IAAI,YAAY,GAAG;AACnB,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,MAAM,EAAE,IAAI;AAChB,CAAC,CAAC;AACK,SAAS,GAAG,CAAC,OAAO,EAAE;AAC7B,IAAI,IAAI,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,SAAS,GAAG,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC;AACjD,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AACzB,QAAQ,IAAI,MAAM,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;AAChD,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,IAAI,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3C,YAAY,IAAI,CAAC,KAAK,EAAE;AACxB,gBAAgB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;AACpD,aAAa;AACb,YAAY,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnC,YAAY,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;AACjD,gBAAgB,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACxC,gBAAgB,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AACnD,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,MAAM,CAAC,KAAK,GAAG,SAAS,KAAK,CAAC,GAAG,EAAE,eAAe,EAAE;AACxD,QAAQ,IAAI,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,QAAQ,IAAI,GAAG,EAAE;AACjB,YAAY,IAAI,GAAG,GAAG,CAAC,eAAe;AACtC,gBAAgB,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,IAAI,eAAe,GAAG,UAAU,CAAC;AACnG;AACA;AACA;AACA,YAAY,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACjF,YAAY,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAClC,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAClC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,MAAM,CAAC;AAClB;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,CAAC;AACZ,SAAS,mBAAmB,GAAG;AACtC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAIC,MAAI,GAAG,cAAc,KAAK,cAAc,GAAG,IAAIC,SAAI,CAAC,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC;AAC5F,IAAI,OAAOD,MAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC;AAMD,IAAI,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;AAChB,SAAS,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE;AAC3C,IAAI,IAAI,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,YAAY,EAAE,YAAY,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,mBAAmB,GAAG,EAAE,EAAE,eAAe,GAAG,EAAE,CAAC,eAAe,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAGE,oBAAW,GAAG,EAAE,CAAC;AACnV,IAAI,IAAI,KAAK,GAAG,OAAO,WAAW,KAAK,UAAU;AACjD,UAAU,IAAI,WAAW,CAAC,GAAG,EAAE,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;AAC5E,UAAU,WAAW,CAAC;AACtB,IAAI,IAAI,UAAU,GAAG,YAAY;AACjC,QAAQ,IAAI,GAAG,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AACjG,QAAQ,IAAI,GAAG,KAAK,KAAK,CAAC,EAAE;AAC5B,YAAY,OAAO,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACnC,QAAQ,IAAI,CAAC,KAAK,EAAE;AACpB,YAAY,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAChE,YAAY,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;AACpD,YAAY,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AACxC;AACA;AACA,YAAY,KAAK,CAAC,MAAM,GAAG,YAAY,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AACrE,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3E;AACA;AACA,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9B,QAAQ,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA;AACA;AACA,QAAQ,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE;AACzC,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AACvE,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC;AAC3B,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE;AAC9C,QAAQ,GAAG,EAAE,YAAY,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;AAC/C,QAAQ,YAAY,EAAE,KAAK;AAC3B,QAAQ,UAAU,EAAE,KAAK;AACzB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,GAAG;AACvC,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,YAAY,EAAE,YAAY;AAClC,QAAQ,eAAe,EAAE,eAAe;AACxC,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,KAAK,EAAE,KAAK;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC3B,QAAQ,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,QAAQ,IAAI,KAAK,EAAE;AACnB,YAAY,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC7B,SAAS;AACT,KAAK;AACL,IAAI,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnC,IAAI,UAAU,CAAC,KAAK,GAAG,SAAS,KAAK,GAAG;AACxC,QAAQ,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACtD,KAAK,CAAC;AACN,IAAI,SAAS,OAAO,CAAC,GAAG,EAAE;AAC1B,QAAQ,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,QAAQ,IAAI,KAAK,EAAE;AACnB,YAAY,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;AAChC,SAAS;AACT,KAAK;AACL,IAAI,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,IAAI,UAAU,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;AACtC,QAAQ,OAAO,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AAC5D,KAAK,CAAC;AACN,IAAI,SAAS,SAAS,CAAC,GAAG,EAAE;AAC5B,QAAQ,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC/C,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;AACrC,IAAI,UAAU,CAAC,MAAM,GAAG,SAAS,MAAM,GAAG;AAC1C,QAAQ,OAAO,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AAC9D,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;AAC3C,IAAI,UAAU,CAAC,MAAM,GAAG,OAAO,GAAG,SAAS,MAAM,GAAG;AACpD,QAAQ,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACxE,KAAK,GAAG,YAAY,CAAC;AACrB,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}