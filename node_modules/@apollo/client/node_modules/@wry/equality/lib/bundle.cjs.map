{"version": 3, "file": "bundle.cjs", "sources": ["index.js"], "sourcesContent": ["const { toString, hasOwnProperty } = Object.prototype;\nconst fnToStr = Function.prototype.toString;\nconst previousComparisons = new Map();\n/**\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\n */\nexport function equal(a, b) {\n    try {\n        return check(a, b);\n    }\n    finally {\n        previousComparisons.clear();\n    }\n}\n// Allow default imports as well.\nexport default equal;\nfunction check(a, b) {\n    // If the two values are strictly equal, our job is easy.\n    if (a === b) {\n        return true;\n    }\n    // Object.prototype.toString returns a representation of the runtime type of\n    // the given value that is considerably more precise than typeof.\n    const aTag = toString.call(a);\n    const bTag = toString.call(b);\n    // If the runtime types of a and b are different, they could maybe be equal\n    // under some interpretation of equality, but for simplicity and performance\n    // we just return false instead.\n    if (aTag !== bTag) {\n        return false;\n    }\n    switch (aTag) {\n        case '[object Array]':\n            // Arrays are a lot like other objects, but we can cheaply compare their\n            // lengths as a short-cut before comparing their elements.\n            if (a.length !== b.length)\n                return false;\n        // Fall through to object case...\n        case '[object Object]': {\n            if (previouslyCompared(a, b))\n                return true;\n            const aKeys = definedKeys(a);\n            const bKeys = definedKeys(b);\n            // If `a` and `b` have a different number of enumerable keys, they\n            // must be different.\n            const keyCount = aKeys.length;\n            if (keyCount !== bKeys.length)\n                return false;\n            // Now make sure they have the same keys.\n            for (let k = 0; k < keyCount; ++k) {\n                if (!hasOwnProperty.call(b, aKeys[k])) {\n                    return false;\n                }\n            }\n            // Finally, check deep equality of all child properties.\n            for (let k = 0; k < keyCount; ++k) {\n                const key = aKeys[k];\n                if (!check(a[key], b[key])) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Error]':\n            return a.name === b.name && a.message === b.message;\n        case '[object Number]':\n            // Handle NaN, which is !== itself.\n            if (a !== a)\n                return b !== b;\n        // Fall through to shared +a === +b case...\n        case '[object Boolean]':\n        case '[object Date]':\n            return +a === +b;\n        case '[object RegExp]':\n        case '[object String]':\n            return a == `${b}`;\n        case '[object Map]':\n        case '[object Set]': {\n            if (a.size !== b.size)\n                return false;\n            if (previouslyCompared(a, b))\n                return true;\n            const aIterator = a.entries();\n            const isMap = aTag === '[object Map]';\n            while (true) {\n                const info = aIterator.next();\n                if (info.done)\n                    break;\n                // If a instanceof Set, aValue === aKey.\n                const [aKey, aValue] = info.value;\n                // So this works the same way for both Set and Map.\n                if (!b.has(aKey)) {\n                    return false;\n                }\n                // However, we care about deep equality of values only when dealing\n                // with Map structures.\n                if (isMap && !check(aValue, b.get(aKey))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Uint16Array]':\n        case '[object Uint8Array]': // Buffer, in Node.js.\n        case '[object Uint32Array]':\n        case '[object Int32Array]':\n        case '[object Int8Array]':\n        case '[object Int16Array]':\n        case '[object ArrayBuffer]':\n            // DataView doesn't need these conversions, but the equality check is\n            // otherwise the same.\n            a = new Uint8Array(a);\n            b = new Uint8Array(b);\n        // Fall through...\n        case '[object DataView]': {\n            let len = a.byteLength;\n            if (len === b.byteLength) {\n                while (len-- && a[len] === b[len]) {\n                    // Keep looping as long as the bytes are equal.\n                }\n            }\n            return len === -1;\n        }\n        case '[object AsyncFunction]':\n        case '[object GeneratorFunction]':\n        case '[object AsyncGeneratorFunction]':\n        case '[object Function]': {\n            const aCode = fnToStr.call(a);\n            if (aCode !== fnToStr.call(b)) {\n                return false;\n            }\n            // We consider non-native functions equal if they have the same code\n            // (native functions require === because their code is censored).\n            // Note that this behavior is not entirely sound, since !== function\n            // objects with the same code can behave differently depending on\n            // their closure scope. However, any function can behave differently\n            // depending on the values of its input arguments (including this)\n            // and its calling context (including its closure scope), even\n            // though the function object is === to itself; and it is entirely\n            // possible for functions that are not === to behave exactly the\n            // same under all conceivable circumstances. Because none of these\n            // factors are statically decidable in JavaScript, JS function\n            // equality is not well-defined. This ambiguity allows us to\n            // consider the best possible heuristic among various imperfect\n            // options, and equating non-native functions that have the same\n            // code has enormous practical benefits, such as when comparing\n            // functions that are repeatedly passed as fresh function\n            // expressions within objects that are otherwise deeply equal. Since\n            // any function created from the same syntactic expression (in the\n            // same code location) will always stringify to the same code\n            // according to fnToStr.call, we can reasonably expect these\n            // repeatedly passed function expressions to have the same code, and\n            // thus behave \"the same\" (with all the caveats mentioned above),\n            // even though the runtime function objects are !== to one another.\n            return !endsWith(aCode, nativeCodeSuffix);\n        }\n    }\n    // Otherwise the values are not equal.\n    return false;\n}\nfunction definedKeys(obj) {\n    // Remember that the second argument to Array.prototype.filter will be\n    // used as `this` within the callback function.\n    return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n    return this[key] !== void 0;\n}\nconst nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n    const fromIndex = full.length - suffix.length;\n    return fromIndex >= 0 &&\n        full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n    // Though cyclic references can make an object graph appear infinite from the\n    // perspective of a depth-first traversal, the graph still contains a finite\n    // number of distinct object references. We use the previousComparisons cache\n    // to avoid comparing the same pair of object references more than once, which\n    // guarantees termination (even if we end up comparing every object in one\n    // graph to every object in the other graph, which is extremely unlikely),\n    // while still allowing weird isomorphic structures (like rings with different\n    // lengths) a chance to pass the equality test.\n    let bSet = previousComparisons.get(a);\n    if (bSet) {\n        // Return true here because we can be sure false will be returned somewhere\n        // else if the objects are not equivalent.\n        if (bSet.has(b))\n            return true;\n    }\n    else {\n        previousComparisons.set(a, bSet = new Set);\n    }\n    bSet.add(b);\n    return false;\n}\n//# sourceMappingURL=index.js.map"], "names": [], "mappings": ";;;;AAAA,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC;AACtD,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC5C,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;AACtC;AACA;AACA;AACO,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,IAAI;AACR,QAAQ,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,YAAY;AACZ,QAAQ,mBAAmB,CAAC,KAAK,EAAE,CAAC;AACpC,KAAK;AACL,CAAC;AAGD,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;AACrB;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AACjB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;AACA;AACA,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC;AACA;AACA;AACA,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AACvB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,QAAQ,IAAI;AAChB,QAAQ,KAAK,gBAAgB;AAC7B;AACA;AACA,YAAY,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;AACrC,gBAAgB,OAAO,KAAK,CAAC;AAC7B;AACA,QAAQ,KAAK,iBAAiB,EAAE;AAChC,YAAY,IAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACzC,YAAY,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA,YAAY,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;AAC1C,YAAY,IAAI,QAAQ,KAAK,KAAK,CAAC,MAAM;AACzC,gBAAgB,OAAO,KAAK,CAAC;AAC7B;AACA,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAAE;AAC/C,gBAAgB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACvD,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB;AACjB,aAAa;AACb;AACA,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAAE;AAC/C,gBAAgB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAgB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC5C,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,KAAK,gBAAgB;AAC7B,YAAY,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,CAAC;AAChE,QAAQ,KAAK,iBAAiB;AAC9B;AACA,YAAY,IAAI,CAAC,KAAK,CAAC;AACvB,gBAAgB,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B;AACA,QAAQ,KAAK,kBAAkB,CAAC;AAChC,QAAQ,KAAK,eAAe;AAC5B,YAAY,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,QAAQ,KAAK,iBAAiB,CAAC;AAC/B,QAAQ,KAAK,iBAAiB;AAC9B,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAQ,KAAK,cAAc,CAAC;AAC5B,QAAQ,KAAK,cAAc,EAAE;AAC7B,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;AACjC,gBAAgB,OAAO,KAAK,CAAC;AAC7B,YAAY,IAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,MAAM,SAAS,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AAC1C,YAAY,MAAM,KAAK,GAAG,IAAI,KAAK,cAAc,CAAC;AAClD,YAAY,OAAO,IAAI,EAAE;AACzB,gBAAgB,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;AAC9C,gBAAgB,IAAI,IAAI,CAAC,IAAI;AAC7B,oBAAoB,MAAM;AAC1B;AACA,gBAAgB,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAClD;AACA,gBAAgB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAClC,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB;AACjB;AACA;AACA,gBAAgB,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;AAC1D,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,KAAK,sBAAsB,CAAC;AACpC,QAAQ,KAAK,qBAAqB,CAAC;AACnC,QAAQ,KAAK,sBAAsB,CAAC;AACpC,QAAQ,KAAK,qBAAqB,CAAC;AACnC,QAAQ,KAAK,oBAAoB,CAAC;AAClC,QAAQ,KAAK,qBAAqB,CAAC;AACnC,QAAQ,KAAK,sBAAsB;AACnC;AACA;AACA,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC;AACA,QAAQ,KAAK,mBAAmB,EAAE;AAClC,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC;AACnC,YAAY,IAAI,GAAG,KAAK,CAAC,CAAC,UAAU,EAAE;AACtC,gBAAgB,OAAO,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;AACnD;AACA,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,KAAK,wBAAwB,CAAC;AACtC,QAAQ,KAAK,4BAA4B,CAAC;AAC1C,QAAQ,KAAK,iCAAiC,CAAC;AAC/C,QAAQ,KAAK,mBAAmB,EAAE;AAClC,YAAY,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,YAAY,IAAI,KAAK,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AAC3C,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AACtD,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B;AACA;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AACtD,CAAC;AACD,SAAS,YAAY,CAAC,GAAG,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;AAChC,CAAC;AACD,MAAM,gBAAgB,GAAG,mBAAmB,CAAC;AAC7C,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE;AAChC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAClD,IAAI,OAAO,SAAS,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,SAAS,CAAC;AACtD,CAAC;AACD,SAAS,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,IAAI,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,IAAI,EAAE;AACd;AACA;AACA,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACvB,YAAY,OAAO,IAAI,CAAC;AACxB,KAAK;AACL,SAAS;AACT,QAAQ,mBAAmB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,IAAI,OAAO,KAAK,CAAC;AACjB;;;;;"}