{"version": 3, "file": "bundle.cjs", "sources": ["es5/slot.js", "es5/index.js"], "sourcesContent": ["// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nvar currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nvar MISSING_VALUE = {};\nvar idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nvar makeSlotClass = function () { return /** @class */ (function () {\n    function Slot() {\n        // If you have a Slot object, you can find out its slot.id, but you cannot\n        // guess the slot.id of a Slot you don't have access to, thanks to the\n        // randomized suffix.\n        this.id = [\n            \"slot\",\n            idCounter++,\n            Date.now(),\n            Math.random().toString(36).slice(2),\n        ].join(\":\");\n    }\n    Slot.prototype.hasValue = function () {\n        for (var context_1 = currentContext; context_1; context_1 = context_1.parent) {\n            // We use the Slot object iself as a key to its value, which means the\n            // value cannot be obtained without a reference to the Slot object.\n            if (this.id in context_1.slots) {\n                var value = context_1.slots[this.id];\n                if (value === MISSING_VALUE)\n                    break;\n                if (context_1 !== currentContext) {\n                    // Cache the value in currentContext.slots so the next lookup will\n                    // be faster. This caching is safe because the tree of contexts and\n                    // the values of the slots are logically immutable.\n                    currentContext.slots[this.id] = value;\n                }\n                return true;\n            }\n        }\n        if (currentContext) {\n            // If a value was not found for this Slot, it's never going to be found\n            // no matter how many times we look it up, so we might as well cache\n            // the absence of the value, too.\n            currentContext.slots[this.id] = MISSING_VALUE;\n        }\n        return false;\n    };\n    Slot.prototype.getValue = function () {\n        if (this.hasValue()) {\n            return currentContext.slots[this.id];\n        }\n    };\n    Slot.prototype.withValue = function (value, callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        var _a;\n        var slots = (_a = {\n                __proto__: null\n            },\n            _a[this.id] = value,\n            _a);\n        var parent = currentContext;\n        currentContext = { parent: parent, slots: slots };\n        try {\n            // Function.prototype.apply allows the arguments array argument to be\n            // omitted or undefined, so args! is fine here.\n            return callback.apply(thisArg, args);\n        }\n        finally {\n            currentContext = parent;\n        }\n    };\n    // Capture the current context and wrap a callback function so that it\n    // reestablishes the captured context when called.\n    Slot.bind = function (callback) {\n        var context = currentContext;\n        return function () {\n            var saved = currentContext;\n            try {\n                currentContext = context;\n                return callback.apply(this, arguments);\n            }\n            finally {\n                currentContext = saved;\n            }\n        };\n    };\n    // Immediately run a callback function without any captured context.\n    Slot.noContext = function (callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        if (currentContext) {\n            var saved = currentContext;\n            try {\n                currentContext = null;\n                // Function.prototype.apply allows the arguments array argument to be\n                // omitted or undefined, so args! is fine here.\n                return callback.apply(thisArg, args);\n            }\n            finally {\n                currentContext = saved;\n            }\n        }\n        else {\n            return callback.apply(thisArg, args);\n        }\n    };\n    return Slot;\n}()); };\nfunction maybe(fn) {\n    try {\n        return fn();\n    }\n    catch (ignored) { }\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nvar globalKey = \"@wry/context:Slot\";\nvar host = \n// Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(function () { return globalThis; }) ||\n    // Fall back to global, which works in Node.js and may be converted by some\n    // bundlers to the appropriate identifier (window, self, ...) depending on the\n    // bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\n    maybe(function () { return global; }) ||\n    // Otherwise, use a dummy host that's local to this module. We used to fall\n    // back to using the Array constructor as a namespace, but that was flagged in\n    // https://github.com/benjamn/wryware/issues/347, and can be avoided.\n    Object.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nvar globalHost = host;\nexport var Slot = globalHost[globalKey] ||\n    // Earlier versions of this package stored the globalKey property on the Array\n    // constructor, so we check there as well, to prevent Slot class duplication.\n    Array[globalKey] ||\n    (function (Slot) {\n        try {\n            Object.defineProperty(globalHost, globalKey, {\n                value: Slot,\n                enumerable: false,\n                writable: false,\n                // When it was possible for globalHost to be the Array constructor (a\n                // legacy Slot dedup strategy), it was important for the property to be\n                // configurable:true so it could be deleted. That does not seem to be as\n                // important when globalHost is the global object, but I don't want to\n                // cause similar problems again, and configurable:true seems safest.\n                // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n                configurable: true\n            });\n        }\n        finally {\n            return Slot;\n        }\n    })(makeSlotClass());\n//# sourceMappingURL=slot.js.map", "import { Slot } from \"./slot.js\";\nexport { Slot };\nexport var bind = Slot.bind, noContext = Slot.noContext;\n// Like global.setTimeout, except the callback runs with captured context.\nexport { setTimeoutWithContext as setTimeout };\nfunction setTimeoutWithContext(callback, delay) {\n    return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nexport function asyncFromGen(genFn) {\n    return function () {\n        var gen = genFn.apply(this, arguments);\n        var boundNext = bind(gen.next);\n        var boundThrow = bind(gen.throw);\n        return new Promise(function (resolve, reject) {\n            function invoke(method, argument) {\n                try {\n                    var result = method.call(gen, argument);\n                }\n                catch (error) {\n                    return reject(error);\n                }\n                var next = result.done ? resolve : invokeNext;\n                if (isPromiseLike(result.value)) {\n                    result.value.then(next, result.done ? reject : invokeThrow);\n                }\n                else {\n                    next(result.value);\n                }\n            }\n            var invokeNext = function (value) { return invoke(boundNext, value); };\n            var invokeThrow = function (error) { return invoke(boundThrow, error); };\n            invokeNext();\n        });\n    };\n}\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nvar wrappedFibers = [];\nexport function wrapYieldingFiberMethods(Fiber) {\n    // There can be only one implementation of Fiber per process, so this array\n    // should never grow longer than one element.\n    if (wrappedFibers.indexOf(Fiber) < 0) {\n        var wrap = function (obj, method) {\n            var fn = obj[method];\n            obj[method] = function () {\n                return noContext(fn, arguments, this);\n            };\n        };\n        // These methods can yield, according to\n        // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n        wrap(Fiber, \"yield\");\n        wrap(Fiber.prototype, \"run\");\n        wrap(Fiber.prototype, \"throwInto\");\n        wrappedFibers.push(Fiber);\n    }\n    return Fiber;\n}\n//# sourceMappingURL=index.js.map"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA,IAAI,cAAc,GAAG,IAAI,CAAC;AAC1B;AACA;AACA,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB;AACA;AACA;AACA,IAAI,aAAa,GAAG,YAAY,EAAE,sBAAsB,YAAY;AACpE,IAAI,SAAS,IAAI,GAAG;AACpB;AACA;AACA;AACA,QAAQ,IAAI,CAAC,EAAE,GAAG;AAClB,YAAY,MAAM;AAClB,YAAY,SAAS,EAAE;AACvB,YAAY,IAAI,CAAC,GAAG,EAAE;AACtB,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AAC1C,QAAQ,KAAK,IAAI,SAAS,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE;AACtF;AACA;AACA,YAAY,IAAI,IAAI,CAAC,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE;AAC5C,gBAAgB,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrD,gBAAgB,IAAI,KAAK,KAAK,aAAa;AAC3C,oBAAoB,MAAM;AAC1B,gBAAgB,IAAI,SAAS,KAAK,cAAc,EAAE;AAClD;AACA;AACA;AACA,oBAAoB,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAC1D,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,cAAc,EAAE;AAC5B;AACA;AACA;AACA,YAAY,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC;AAC1D,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AAC1C,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;AAC7B,YAAY,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,KAAK,EAAE,QAAQ;AACxD;AACA;AACA,IAAI,IAAI,EAAE,OAAO,EAAE;AACnB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,KAAK,IAAI,EAAE,GAAG;AAC1B,gBAAgB,SAAS,EAAE,IAAI;AAC/B,aAAa;AACb,YAAY,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK;AAC/B,YAAY,EAAE,CAAC,CAAC;AAChB,QAAQ,IAAI,MAAM,GAAG,cAAc,CAAC;AACpC,QAAQ,cAAc,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC1D,QAAQ,IAAI;AACZ;AACA;AACA,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACjD,SAAS;AACT,gBAAgB;AAChB,YAAY,cAAc,GAAG,MAAM,CAAC;AACpC,SAAS;AACT,KAAK,CAAC;AACN;AACA;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,UAAU,QAAQ,EAAE;AACpC,QAAQ,IAAI,OAAO,GAAG,cAAc,CAAC;AACrC,QAAQ,OAAO,YAAY;AAC3B,YAAY,IAAI,KAAK,GAAG,cAAc,CAAC;AACvC,YAAY,IAAI;AAChB,gBAAgB,cAAc,GAAG,OAAO,CAAC;AACzC,gBAAgB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACvD,aAAa;AACb,oBAAoB;AACpB,gBAAgB,cAAc,GAAG,KAAK,CAAC;AACvC,aAAa;AACb,SAAS,CAAC;AACV,KAAK,CAAC;AACN;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,UAAU,QAAQ;AACvC;AACA;AACA,IAAI,IAAI,EAAE,OAAO,EAAE;AACnB,QAAQ,IAAI,cAAc,EAAE;AAC5B,YAAY,IAAI,KAAK,GAAG,cAAc,CAAC;AACvC,YAAY,IAAI;AAChB,gBAAgB,cAAc,GAAG,IAAI,CAAC;AACtC;AACA;AACA,gBAAgB,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACrD,aAAa;AACb,oBAAoB;AACpB,gBAAgB,cAAc,GAAG,KAAK,CAAC;AACvC,aAAa;AACb,SAAS;AACT,aAAa;AACb,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACjD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC,EAAE,EAAE,EAAE,CAAC;AACR,SAAS,KAAK,CAAC,EAAE,EAAE;AACnB,IAAI,IAAI;AACR,QAAQ,OAAO,EAAE,EAAE,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,OAAO,EAAE,GAAG;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,GAAG,mBAAmB,CAAC;AACpC,IAAI,IAAI;AACR;AACA;AACA,KAAK,CAAC,YAAY,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;AACzC;AACA;AACA;AACA,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AACzC;AACA;AACA;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxB;AACA;AACA,IAAI,UAAU,GAAG,IAAI,CAAC;AACZ,IAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;AACvC;AACA;AACA,IAAI,KAAK,CAAC,SAAS,CAAC;AACpB,IAAI,CAAC,UAAU,IAAI,EAAE;AACrB,QAAQ,IAAI;AACZ,YAAY,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE;AACzD,gBAAgB,KAAK,EAAE,IAAI;AAC3B,gBAAgB,UAAU,EAAE,KAAK;AACjC,gBAAgB,QAAQ,EAAE,KAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,YAAY,EAAE,IAAI;AAClC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,gBAAgB;AAChB,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,KAAK,EAAE,aAAa,EAAE;;AClKZ,IAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU;AAGxD,SAAS,qBAAqB,CAAC,QAAQ,EAAE,KAAK,EAAE;AAChD,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC;AACD;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,YAAY;AACvB,QAAQ,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACzC,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AACtD,YAAY,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC9C,gBAAgB,IAAI;AACpB,oBAAoB,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC5D,iBAAiB;AACjB,gBAAgB,OAAO,KAAK,EAAE;AAC9B,oBAAoB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,iBAAiB;AACjB,gBAAgB,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,OAAO,GAAG,UAAU,CAAC;AAC9D,gBAAgB,IAAI,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AACjD,oBAAoB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,WAAW,CAAC,CAAC;AAChF,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvC,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,UAAU,GAAG,UAAU,KAAK,EAAE,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;AACnF,YAAY,IAAI,WAAW,GAAG,UAAU,KAAK,EAAE,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;AACrF,YAAY,UAAU,EAAE,CAAC;AACzB,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC;AACD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACrD,CAAC;AACD;AACA;AACA;AACA,IAAI,aAAa,GAAG,EAAE,CAAC;AAChB,SAAS,wBAAwB,CAAC,KAAK,EAAE;AAChD;AACA;AACA,IAAI,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC1C,QAAQ,IAAI,IAAI,GAAG,UAAU,GAAG,EAAE,MAAM,EAAE;AAC1C,YAAY,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;AACjC,YAAY,GAAG,CAAC,MAAM,CAAC,GAAG,YAAY;AACtC,gBAAgB,OAAO,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACtD,aAAa,CAAC;AACd,SAAS,CAAC;AACV;AACA;AACA,QAAQ,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7B,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC3C,QAAQ,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB;;;;;;;;;"}