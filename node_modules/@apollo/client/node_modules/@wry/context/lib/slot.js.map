{"version": 3, "file": "slot.js", "sourceRoot": "", "sources": ["../src/slot.ts"], "names": [], "mappings": "AAKA,sEAAsE;AACtE,0EAA0E;AAC1E,uCAAuC;AACvC,IAAI,cAAc,GAAmB,IAAI,CAAC;AAE1C,uEAAuE;AACvE,0DAA0D;AAC1D,MAAM,aAAa,GAAQ,EAAE,CAAC;AAE9B,IAAI,SAAS,GAAG,CAAC,CAAC;AAElB,uEAAuE;AACvE,4EAA4E;AAC5E,oEAAoE;AACpE,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,MAAM,IAAI;IAAV;QAC1B,0EAA0E;QAC1E,sEAAsE;QACtE,qBAAqB;QACL,OAAE,GAAG;YACnB,MAAM;YACN,SAAS,EAAE;YACX,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACpC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IA+Fd,CAAC;IA7FQ,QAAQ;QACb,KAAK,IAAI,OAAO,GAAG,cAAc,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,EAAE;YACpE,sEAAsE;YACtE,mEAAmE;YACnE,IAAI,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;gBAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrC,IAAI,KAAK,KAAK,aAAa;oBAAE,MAAM;gBACnC,IAAI,OAAO,KAAK,cAAc,EAAE;oBAC9B,kEAAkE;oBAClE,mEAAmE;oBACnE,mDAAmD;oBACnD,cAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;iBACxC;gBACD,OAAO,IAAI,CAAC;aACb;SACF;QACD,IAAI,cAAc,EAAE;YAClB,uEAAuE;YACvE,oEAAoE;YACpE,iCAAiC;YACjC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC;SAC/C;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,QAAQ;QACb,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,OAAO,cAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAW,CAAC;SACjD;IACH,CAAC;IAEM,SAAS,CACd,KAAa,EACb,QAAkD;IAClD,0EAA0E;IAC1E,sEAAsE;IACtE,IAAY,EACZ,OAAe;QAEf,MAAM,KAAK,GAAG;YACZ,SAAS,EAAE,IAAI;YACf,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK;SACjB,CAAC;QACF,MAAM,MAAM,GAAG,cAAc,CAAC;QAC9B,cAAc,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;QACnC,IAAI;YACF,qEAAqE;YACrE,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAQ,EAAE,IAAK,CAAC,CAAC;SACxC;gBAAS;YACR,cAAc,GAAG,MAAM,CAAC;SACzB;IACH,CAAC;IAED,sEAAsE;IACtE,kDAAkD;IAClD,MAAM,CAAC,IAAI,CACT,QAAkD;QAElD,MAAM,OAAO,GAAG,cAAc,CAAC;QAC/B,OAAO;YACL,MAAM,KAAK,GAAG,cAAc,CAAC;YAC7B,IAAI;gBACF,cAAc,GAAG,OAAO,CAAC;gBACzB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;aAC/C;oBAAS;gBACR,cAAc,GAAG,KAAK,CAAC;aACxB;QACH,CAAoB,CAAC;IACvB,CAAC;IAED,oEAAoE;IACpE,MAAM,CAAC,SAAS,CACd,QAAkD;IAClD,0EAA0E;IAC1E,sEAAsE;IACtE,IAAY,EACZ,OAAe;QAEf,IAAI,cAAc,EAAE;YAClB,MAAM,KAAK,GAAG,cAAc,CAAC;YAC7B,IAAI;gBACF,cAAc,GAAG,IAAI,CAAC;gBACtB,qEAAqE;gBACrE,+CAA+C;gBAC/C,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAQ,EAAE,IAAK,CAAC,CAAC;aACxC;oBAAS;gBACR,cAAc,GAAG,KAAK,CAAC;aACxB;SACF;aAAM;YACL,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAQ,EAAE,IAAK,CAAC,CAAC;SACxC;IACH,CAAC;CACF,CAAC;AAEF,SAAS,KAAK,CAAI,EAAW;IAC3B,IAAI;QACF,OAAO,EAAE,EAAE,CAAC;KACb;IAAC,OAAO,OAAO,EAAE,GAAE;AACtB,CAAC;AAED,2EAA2E;AAC3E,0EAA0E;AAC1E,2EAA2E;AAC3E,+EAA+E;AAC/E,4EAA4E;AAC5E,yEAAyE;AACzE,6EAA6E;AAC7E,6BAA6B;AAC7B,MAAM,SAAS,GAAG,mBAAmB,CAAC;AAEtC,MAAM,IAAI;AACR,oCAAoC;AACpC,gDAAgD;AAChD,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;IACvB,2EAA2E;IAC3E,8EAA8E;IAC9E,qFAAqF;IACrF,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IACnB,2EAA2E;IAC3E,8EAA8E;IAC9E,qEAAqE;IACrE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAiB,CAAC;AAEtC,+EAA+E;AAC/E,sBAAsB;AACtB,MAAM,UAAU,GAEZ,IAAI,CAAC;AAET,MAAM,CAAC,MAAM,IAAI,GACf,UAAU,CAAC,SAAS,CAAC;IACrB,8EAA8E;IAC9E,6EAA6E;IAC5E,KAA2B,CAAC,SAAS,CAAC;IACvC,CAAC,UAAU,IAAI;QACb,IAAI;YACF,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE;gBAC3C,KAAK,EAAE,IAAI;gBACX,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,qEAAqE;gBACrE,uEAAuE;gBACvE,wEAAwE;gBACxE,sEAAsE;gBACtE,oEAAoE;gBACpE,oEAAoE;gBACpE,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;SACJ;gBAAS;YACR,OAAO,IAAI,CAAC;SACb;IACH,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC"}