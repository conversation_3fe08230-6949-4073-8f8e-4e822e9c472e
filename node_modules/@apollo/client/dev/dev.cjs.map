{"version": 3, "file": "dev.cjs", "sources": ["../invariantErrorCodes.js", "../version.js", "../utilities/globals/maybe.js", "../utilities/globals/global.js", "../utilities/common/makeUniqueId.js", "../utilities/common/stringifyForDisplay.js", "../utilities/globals/invariantWrappers.js", "setErrorMessageHandler.js", "loadErrorMessageHandler.js", "loadDevMessages.js", "loadErrorMessages.js"], "sourcesContent": ["export const errorCodes = // This file is used by the error message display website and the\n// @apollo/client/includeErrors entry point.\n// This file is not meant to be imported manually.\n{\n  2: {\n    file: \"@apollo/client/cache/inmemory/entityStore.js\",\n    condition: \"typeof dataId === \\\"string\\\"\",\n    message: \"store.merge expects a string ID\"\n  },\n\n  5: {\n    file: \"@apollo/client/cache/inmemory/key-extractor.js\",\n    condition: \"extracted !== void 0\",\n    message: \"Missing field '%s' while extracting keyFields from %s\"\n  },\n\n  6: {\n    file: \"@apollo/client/cache/inmemory/policies.js\",\n    condition: \"!old || old === which\",\n    message: \"Cannot change root %s __typename more than once\"\n  },\n\n  9: {\n    file: \"@apollo/client/cache/inmemory/policies.js\",\n    message: \"Cannot automatically merge arrays\"\n  },\n\n  10: {\n    file: \"@apollo/client/cache/inmemory/readFromStore.js\",\n    message: \"No fragment named %s\"\n  },\n\n  11: {\n    file: \"@apollo/client/cache/inmemory/readFromStore.js\",\n    condition: \"!isReference(value)\",\n    message: \"Missing selection set for object of type %s returned for query field %s\"\n  },\n\n  12: {\n    file: \"@apollo/client/cache/inmemory/writeToStore.js\",\n    message: \"Could not identify object %s\"\n  },\n\n  14: {\n    file: \"@apollo/client/cache/inmemory/writeToStore.js\",\n    message: \"No fragment named %s\"\n  },\n\n  16: {\n    file: \"@apollo/client/core/ApolloClient.js\",\n\n    message: \"To initialize Apollo Client, you must specify a 'cache' property \" +\n        \"in the options object. \\n\" +\n        \"For more information, please visit: https://go.apollo.dev/c/docs\"\n  },\n\n  17: {\n    file: \"@apollo/client/core/ApolloClient.js\",\n    condition: \"options.fetchPolicy !== \\\"cache-and-network\\\"\",\n\n    message: \"The cache-and-network fetchPolicy does not work with client.query, because \" +\n        \"client.query can only return a single result. Please use client.watchQuery \" +\n        \"to receive multiple results from the cache and the network, or consider \" +\n        \"using a different fetchPolicy, such as cache-first or network-only.\"\n  },\n\n  19: {\n    file: \"@apollo/client/core/LocalState.js\",\n    condition: \"fragment\",\n    message: \"No fragment named %s\"\n  },\n\n  20: {\n    file: \"@apollo/client/core/LocalState.js\",\n    condition: \"fragment\",\n    message: \"No fragment named %s\"\n  },\n\n  22: {\n    file: \"@apollo/client/core/ObservableQuery.js\",\n    condition: \"updateQuery\",\n    message: \"You must provide an `updateQuery` function when using `fetchMore` with a `no-cache` fetch policy.\"\n  },\n\n  24: {\n    file: \"@apollo/client/core/ObservableQuery.js\",\n    condition: \"pollInterval\",\n    message: \"Attempted to start a polling query without a polling interval.\"\n  },\n\n  27: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    message: \"QueryManager stopped while query was in flight\"\n  },\n\n  28: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    condition: \"mutation\",\n    message: \"mutation option is required. You must specify your GraphQL document in the mutation option.\"\n  },\n\n  29: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    condition: \"fetchPolicy === \\\"network-only\\\" || fetchPolicy === \\\"no-cache\\\"\",\n    message: \"Mutations support only 'network-only' or 'no-cache' fetchPolicy strings. The default `network-only` behavior automatically writes mutation results to the cache. Passing `no-cache` skips the cache write.\"\n  },\n\n  30: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    condition: \"options.query\",\n\n    message: \"query option is required. You must specify your GraphQL document \" +\n        \"in the query option.\"\n  },\n\n  31: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    condition: \"options.query.kind === \\\"Document\\\"\",\n    message: 'You must wrap the query string in a \"gql\" tag.'\n  },\n\n  32: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    condition: \"!options.returnPartialData\",\n    message: \"returnPartialData option only supported on watchQuery.\"\n  },\n\n  33: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    condition: \"!options.pollInterval\",\n    message: \"pollInterval option only supported on watchQuery.\"\n  },\n\n  34: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    message: \"Store reset while query was in flight (not completed in link chain)\"\n  },\n\n  39: {\n    file: \"@apollo/client/link/core/ApolloLink.js\",\n    message: \"request is not implemented\"\n  },\n\n  40: {\n    file: \"@apollo/client/link/http/checkFetcher.js\",\n    message: \"\\n\\\"fetch\\\" has not been found globally and no fetcher has been configured. To fix this, install a fetch package (like https://www.npmjs.com/package/cross-fetch), instantiate the fetcher, and pass it into your HttpLink constructor. For example:\\n\\nimport fetch from 'cross-fetch';\\nimport { ApolloClient, HttpLink } from '@apollo/client';\\nconst client = new ApolloClient({\\n  link: new HttpLink({ uri: '/graphql', fetch })\\n});\\n    \"\n  },\n\n  42: {\n    file: \"@apollo/client/link/http/serializeFetchParameter.js\",\n    message: \"Network request failed. %s is not serializable: %s\"\n  },\n\n  43: {\n    file: \"@apollo/client/link/persisted-queries/index.js\",\n    condition: \"options &&\\n    (typeof options.sha256 === \\\"function\\\" ||\\n        typeof options.generateHash === \\\"function\\\")\",\n\n    message: 'Missing/invalid \"sha256\" or \"generateHash\" function. Please ' +\n        'configure one using the \"createPersistedQueryLink(options)\" options ' +\n        \"parameter.\"\n  },\n\n  44: {\n    file: \"@apollo/client/link/persisted-queries/index.js\",\n    condition: \"forward\",\n    message: \"PersistedQueryLink cannot be the last link in the chain.\"\n  },\n\n  46: {\n    file: \"@apollo/client/link/utils/validateOperation.js\",\n    message: \"illegal argument: %s\"\n  },\n\n  47: {\n    file: \"@apollo/client/masking/maskDefinition.js\",\n    condition: \"fragment\",\n    message: \"Could not find fragment with name '%s'.\"\n  },\n\n  49: {\n    file: \"@apollo/client/masking/maskFragment.js\",\n    condition: \"fragments.length === 1\",\n    message: \"Found %s fragments. `fragmentName` must be provided when there is not exactly 1 fragment.\"\n  },\n\n  50: {\n    file: \"@apollo/client/masking/maskFragment.js\",\n    condition: \"!!fragment\",\n    message: \"Could not find fragment with name \\\"%s\\\".\"\n  },\n\n  51: {\n    file: \"@apollo/client/masking/maskOperation.js\",\n    condition: \"definition\",\n    message: \"Expected a parsed GraphQL document with a query, mutation, or subscription.\"\n  },\n\n  53: {\n    file: \"@apollo/client/react/context/ApolloConsumer.js\",\n    condition: \"context && context.client\",\n\n    message: 'Could not find \"client\" in the context of ApolloConsumer. ' +\n        \"Wrap the root component in an <ApolloProvider>.\"\n  },\n\n  54: {\n    file: \"@apollo/client/react/context/ApolloContext.js\",\n    condition: \"\\\"createContext\\\" in React\",\n\n    message: \"Invoking `getApolloContext` in an environment where `React.createContext` is not available.\\n\" +\n        \"The Apollo Client functionality you are trying to use is only available in React Client Components.\\n\" +\n        'Please make sure to add \"use client\" at the top of your file.\\n' +\n        // TODO: change to React documentation once React documentation contains information about Client Components\n        \"For more information, see https://nextjs.org/docs/getting-started/react-essentials#client-components\"\n  },\n\n  55: {\n    file: \"@apollo/client/react/context/ApolloProvider.js\",\n    condition: \"context.client\",\n\n    message: \"ApolloProvider was not passed a client instance. Make \" +\n        'sure you pass in your client via the \"client\" prop.'\n  },\n\n  56: {\n    file: \"@apollo/client/react/hoc/hoc-utils.js\",\n    condition: \"this.withRef\",\n\n    message: \"To access the wrapped instance, you need to specify \" +\n        \"{ withRef: true } in the options\"\n  },\n\n  57: {\n    file: \"@apollo/client/react/hoc/withApollo.js\",\n    condition: \"operationOptions.withRef\",\n\n    message: \"To access the wrapped instance, you need to specify \" +\n        \"{ withRef: true } in the options\"\n  },\n\n  58: {\n    file: \"@apollo/client/react/hooks/useApolloClient.js\",\n    condition: \"!!client\",\n\n    message: 'Could not find \"client\" in the context or passed in as an option. ' +\n        \"Wrap the root component in an <ApolloProvider>, or pass an ApolloClient \" +\n        \"instance in via options.\"\n  },\n\n  59: {\n    file: \"@apollo/client/react/hooks/useLoadableQuery.js\",\n    condition: \"!calledDuringRender()\",\n    message: \"useLoadableQuery: 'loadQuery' should not be called during render. To start a query during render, use the 'useBackgroundQuery' hook.\"\n  },\n\n  60: {\n    file: \"@apollo/client/react/hooks/useLoadableQuery.js\",\n    condition: \"internalQueryRef\",\n    message: \"The query has not been loaded. Please load the query.\"\n  },\n\n  65: {\n    file: \"@apollo/client/react/hooks/useSubscription.js\",\n    condition: \"!optionsRef.current.skip\",\n    message: \"A subscription that is skipped cannot be restarted.\"\n  },\n\n  66: {\n    file: \"@apollo/client/react/hooks/useSuspenseQuery.js\",\n    condition: \"supportedFetchPolicies.includes(fetchPolicy)\",\n    message: \"The fetch policy `%s` is not supported with suspense.\"\n  },\n\n  69: {\n    file: \"@apollo/client/react/internal/cache/QueryReference.js\",\n    condition: \"!queryRef || QUERY_REFERENCE_SYMBOL in queryRef\",\n    message: \"Expected a QueryRef object, but got something else instead.\"\n  },\n\n  70: {\n    file: \"@apollo/client/react/parser/index.js\",\n    condition: \"!!document && !!document.kind\",\n\n    message: \"Argument of %s passed to parser was not a valid GraphQL \" +\n        \"DocumentNode. You may need to use 'graphql-tag' or another method \" +\n        \"to convert your operation into a document\"\n  },\n\n  71: {\n    file: \"@apollo/client/react/parser/index.js\",\n    condition: \"!fragments.length ||\\n    queries.length ||\\n    mutations.length ||\\n    subscriptions.length\",\n\n    message: \"Passing only a fragment to 'graphql' is not yet supported. \" +\n        \"You must include a query, subscription or mutation as well\"\n  },\n\n  72: {\n    file: \"@apollo/client/react/parser/index.js\",\n    condition: \"queries.length + mutations.length + subscriptions.length <= 1\",\n\n    message: \"react-apollo only supports a query, subscription, or a mutation per HOC. \" +\n        \"%s had %s queries, %s \" +\n        \"subscriptions and %s mutations. \" +\n        \"You can use 'compose' to join multiple operation types to a component\"\n  },\n\n  73: {\n    file: \"@apollo/client/react/parser/index.js\",\n    condition: \"definitions.length === 1\",\n\n    message: \"react-apollo only supports one definition per HOC. %s had \" +\n        \"%s definitions. \" +\n        \"You can use 'compose' to join multiple operation types to a component\"\n  },\n\n  74: {\n    file: \"@apollo/client/react/parser/index.js\",\n    condition: \"operation.type === type\",\n    message: \"Running a %s requires a graphql \" + \"%s, but a %s was used instead.\"\n  },\n\n  75: {\n    file: \"@apollo/client/testing/core/mocking/mockLink.js\",\n    condition: \"queryWithoutClientOnlyDirectives\",\n    message: \"query is required\"\n  },\n\n  76: {\n    file: \"@apollo/client/testing/core/mocking/mockLink.js\",\n    condition: \"mockedResponse.maxUsageCount > 0\",\n    message: \"Mock response maxUsageCount must be greater than 0, %s given\"\n  },\n\n  77: {\n    file: \"@apollo/client/utilities/graphql/DocumentTransform.js\",\n    condition: \"Array.isArray(cacheKeys)\",\n    message: \"`getCacheKey` must return an array or undefined\"\n  },\n\n  78: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    condition: \"evaledValue !== void 0\",\n    message: \"Invalid variable referenced in @%s directive.\"\n  },\n\n  79: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    condition: \"directiveArguments && directiveArguments.length === 1\",\n    message: \"Incorrect number of arguments for the @%s directive.\"\n  },\n\n  80: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    condition: \"ifArgument.name && ifArgument.name.value === \\\"if\\\"\",\n    message: \"Invalid argument for the @%s directive.\"\n  },\n\n  81: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    condition: \"ifValue &&\\n    (ifValue.kind === \\\"Variable\\\" || ifValue.kind === \\\"BooleanValue\\\")\",\n    message: \"Argument for the @%s directive must be a variable or a boolean value.\"\n  },\n\n  85: {\n    file: \"@apollo/client/utilities/graphql/fragments.js\",\n\n    message: \"Found a %s operation%s. \" +\n        \"No operations are allowed when using a fragment as a query. Only fragments are allowed.\"\n  },\n\n  86: {\n    file: \"@apollo/client/utilities/graphql/fragments.js\",\n    condition: \"fragments.length === 1\",\n    message: \"Found %s fragments. `fragmentName` must be provided when there is not exactly 1 fragment.\"\n  },\n\n  87: {\n    file: \"@apollo/client/utilities/graphql/fragments.js\",\n    condition: \"fragment\",\n    message: \"No fragment named %s\"\n  },\n\n  88: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    condition: \"doc && doc.kind === \\\"Document\\\"\",\n    message: \"Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a \\\"gql\\\" tag? http://docs.apollostack.com/apollo-client/core.html#gql\"\n  },\n\n  89: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    message: \"Schema type definitions not allowed in queries. Found: \\\"%s\\\"\"\n  },\n\n  90: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    condition: \"operations.length <= 1\",\n    message: \"Ambiguous GraphQL document: contains %s operations\"\n  },\n\n  91: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    condition: \"queryDef && queryDef.operation === \\\"query\\\"\",\n    message: \"Must contain a query definition.\"\n  },\n\n  92: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    condition: \"doc.kind === \\\"Document\\\"\",\n    message: \"Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a \\\"gql\\\" tag? http://docs.apollostack.com/apollo-client/core.html#gql\"\n  },\n\n  93: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    condition: \"doc.definitions.length <= 1\",\n    message: \"Fragment must have exactly one definition.\"\n  },\n\n  94: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    condition: \"fragmentDef.kind === \\\"FragmentDefinition\\\"\",\n    message: \"Must be a fragment definition.\"\n  },\n\n  95: {\n    file: \"@apollo/client/utilities/graphql/getFromAST.js\",\n    message: \"Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment.\"\n  },\n\n  96: {\n    file: \"@apollo/client/utilities/graphql/storeUtils.js\",\n\n    message: \"The inline argument \\\"%s\\\" of kind \\\"%s\\\"\" +\n        \"is not supported. Use variables instead of inline arguments to \" +\n        \"overcome this limitation.\"\n  }\n};\n\nexport const devDebug = {\n  18: {\n    file: \"@apollo/client/core/ApolloClient.js\",\n    message: \"In client.refetchQueries, Promise.all promise rejected with error %o\"\n  },\n\n  26: {\n    file: \"@apollo/client/core/ObservableQuery.js\",\n    message: \"Missing cache result fields: %o\"\n  }\n};\n\nexport const devLog = {};\n\nexport const devWarn = {\n  1: {\n    file: \"@apollo/client/cache/core/cache.js\",\n    message: \"Could not identify object passed to `from` for '%s' fragment, either because the object is non-normalized or the key fields are missing. If you are masking this object, please ensure the key fields are requested by the parent object.\"\n  },\n\n  3: {\n    file: \"@apollo/client/cache/inmemory/entityStore.js\",\n\n    message: \"cache.modify: You are trying to write a Reference that is not part of the store: %o\\n\" +\n        \"Please make sure to set the `mergeIntoStore` parameter to `true` when creating a Reference that is not part of the store yet:\\n\" +\n        \"`toReference(object, true)`\"\n  },\n\n  4: {\n    file: \"@apollo/client/cache/inmemory/entityStore.js\",\n\n    message: \"cache.modify: Writing an array with a mix of both References and Objects will not result in the Objects being normalized correctly.\\n\" +\n        \"Please convert the object instance %o to a Reference before writing it to the cache by calling `toReference(object, true)`.\"\n  },\n\n  7: {\n    file: \"@apollo/client/cache/inmemory/policies.js\",\n    message: \"Inferring subtype %s of supertype %s\"\n  },\n\n  8: {\n    file: \"@apollo/client/cache/inmemory/policies.js\",\n    message: \"Undefined 'from' passed to readField with arguments %s\"\n  },\n\n  15: {\n    file: \"@apollo/client/cache/inmemory/writeToStore.js\",\n    message: \"Cache data may be lost when replacing the %s field of a %s object.\\n\\nThis could cause additional (usually avoidable) network requests to fetch data that were otherwise cached.\\n\\nTo address this problem (which is not a bug in Apollo Client), %sdefine a custom merge function for the %s field, so InMemoryCache can safely merge these objects:\\n\\n  existing: %o\\n  incoming: %o\\n\\nFor more information about these options, please refer to the documentation:\\n\\n  * Ensuring entity objects have IDs: https://go.apollo.dev/c/generating-unique-identifiers\\n  * Defining custom merge functions: https://go.apollo.dev/c/merging-non-normalized-objects\\n\"\n  },\n\n  21: {\n    file: \"@apollo/client/core/ObservableQuery.js\",\n    message: \"Called refetch(%o) for query %o, which does not declare a $variables variable.\\nDid you mean to call refetch(variables) instead of refetch({ variables })?\"\n  },\n\n  35: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    message: \"Unknown query named \\\"%s\\\" requested in refetchQueries options.include array\"\n  },\n\n  36: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    message: \"Unknown anonymous query requested in refetchQueries options.include array\"\n  },\n\n  37: {\n    file: \"@apollo/client/core/QueryManager.js\",\n    message: '[%s]: Fragments masked by data masking are inaccessible when using fetch policy \"no-cache\". Please add `@unmask` to each fragment spread to access the data.'\n  },\n\n  38: {\n    file: \"@apollo/client/link/core/ApolloLink.js\",\n    message: \"You are calling concat on a terminating link, which will have no effect %o\"\n  },\n\n  41: {\n    file: \"@apollo/client/link/http/createHttpLink.js\",\n    message: \"Multipart-subscriptions do not support @defer\"\n  },\n\n  45: {\n    file: \"@apollo/client/link/utils/toPromise.js\",\n    message: \"Promise Wrapper does not support multiple results from Observable\"\n  },\n\n  48: {\n    file: \"@apollo/client/masking/maskDefinition.js\",\n    message: \"Accessing unmasked field on %s at path '%s'. This field will not be available when masking is enabled. Please read the field from the fragment instead.\"\n  },\n\n  52: {\n    file: \"@apollo/client/masking/utils.js\",\n    message: \"The configured cache does not support data masking which effectively disables it. Please use a cache that supports data masking or disable data masking to silence this warning.\"\n  },\n\n  61: {\n    file: \"@apollo/client/react/hooks/useSubscription.js\",\n    message: \"'useSubscription' supports only the 'onSubscriptionData' or 'onData' option, but not both. Only the 'onData' option will be used.\"\n  },\n\n  62: {\n    file: \"@apollo/client/react/hooks/useSubscription.js\",\n    message: \"'onSubscriptionData' is deprecated and will be removed in a future major version. Please use the 'onData' option instead.\"\n  },\n\n  63: {\n    file: \"@apollo/client/react/hooks/useSubscription.js\",\n    message: \"'useSubscription' supports only the 'onSubscriptionComplete' or 'onComplete' option, but not both. Only the 'onComplete' option will be used.\"\n  },\n\n  64: {\n    file: \"@apollo/client/react/hooks/useSubscription.js\",\n    message: \"'onSubscriptionComplete' is deprecated and will be removed in a future major version. Please use the 'onComplete' option instead.\"\n  },\n\n  67: {\n    file: \"@apollo/client/react/hooks/useSuspenseQuery.js\",\n    message: \"Using `returnPartialData` with a `no-cache` fetch policy has no effect. To read partial data from the cache, consider using an alternate fetch policy.\"\n  },\n\n  82: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    message: \"@unmask 'mode' argument does not support variables.\"\n  },\n\n  83: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    message: \"@unmask 'mode' argument must be of type string.\"\n  },\n\n  84: {\n    file: \"@apollo/client/utilities/graphql/directives.js\",\n    message: \"@unmask 'mode' argument does not recognize value '%s'.\"\n  },\n\n  98: {\n    file: \"@apollo/client/utilities/graphql/transform.js\",\n\n    message: \"Removing an @connection directive even though it does not have a key. \" +\n        \"You may want to use the key parameter to specify a store key.\"\n  }\n};\n\nexport const devError = {\n  13: {\n    file: \"@apollo/client/cache/inmemory/writeToStore.js\",\n    message: \"Missing field '%s' while writing result %o\"\n  },\n\n  23: {\n    file: \"@apollo/client/core/ObservableQuery.js\",\n    message: \"Unhandled GraphQL subscription error\"\n  },\n\n  25: {\n    file: \"@apollo/client/core/ObservableQuery.js\",\n    message: \"Unhandled error\"\n  },\n\n  68: {\n    file: \"@apollo/client/react/hooks/useSyncExternalStore.js\",\n    message: \"The result of getSnapshot should be cached to avoid an infinite loop\"\n  },\n\n  97: {\n    file: \"@apollo/client/utilities/graphql/transform.js\",\n    message: \"Could not find operation or fragment\"\n  }\n};\n", "export var version = \"3.12.8\";\n//# sourceMappingURL=version.js.map", "export function maybe(thunk) {\n    try {\n        return thunk();\n    }\n    catch (_a) { }\n}\n//# sourceMappingURL=maybe.js.map", "import { maybe } from \"./maybe.js\";\nexport default (maybe(function () { return globalThis; }) ||\n    maybe(function () { return window; }) ||\n    maybe(function () { return self; }) ||\n    maybe(function () { return global; }) || // We don't expect the Function constructor ever to be invoked at runtime, as\n// long as at least one of globalThis, window, self, or global is defined, so\n// we are under no obligation to make it easy for static analysis tools to\n// detect syntactic usage of the Function constructor. If you think you can\n// improve your static analysis to detect this obfuscation, think again. This\n// is an arms race you cannot win, at least not in JavaScript.\nmaybe(function () {\n    return maybe.constructor(\"return this\")();\n}));\n//# sourceMappingURL=global.js.map", "var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n    var count = prefixCounts.get(prefix) || 1;\n    prefixCounts.set(prefix, count + 1);\n    return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}\n//# sourceMappingURL=makeUniqueId.js.map", "import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n    if (space === void 0) { space = 0; }\n    var undefId = makeUniqueId(\"stringifyForDisplay\");\n    return JSON.stringify(value, function (key, value) {\n        return value === void 0 ? undefId : value;\n    }, space)\n        .split(JSON.stringify(undefId))\n        .join(\"<undefined>\");\n}\n//# sourceMappingURL=stringifyForDisplay.js.map", "import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\nfunction wrap(fn) {\n    return function (message) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (typeof message === \"number\") {\n            var arg0 = message;\n            message = getHandledErrorMsg(arg0);\n            if (!message) {\n                message = getFallbackErrorMsg(arg0, args);\n                args = [];\n            }\n        }\n        fn.apply(void 0, [message].concat(args));\n    };\n}\nvar invariant = Object.assign(function invariant(condition, message) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    if (!condition) {\n        originalInvariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));\n    }\n}, {\n    debug: wrap(originalInvariant.debug),\n    log: wrap(originalInvariant.log),\n    warn: wrap(originalInvariant.warn),\n    error: wrap(originalInvariant.error),\n});\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(message) {\n    var optionalParams = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        optionalParams[_i - 1] = arguments[_i];\n    }\n    return new InvariantError(getHandledErrorMsg(message, optionalParams) ||\n        getFallbackErrorMsg(message, optionalParams));\n}\nvar ApolloErrorMessageHandler = Symbol.for(\"ApolloErrorMessageHandler_\" + version);\nfunction stringify(arg) {\n    if (typeof arg == \"string\") {\n        return arg;\n    }\n    try {\n        return stringifyForDisplay(arg, 2).slice(0, 1000);\n    }\n    catch (_a) {\n        return \"<non-serializable>\";\n    }\n}\nfunction getHandledErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return (global[ApolloErrorMessageHandler] &&\n        global[ApolloErrorMessageHandler](message, messageArgs.map(stringify)));\n}\nfunction getFallbackErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return \"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#\".concat(encodeURIComponent(JSON.stringify({\n        version: version,\n        message: message,\n        args: messageArgs.map(stringify),\n    })));\n}\nexport { invariant, InvariantError, newInvariantError, ApolloErrorMessageHandler, };\n//# sourceMappingURL=invariantWrappers.js.map", "import { global } from \"../utilities/globals/index.js\";\nimport { ApolloErrorMessageHandler } from \"../utilities/globals/invariantWrappers.js\";\n/**\n * Overrides the global \"Error Message Handler\" with a custom implementation.\n */\nexport function setErrorMessageHandler(handler) {\n    global[ApolloErrorMessageHandler] = handler;\n}\n//# sourceMappingURL=setErrorMessageHandler.js.map", "import { global } from \"../utilities/globals/index.js\";\nimport { ApolloErrorMessageHandler } from \"../utilities/globals/invariantWrappers.js\";\nimport { setErrorMessageHandler } from \"./setErrorMessageHandler.js\";\n/**\n * Injects Apollo Client's default error message handler into the application and\n * also loads the error codes that are passed in as arguments.\n */\nexport function loadErrorMessageHandler() {\n    var errorCodes = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        errorCodes[_i] = arguments[_i];\n    }\n    setErrorMessageHandler(handler);\n    for (var _a = 0, errorCodes_1 = errorCodes; _a < errorCodes_1.length; _a++) {\n        var codes = errorCodes_1[_a];\n        Object.assign(handler, codes);\n    }\n    return handler;\n}\nvar handler = (function (message, args) {\n    if (typeof message === \"number\") {\n        var definition = global[ApolloErrorMessageHandler][message];\n        if (!message || !(definition === null || definition === void 0 ? void 0 : definition.message))\n            return;\n        message = definition.message;\n    }\n    return args.reduce(function (msg, arg) { return msg.replace(/%[sdfo]/, String(arg)); }, String(message));\n});\n//# sourceMappingURL=loadErrorMessageHandler.js.map", "import { devDebug, devError, devLog, devWarn } from \"../invariantErrorCodes.js\";\nimport { loadErrorMessageHandler } from \"./loadErrorMessageHandler.js\";\nexport function loadDevMessages() {\n    loadErrorMessageHandler(devDebug, devError, devLog, devWarn);\n}\n//# sourceMappingURL=loadDevMessages.js.map", "import { errorCodes } from \"../invariantErrorCodes.js\";\nimport { loadErrorMessageHandler } from \"./loadErrorMessageHandler.js\";\nexport function loadErrorMessages() {\n    loadErrorMessageHandler(errorCodes);\n}\n//# sourceMappingURL=loadErrorMessages.js.map"], "names": ["originalInvariant", "global"], "mappings": ";;;;;;;AAAO,MAAM,UAAU;AAGvB;AACA,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,8CAA8C;AACxD,IAAI,SAAS,EAAE,8BAA8B;AAC7C,IAAI,OAAO,EAAE,iCAAiC;AAC9C,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,sBAAsB;AACrC,IAAI,OAAO,EAAE,uDAAuD;AACpE,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,2CAA2C;AACrD,IAAI,SAAS,EAAE,uBAAuB;AACtC,IAAI,OAAO,EAAE,iDAAiD;AAC9D,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,2CAA2C;AACrD,IAAI,OAAO,EAAE,mCAAmC;AAChD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,sBAAsB;AACnC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,OAAO,EAAE,yEAAyE;AACtF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,8BAA8B;AAC3C,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,sBAAsB;AACnC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAE/C,IAAI,OAAO,EAAE,mEAAmE;AAChF,QAAQ,2BAA2B;AACnC,QAAQ,kEAAkE;AAC1E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,+CAA+C;AAE9D,IAAI,OAAO,EAAE,6EAA6E;AAC1F,QAAQ,6EAA6E;AACrF,QAAQ,0EAA0E;AAClF,QAAQ,qEAAqE;AAC7E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,mCAAmC;AAC7C,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,OAAO,EAAE,sBAAsB;AACnC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,mCAAmC;AAC7C,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,OAAO,EAAE,sBAAsB;AACnC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,SAAS,EAAE,aAAa;AAC5B,IAAI,OAAO,EAAE,mGAAmG;AAChH,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,SAAS,EAAE,cAAc;AAC7B,IAAI,OAAO,EAAE,gEAAgE;AAC7E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,OAAO,EAAE,gDAAgD;AAC7D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,OAAO,EAAE,6FAA6F;AAC1G,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,kEAAkE;AACjF,IAAI,OAAO,EAAE,4MAA4M;AACzN,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,eAAe;AAE9B,IAAI,OAAO,EAAE,mEAAmE;AAChF,QAAQ,sBAAsB;AAC9B,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,qCAAqC;AACpD,IAAI,OAAO,EAAE,gDAAgD;AAC7D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,4BAA4B;AAC3C,IAAI,OAAO,EAAE,wDAAwD;AACrE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,SAAS,EAAE,uBAAuB;AACtC,IAAI,OAAO,EAAE,mDAAmD;AAChE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,OAAO,EAAE,qEAAqE;AAClF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,4BAA4B;AACzC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,0CAA0C;AACpD,IAAI,OAAO,EAAE,obAAob;AACjc,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qDAAqD;AAC/D,IAAI,OAAO,EAAE,oDAAoD;AACjE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,mHAAmH;AAElI,IAAI,OAAO,EAAE,8DAA8D;AAC3E,QAAQ,sEAAsE;AAC9E,QAAQ,YAAY;AACpB,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,SAAS;AACxB,IAAI,OAAO,EAAE,0DAA0D;AACvE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,sBAAsB;AACnC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,0CAA0C;AACpD,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,OAAO,EAAE,yCAAyC;AACtD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,SAAS,EAAE,wBAAwB;AACvC,IAAI,OAAO,EAAE,2FAA2F;AACxG,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,SAAS,EAAE,YAAY;AAC3B,IAAI,OAAO,EAAE,2CAA2C;AACxD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,yCAAyC;AACnD,IAAI,SAAS,EAAE,YAAY;AAC3B,IAAI,OAAO,EAAE,6EAA6E;AAC1F,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,2BAA2B;AAE1C,IAAI,OAAO,EAAE,4DAA4D;AACzE,QAAQ,iDAAiD;AACzD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,SAAS,EAAE,4BAA4B;AAE3C,IAAI,OAAO,EAAE,+FAA+F;AAC5G,QAAQ,uGAAuG;AAC/G,QAAQ,iEAAiE;AAEzE,QAAQ,sGAAsG;AAC9G,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,gBAAgB;AAE/B,IAAI,OAAO,EAAE,wDAAwD;AACrE,QAAQ,qDAAqD;AAC7D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,uCAAuC;AACjD,IAAI,SAAS,EAAE,cAAc;AAE7B,IAAI,OAAO,EAAE,sDAAsD;AACnE,QAAQ,kCAAkC;AAC1C,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,SAAS,EAAE,0BAA0B;AAEzC,IAAI,OAAO,EAAE,sDAAsD;AACnE,QAAQ,kCAAkC;AAC1C,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,SAAS,EAAE,UAAU;AAEzB,IAAI,OAAO,EAAE,oEAAoE;AACjF,QAAQ,0EAA0E;AAClF,QAAQ,0BAA0B;AAClC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,uBAAuB;AACtC,IAAI,OAAO,EAAE,sIAAsI;AACnJ,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,kBAAkB;AACjC,IAAI,OAAO,EAAE,uDAAuD;AACpE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,SAAS,EAAE,0BAA0B;AACzC,IAAI,OAAO,EAAE,qDAAqD;AAClE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,8CAA8C;AAC7D,IAAI,OAAO,EAAE,uDAAuD;AACpE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,uDAAuD;AACjE,IAAI,SAAS,EAAE,iDAAiD;AAChE,IAAI,OAAO,EAAE,6DAA6D;AAC1E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,SAAS,EAAE,+BAA+B;AAE9C,IAAI,OAAO,EAAE,0DAA0D;AACvE,QAAQ,oEAAoE;AAC5E,QAAQ,2CAA2C;AACnD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,SAAS,EAAE,gGAAgG;AAE/G,IAAI,OAAO,EAAE,6DAA6D;AAC1E,QAAQ,4DAA4D;AACpE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,SAAS,EAAE,+DAA+D;AAE9E,IAAI,OAAO,EAAE,2EAA2E;AACxF,QAAQ,wBAAwB;AAChC,QAAQ,kCAAkC;AAC1C,QAAQ,uEAAuE;AAC/E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,SAAS,EAAE,0BAA0B;AAEzC,IAAI,OAAO,EAAE,4DAA4D;AACzE,QAAQ,kBAAkB;AAC1B,QAAQ,uEAAuE;AAC/E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,SAAS,EAAE,yBAAyB;AACxC,IAAI,OAAO,EAAE,kCAAkC,GAAG,gCAAgC;AAClF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,iDAAiD;AAC3D,IAAI,SAAS,EAAE,kCAAkC;AACjD,IAAI,OAAO,EAAE,mBAAmB;AAChC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,iDAAiD;AAC3D,IAAI,SAAS,EAAE,kCAAkC;AACjD,IAAI,OAAO,EAAE,8DAA8D;AAC3E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,uDAAuD;AACjE,IAAI,SAAS,EAAE,0BAA0B;AACzC,IAAI,OAAO,EAAE,iDAAiD;AAC9D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,wBAAwB;AACvC,IAAI,OAAO,EAAE,+CAA+C;AAC5D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,uDAAuD;AACtE,IAAI,OAAO,EAAE,sDAAsD;AACnE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,qDAAqD;AACpE,IAAI,OAAO,EAAE,yCAAyC;AACtD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,sFAAsF;AACrG,IAAI,OAAO,EAAE,uEAAuE;AACpF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AAEzD,IAAI,OAAO,EAAE,0BAA0B;AACvC,QAAQ,yFAAyF;AACjG,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,SAAS,EAAE,wBAAwB;AACvC,IAAI,OAAO,EAAE,2FAA2F;AACxG,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,OAAO,EAAE,sBAAsB;AACnC,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,kCAAkC;AACjD,IAAI,OAAO,EAAE,0JAA0J;AACvK,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,+DAA+D;AAC5E,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,wBAAwB;AACvC,IAAI,OAAO,EAAE,oDAAoD;AACjE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,8CAA8C;AAC7D,IAAI,OAAO,EAAE,kCAAkC;AAC/C,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,2BAA2B;AAC1C,IAAI,OAAO,EAAE,0JAA0J;AACvK,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,6BAA6B;AAC5C,IAAI,OAAO,EAAE,4CAA4C;AACzD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,SAAS,EAAE,6CAA6C;AAC5D,IAAI,OAAO,EAAE,gCAAgC;AAC7C,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,sFAAsF;AACnG,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAE1D,IAAI,OAAO,EAAE,2CAA2C;AACxD,QAAQ,iEAAiE;AACzE,QAAQ,2BAA2B;AACnC,GAAG;AACH,CAAC,CAAC;AAEK,MAAM,QAAQ,GAAG;AACxB,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,OAAO,EAAE,sEAAsE;AACnF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,iCAAiC;AAC9C,GAAG;AACH,CAAC,CAAC;AAEK,MAAM,MAAM,GAAG,EAAE,CAAC;AAElB,MAAM,OAAO,GAAG;AACvB,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,oCAAoC;AAC9C,IAAI,OAAO,EAAE,2OAA2O;AACxP,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,8CAA8C;AAExD,IAAI,OAAO,EAAE,uFAAuF;AACpG,QAAQ,iIAAiI;AACzI,QAAQ,6BAA6B;AACrC,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,8CAA8C;AAExD,IAAI,OAAO,EAAE,uIAAuI;AACpJ,QAAQ,6HAA6H;AACrI,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,2CAA2C;AACrD,IAAI,OAAO,EAAE,sCAAsC;AACnD,GAAG;AAEH,EAAE,CAAC,EAAE;AACL,IAAI,IAAI,EAAE,2CAA2C;AACrD,IAAI,OAAO,EAAE,wDAAwD;AACrE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,woBAAwoB;AACrpB,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,4JAA4J;AACzK,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,OAAO,EAAE,8EAA8E;AAC3F,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,OAAO,EAAE,2EAA2E;AACxF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,qCAAqC;AAC/C,IAAI,OAAO,EAAE,8JAA8J;AAC3K,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,4EAA4E;AACzF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,4CAA4C;AACtD,IAAI,OAAO,EAAE,+CAA+C;AAC5D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,mEAAmE;AAChF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,0CAA0C;AACpD,IAAI,OAAO,EAAE,yJAAyJ;AACtK,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,iCAAiC;AAC3C,IAAI,OAAO,EAAE,kLAAkL;AAC/L,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,mIAAmI;AAChJ,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,2HAA2H;AACxI,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,+IAA+I;AAC5J,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,mIAAmI;AAChJ,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,wJAAwJ;AACrK,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,qDAAqD;AAClE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,iDAAiD;AAC9D,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,gDAAgD;AAC1D,IAAI,OAAO,EAAE,wDAAwD;AACrE,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AAEzD,IAAI,OAAO,EAAE,wEAAwE;AACrF,QAAQ,+DAA+D;AACvE,GAAG;AACH,CAAC,CAAC;AAEK,MAAM,QAAQ,GAAG;AACxB,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,4CAA4C;AACzD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,sCAAsC;AACnD,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,wCAAwC;AAClD,IAAI,OAAO,EAAE,iBAAiB;AAC9B,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,oDAAoD;AAC9D,IAAI,OAAO,EAAE,sEAAsE;AACnF,GAAG;AAEH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,+CAA+C;AACzD,IAAI,OAAO,EAAE,sCAAsC;AACnD,GAAG;AACH,CAAC;;AC7lBM,IAAI,OAAO,GAAG,QAAQ;;ACAtB,SAAS,KAAK,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI;AACR,QAAQ,OAAO,KAAK,EAAE,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,GAAG;AAClB;;ACJA,eAAe,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;AACzD,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AACzC,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AACvC,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AAMzC,KAAK,CAAC,YAAY;AAClB,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9C,CAAC,CAAC;;ACZF,IAAI,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AAGtB,SAAS,YAAY,CAAC,MAAM,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG;;ACNO,SAAS,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE;AAClD,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;AACxC,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,qBAAqB,CAAC,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;AACvD,QAAQ,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;AAClD,KAAK,EAAE,KAAK,CAAC;AACb,SAAS,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACvC,SAAS,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7B;;ACLA,SAAS,IAAI,CAAC,EAAE,EAAE;AAClB,IAAI,OAAO,UAAU,OAAO,EAAE;AAC9B,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACzC,YAAY,IAAI,IAAI,GAAG,OAAO,CAAC;AAC/B,YAAY,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/C,YAAY,IAAI,CAAC,OAAO,EAAE;AAC1B,gBAAgB,OAAO,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1D,gBAAgB,IAAI,GAAG,EAAE,CAAC;AAC1B,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,KAAK,CAAC;AACN,CAAC;AACe,MAAM,CAAC,MAAM,CAAC,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACrE,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,QAAQA,qBAAiB,CAAC,SAAS,EAAE,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9G,KAAK;AACL,CAAC,EAAE;AACH,IAAI,KAAK,EAAE,IAAI,CAACA,qBAAiB,CAAC,KAAK,CAAC;AACxC,IAAI,GAAG,EAAE,IAAI,CAACA,qBAAiB,CAAC,GAAG,CAAC;AACpC,IAAI,IAAI,EAAE,IAAI,CAACA,qBAAiB,CAAC,IAAI,CAAC;AACtC,IAAI,KAAK,EAAE,IAAI,CAACA,qBAAiB,CAAC,KAAK,CAAC;AACxC,CAAC,EAAE;AAmBH,IAAI,yBAAyB,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,GAAG,OAAO,CAAC,CAAC;AACnF,SAAS,SAAS,CAAC,GAAG,EAAE;AACxB,IAAI,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE;AAChC,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,IAAI;AACR,QAAQ,OAAO,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,EAAE,EAAE;AACf,QAAQ,OAAO,oBAAoB,CAAC;AACpC,KAAK;AACL,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE;AAClD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,CAAC,OAAO;AAChB,QAAQ,OAAO;AACf,IAAI,QAAQC,QAAM,CAAC,yBAAyB,CAAC;AAC7C,QAAQA,QAAM,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE;AAChF,CAAC;AACD,SAAS,mBAAmB,CAAC,OAAO,EAAE,WAAW,EAAE;AACnD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,CAAC,OAAO;AAChB,QAAQ,OAAO;AACf,IAAI,OAAO,8FAA8F,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC;AACnJ,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;AACxC,KAAK,CAAC,CAAC,CAAC,CAAC;AACT;;AC5EO,SAAS,sBAAsB,CAAC,OAAO,EAAE;AAChD,IAAIA,cAAM,CAAC,yBAAyB,CAAC,GAAG,OAAO,CAAC;AAChD;;ACAO,SAAS,uBAAuB,GAAG;AAC1C,IAAI,IAAI,UAAU,GAAG,EAAE,CAAC;AACxB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,UAAU,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAC;AACpC,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,GAAG,UAAU,EAAE,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAChF,QAAQ,IAAI,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC;AACrC,QAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACD,IAAI,OAAO,IAAI,UAAU,OAAO,EAAE,IAAI,EAAE;AACxC,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACrC,QAAQ,IAAI,UAAU,GAAGA,cAAM,CAAC,yBAAyB,CAAC,CAAC,OAAO,CAAC,CAAC;AACpE,QAAQ,IAAI,CAAC,OAAO,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC;AACrG,YAAY,OAAO;AACnB,QAAQ,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACrC,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7G,CAAC,CAAC;;ACzBK,SAAS,eAAe,GAAG;AAClC,IAAI,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACjE;;ACFO,SAAS,iBAAiB,GAAG;AACpC,IAAI,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACxC;;;;;;;"}