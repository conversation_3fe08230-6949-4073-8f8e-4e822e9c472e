import { useEffect, useState } from "react";
import { Route, Switch, useHistory, useParams, useRouteMatch } from "react-router-dom";
import { useQuery } from "@apollo/client";

import { PCContainer, PCProductInfoCol, PCProductCatCol, PCOurProductsHeading, PCProductHeading, PCProductImage, PCProductInfo, PCProductWrapper, PCProduct } from "./style";
import MainLayout from "../../../component/globals/Layout";
import generalProductQuery from "../../../graphql/generalProduct.gql";
import ProductIndividualScreen from "../Individual";
import { stripHtml } from "../../../util/stripHtml";
import Breadcrumbs from "../../../component/globals/Breadcrumbs/Breadcrumbs";

const ProductCategoryScreen = (props) => {
    const [isClient, setIsClient] = useState(false);
    const { prodCat } = useParams();
    const { url, path } = useRouteMatch();
    const history = useHistory();
    const { data, loading, error } = useQuery(generalProductQuery,{
      variables: {
        id: prodCat
      }
    });

    const renderProductInfo = () => {
      const prodInfo = data?.categoriesProduct?.acfProductCategory;
      const prodDesc = data?.categoriesProduct?.description;
      const prodImg = prodInfo?.productSectionImage;
      return (
        <>
          <PCProductHeading as="h2" color="black" font="2xl">
            <span>{prodInfo.productName}</span>
          </PCProductHeading>
          <PCProductImage>
            <img alt={prodImg.altText ?? ""} src={prodImg.mediaItemUrl ?? ""} width={250}/>
          </PCProductImage>
          <PCProductInfo>
            <p>Description:</p>
            <p>{prodDesc}</p>
          </PCProductInfo>
          <PCProductInfo>
            <p>Common Usage:</p>
            <p>{prodInfo.productUsage}</p>
          </PCProductInfo>
        </>
      )
    }

    const renderProductCat = () => {
      const prodChildren = data?.categoriesProduct?.children?.nodes;
      return prodChildren.map((child) => (
        <PCProductWrapper key={child.id}>
          <PCProduct to={`${url}${child.slug}`}>
            <img alt={child.acfProductCategory?.categoryimg?.altText ?? ""} 
                src={child.acfProductCategory?.categoryimg?.mediaItemUrl ?? ""}
                width={isClient ? (stripHtml(child.acfProductCategory?.categoryimg?.caption) || "") : ""}/> 
            <span>{child.name}</span>
          </PCProduct> 
        </PCProductWrapper>
      ))
    }

    useEffect(() => {
      setIsClient(true);
    }, []);

    // Define breadcrumb items
    const breadcrumbItems = [
      { name: "Home", link: "/" },
      { name: "Products", link: "/products" },
    ];
    if (data && data.categoriesProduct) {
      breadcrumbItems.push({ name: data.categoriesProduct.name, link: `/products/${prodCat}` });
    }

    return (
        <MainLayout>
          {
            !loading && data &&
              <PCContainer>
                <Breadcrumbs items={breadcrumbItems} />
                <PCProductInfoCol>
                  <PCOurProductsHeading as="h3">
                    Our Products
                  </PCOurProductsHeading>
                  { renderProductInfo() }
                </PCProductInfoCol>
                <PCProductCatCol>
                  { renderProductCat() }
                </PCProductCatCol>
              </PCContainer>
          }
        </MainLayout>
     );
}
export const ProductCatRoute = () => {
  const { url, path } = useRouteMatch();
  return (
    <Switch>
      <Route
        exact
        path={path}
        children={<ProductCategoryScreen/>}
      />
      <Route path={`${path}/:subCat`} children={<ProductIndividualScreen />} />
      {/* <Route path={`${path}/equal-angle`} children={<ProductIndividualScreen />} /> */}
    </Switch>
  );
}
 
export default ProductCatRoute;
