import { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { toast } from "react-toastify";
import { Route, useParams, useRouteMatch, Switch, useHistory, useLocation } from "react-router-dom";


import MainLayout from "../../../component/globals/Layout";
import Icon from "../../../partials/Icon";
import Breadcrumbs from "../../../component/globals/Breadcrumbs/Breadcrumbs";
import {
  ConveyorAccessoriesImage,
  ConveyorAccessoriesInfoList,
  ConveyorAccessoriesList,
  ConveyorAccessoriesListItem,
  ConveyorAccessoriesListWrapper,
  ConveyorAccessoriesMoreInfo,
  ConveyorAccessoriesSpecs,
  ConveyorAccessoriesSpecsHeading,
  ConveyorContainer,
  ConveyorDivideLine,
  ConveyorHeadingTitle,
  ConveyorHeadingWrapper,
  ConveyorImageBg,
  ConveyorImageBgContent,
  ConveyorImageBgOverlay,
  ConveyorMoreInfoImg,
  ConveyorMoreInfoList,
  ConveyorMoreInfoListItem,
  ConveyorProduct,
  ConveyorProductAccessoriesBlock,
  ConveyorProductAccessoriesContainer,
  ConveyorProductAccessoriesDetails,
  ConveyorProductAccessoriesHeading,
  ConveyorProductAccessoriesName,
  ConveyorProductAccessoriesWrapper,
  ConveyorProductDetails,
  ConveyorProductHorizontal,
  ConveyorProductImage,
  ConveyorProductMoreInfo,
  ConveyorProductMoreInfoBtn,
  ConveyorProductMoreInfoContent,
  ConveyorProductName,
  ConveyorProductSpecs,
  ConveyorProductSpecsList,
  ConveyorProductSpecsListItem,
  ConveyorSubCat,
  ConveyorSubNav,
  ConveyorSubNavItem,
  ConveyorSubNavItemIcon,
  ConveyorSubNavItemIconText,
  ConveyorSubNavItemOverlay,
  ConveyorSubNavItemText,
} from "./style";
import Button from "../../../component/globals/Button";
import productCategories from "../../../graphql/productCategories.gql";
import conveyorProducts from "../../../graphql/conveyorProducts.gql";
import {
  ConveyorALProfileString,
  ConveyorAccessoriesString,
  ConveyorItemNameString,
  ConveyorLengthString,
  ConveyorProfileString,

  ConveyorSectionNoString,
  ConveyorSectionString,
  ConveyorUomString,
} from "../../../constant";
import { addToCart } from "../../../state/state.global";

const ProductConveyorScreen = ({catData, catLoading ,...props}) => {
  const [selectedID, setSelectedID] = useState("");
  const { slug } = useParams();
  const history = useHistory();
  const location = useLocation();

  // Use series-20 as default when no slug is provided
  const effectiveSlug = slug || "series-20";

  const {
    data: prodData,
    loading: prodLoading,
    error: prodError,
  } = useQuery(conveyorProducts, {
    variables: {
      terms1: "accessories",
      terms2: effectiveSlug,
      operator1: "NOT_IN",
      operator2: "IN",
    },
  });

  const {
    data: accessData,
    loading: accessLoading,
    error: accessError,
  } = useQuery(conveyorProducts, {
    variables: {
      terms1: "accessories",
      terms2: effectiveSlug,
      operator1: "IN",
      operator2: "IN",
    },
  });

  const handleButtonClick = (id) => {
    if (id === selectedID) {
      setSelectedID("");
    } else {
      setSelectedID(id);
    }
  };
  const handleProductTypeClick = (slug) => {
    // navigate
    const lastChar = location.pathname.slice(-1);
    const slugToPush = lastChar === '/' ? `/products/conveyor/${slug}` : slug;
    history.push(slugToPush);
  };

  const handleAddToCart = (prod, isAccessory = false) => {
    if (!prod) return;
    const acfSettings = prod.acfProductSettings;
    const prodDescription = acfSettings.description;
    const prodInfo = acfSettings.productInfo;
    const matches = effectiveSlug.match(/(\d+)/);
    let prodSubCat = "SERIES ";
    let prodImg = acfSettings.productInfoImage?.mediaItemUrl;
    if (!prodImg) prodImg = acfSettings.productImage?.mediaItemUrl;
    if (isAccessory){
      prodSubCat = "ACCESSORIES "
    }
    const parser = new DOMParser();
    let color = "";
    let grade = "";
    let description = "";
    
    if (prodDescription){
      const descDom = parser.parseFromString(prodDescription,"text/html");
      const liInnerText = [...descDom.getElementsByTagName("li")].map(li => li.innerHTML);
      description = liInnerText.join(" ");
    }
    if (prodInfo){
      const prodInfoDom = parser.parseFromString(prodInfo,"text/html");
      const li = prodInfoDom.getElementsByTagName("li");
      color = li[0].innerText.toLowerCase().replace(/colou?r: /,"");
      grade = li[1].innerText.toLowerCase().replace("grade: ","");;
    }

    const prodObj = {
      key: prod.id,
      itemNumber: acfSettings.itemNumber,
      setItems: acfSettings.setItems,
      section: acfSettings.sectionNo,
      itemName: prod.title,
      description,
      prodSubCat: prodSubCat + matches[0],
      color,
      grade,
      prodImg,
      isConveyor: true,
    }

    addToCart(prodObj);
    toast.success("Item added!");
  }

  const renderConveyorHeading = (data) => {
    if (!data) return;
    const catImgObj =
      data.categoriesProduct?.acfProductCategory?.productSectionImage;
    return (
      <ConveyorImageBg bgImg={catImgObj.mediaItemUrl}>
        <ConveyorImageBgOverlay />
        <ConveyorImageBgContent>
          <ConveyorHeadingWrapper>
            <ConveyorHeadingTitle as="h2" color="white" font="2xl">
              <span>
                {ConveyorSectionString}
                <br />
                <span>{ConveyorALProfileString}</span>
              </span>
            </ConveyorHeadingTitle>
          </ConveyorHeadingWrapper>
        </ConveyorImageBgContent>
      </ConveyorImageBg>
    );
  };

  const renderSubNav = (data) => {
    if (!data) return;
    const dataArr = data.categoriesProduct?.children?.nodes;
    return (
      <ConveyorSubNav>
        {dataArr.length > 0 &&
          dataArr.map((navItem, idx) => (
            <ConveyorSubNavItem onClick={() => handleProductTypeClick(navItem.slug)} key={navItem.id}>
              <ConveyorSubNavItemIconText>
                <ConveyorSubNavItemIcon>
                  <ConveyorSubNavItemOverlay />
                  <img
                    src={navItem.acfProductCategory?.categoryimg?.mediaItemUrl}
                    alt={navItem.acfProductCategory?.categoryimg?.altText}
                    width={`${idx * 10 + 45}`}
                  />
                </ConveyorSubNavItemIcon>
                <ConveyorSubNavItemText>{navItem.name}</ConveyorSubNavItemText>
              </ConveyorSubNavItemIconText>
            </ConveyorSubNavItem>
          ))}
      </ConveyorSubNav>
    );
  };

  /**
   *
   * @param {boolean} isVertical
   *
   */
  const renderMoreInfo = (isVertical, data, id) => (
    <ConveyorProductMoreInfo isVertical={isVertical}>
      <ConveyorProductMoreInfoBtn
        onClick={() => handleButtonClick(id)}
        isVertical={isVertical}
        isActive={selectedID === id}
      >
        <Icon
          icon={
            isVertical
              ? selectedID === id
                ? "white-left-caret"
                : "white-right-caret"
              : selectedID === id
              ? "white-down-caret"
              : "white-up-caret"
          }
        />
        {selectedID === id ? "HIDE INFO" : "MORE INFO"}
      </ConveyorProductMoreInfoBtn>
      <ConveyorProductMoreInfoContent
        isVertical={isVertical}
        isActive={selectedID === id}
      >
        <ConveyorMoreInfoImg bgImg={data.productInfoImage?.mediaItemUrl ?? ""} />
        <ConveyorDivideLine />
        <ConveyorMoreInfoList
          dangerouslySetInnerHTML={{
            __html: data.productInfo,
          }}
        ></ConveyorMoreInfoList>
      </ConveyorProductMoreInfoContent>
    </ConveyorProductMoreInfo>
  );

  const renderProduct = (data) => {
    if (!data) return;
    data = data.products?.nodes;
    return (
      <ConveyorSubCat>
        <ConveyorProduct>
          {data.length > 0 &&
            data.map((prod, idx) => (
              <ConveyorProductHorizontal key={prod.id}>
                <ConveyorProductName as="h2" color="white" font="lg">
                  <span>
                    {ConveyorItemNameString} {prod.title ?? ""}{" "}
                    {ConveyorProfileString}
                  </span>
                </ConveyorProductName>
                <ConveyorProductDetails>
                  <ConveyorProductSpecs>
                    <ConveyorProductSpecsList>
                      <ConveyorProductSpecsListItem>
                        <span>{ConveyorSectionNoString} </span>
                        <span>{prod.acfProductSettings.sectionNo ?? ""}</span>
                      </ConveyorProductSpecsListItem>
                      <ConveyorProductSpecsListItem>
                        <span>{ConveyorLengthString} </span>
                        <span>{prod.acfProductSettings.length ?? ""}</span>
                      </ConveyorProductSpecsListItem>
                      <ConveyorProductSpecsListItem>
                        <span>{ConveyorUomString} </span>
                        <span>{prod.acfProductSettings.uom ?? ""}</span>
                      </ConveyorProductSpecsListItem>
                    </ConveyorProductSpecsList>
                  </ConveyorProductSpecs>
                  <ConveyorProductImage>
                    <img
                      src={
                        prod.acfProductSettings.productImage.mediaItemUrl ?? ""
                      }
                      alt={prod.acfProductSettings.productImage.altText ?? ""}
                    />
                  </ConveyorProductImage>
                  {renderMoreInfo(true, prod.acfProductSettings, prod.id)}
                </ConveyorProductDetails>
                <Button
                  variant="iconBg"
                  iconHeight="28"
                  iconWidth="28"
                  icon="plus"
                  onClick={() => handleAddToCart(prod)}
                >
                  add to enquiry
                </Button>
                {renderMoreInfo(false, prod.acfProductSettings, prod.id)}
              </ConveyorProductHorizontal>
            ))}
        </ConveyorProduct>
      </ConveyorSubCat>
    );
  };

  const renderAccessories = (data) => {
    if (!data) return;
    data = data.products?.nodes;
    if (data.length <= 0) return;
    return (
      <ConveyorSubCat>
        <ConveyorProductAccessoriesHeading as="h2" color="white" font="4xl">
          <span>{ConveyorAccessoriesString}</span>
        </ConveyorProductAccessoriesHeading>
        <ConveyorProductAccessoriesContainer>
          {data.length > 0 &&
            data.map((asProd, idx) => (
              <ConveyorProductAccessoriesWrapper key={asProd.id}>
                <ConveyorProductAccessoriesBlock>
                  <ConveyorProductAccessoriesName
                    as="h2"
                    color="white"
                    font="lg"
                  >
                    <span>{asProd.title}</span>
                  </ConveyorProductAccessoriesName>
                  <ConveyorProductAccessoriesDetails>
                    <ConveyorAccessoriesImage>
                      <img
                        src={
                          asProd.acfProductSettings?.productImage
                            ?.mediaItemUrl ?? ""
                        }
                        alt={
                          asProd.acfProductSettings?.productImage?.altText ?? ""
                        }
                      />
                    </ConveyorAccessoriesImage>
                    <ConveyorAccessoriesSpecs>
                      <ConveyorAccessoriesSpecsHeading as="h4" color="white">
                        <span>
                          {ConveyorSectionNoString}{" "}
                          {asProd.acfProductSettings?.sectionNo}
                        </span>
                      </ConveyorAccessoriesSpecsHeading>
                      <ConveyorAccessoriesListWrapper
                        dangerouslySetInnerHTML={{
                          __html: asProd.acfProductSettings?.description,
                        }}
                      ></ConveyorAccessoriesListWrapper>
                    </ConveyorAccessoriesSpecs>
                    <Button
                      variant="iconBg"
                      iconHeight="28"
                      iconWidth="28"
                      icon="plus"
                      onClick={() => handleAddToCart(asProd, true)}
                    >
                      add to enquiry
                    </Button>
                    <ConveyorProductMoreInfo isAccessory>
                      <ConveyorProductMoreInfoBtn
                        onClick={() => handleButtonClick(asProd.id)}
                        isActive={selectedID === asProd.id}
                      >
                        <Icon
                          icon={
                            selectedID === asProd.id
                              ? "white-down-caret"
                              : "white-up-caret"
                          }
                        />
                        {selectedID === asProd.id ? "HIDE INFO" : "MORE INFO"}
                      </ConveyorProductMoreInfoBtn>
                    </ConveyorProductMoreInfo>
                  </ConveyorProductAccessoriesDetails>
                  <ConveyorAccessoriesMoreInfo
                    isActive={selectedID === asProd.id}
                  >
                    <div>
                      <ConveyorAccessoriesInfoList
                        dangerouslySetInnerHTML={{
                          __html: asProd.acfProductSettings?.productInfo,
                        }}
                      ></ConveyorAccessoriesInfoList>
                    </div>
                  </ConveyorAccessoriesMoreInfo>
                </ConveyorProductAccessoriesBlock>
              </ConveyorProductAccessoriesWrapper>
            ))}
        </ConveyorProductAccessoriesContainer>
      </ConveyorSubCat>
    );
  };

  // Define breadcrumb items
  const breadcrumbItems = [
    { name: "Home", link: "/" },
    { name: "Products", link: "/products" },
    { name: "Conveyor", link: "/products/conveyor" },
  ];
  if (catData && catData.categoriesProduct?.children?.nodes && effectiveSlug) {
    const currentSeries = catData.categoriesProduct.children.nodes.find(node => node.slug === effectiveSlug);
    if (currentSeries) {
      // Only add series breadcrumb if we have a specific slug (not the default)
      if (slug) {
        breadcrumbItems.push({ name: currentSeries.name, link: `/products/conveyor/${slug}` });
      }
    }
  }

  return (
    <MainLayout isHomeHeader disableAbs isBlackBg isWhiteLogo>
      <ConveyorContainer>
        {!catLoading && !prodLoading && !accessLoading && (
          <>
            <Breadcrumbs items={breadcrumbItems} isOnDark={true} containerType="conveyor" />
            {renderConveyorHeading(catData)}
            {renderSubNav(catData)}
            {renderProduct(prodData)}
            {renderAccessories(accessData)}
          </>
        )}
      </ConveyorContainer>
    </MainLayout>
  );
};

const ConveyorRouter = () => {
  const { path, url } = useRouteMatch();
  const {data, loading, error} = useQuery(productCategories, {
    variables: {
      id: "conveyor",
      nameLike: "series" // to filter accessories out
    },
  });



  return (
    <Switch>
        {/* Render page directly for exact path */}
        <Route exact path={path} render={() => <ProductConveyorScreen catData={data} catLoading={loading}/>}/>
        <Route path={`${path}/:slug`} render={() => <ProductConveyorScreen catData={data} catLoading={loading}/>}/>
    </Switch>
  )
}

export default ConveyorRouter;
