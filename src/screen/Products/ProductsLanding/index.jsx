import { useQuery } from "@apollo/client";
import { Link } from "react-router-dom";
import productCategoriesLanding from "../../../graphql/productCategoriesLanding.gql";
import MainLayout from "../../../component/globals/Layout";
import Breadcrumbs from "../../../component/globals/Breadcrumbs/Breadcrumbs";
import {
  PLContainer,
  PLHeading,
  PLProductGrid,
  PLProductItem,
  PLProductImage,
  PLProductName } from "./style";

const ProductsLandingScreen = (props) => {
  const { data, loading, error } = useQuery(productCategoriesLanding);

  const breadcrumbItems = [
    { name: "Home", link: "/" },
    { name: "Products", link: "/products" },
  ];

  const renderProductCategories = () => {
    if (loading || !data) return null;

    return data.allCategoriesProduct?.nodes?.map((category) => (
      <PLProductItem key={category.slug}>
        <Link to={`/products/${category.slug}`}>
          <PLProductImage>
            <img
              src={category.acfProductCategory?.categoryimg?.sourceUrl}
              alt={category.acfProductCategory?.categoryimg?.altText || category.name}
            />
          </PLProductImage>
          <PLProductName>{category.name}</PLProductName>
        </Link>
      </PLProductItem>
    ));
  };

  return (
    <MainLayout>
      <PLContainer>
        <Breadcrumbs items={breadcrumbItems} />
        <PLHeading as="h1" font="3xl" color="primary">
          Our Products
        </PLHeading>
        <PLProductGrid>
          {renderProductCategories()}
        </PLProductGrid>
      </PLContainer>
    </MainLayout>
  );
};

export default ProductsLandingScreen;
