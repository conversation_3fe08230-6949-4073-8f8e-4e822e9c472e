import { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import { InView } from "react-intersection-observer";

import aboutUsQuery from "../../graphql/aboutUsTemplate.gql";
import AboutUsMastHead from "../../component/aboutUs/MastHead";
import MainLayout from "../../component/globals/Layout";
import Breadcrumbs from "../../component/globals/Breadcrumbs/Breadcrumbs";
import {
  AboutUsSection,
  AboutUsOurStoryContainer,
  AboutUsOurStoryWrapper,
  AboutUsWrapper,
  AboutUsTitle,
  AboutUsContent,
  AboutUsIndustriesTitle,
  AboutUsIndustriesContainer,
  AboutUsIndustriesIconWrapper,
  AboutUsIndustriesIcon,
  AboutUsIndustriesIconText,
  AboutUsIndustriesDescription,
  AboutUsCommonContainer
} from "./style";
import { AboutUsIndustriesIntro, AboutUsOurHistory, AboutUsOurStory, AboutUsOurValues, ScreenBreakpoints } from "../../constant";
import tw from "twin.macro";
import AboutUsOurValuesComponent from "../../component/aboutUs/OurValues";
import AboutUsOurHistoryComponent from "../../component/aboutUs/OurHistory";

const AboutUsScreen = () => {
  const { data, error, loading } = useQuery(aboutUsQuery);

  return (
    <MainLayout isHomeHeader isWhiteLogo>
      <AboutUsWrapper isLoading={loading || !data.nodeByUri?.template?.aboutUsSettings}>
        {!loading && data.nodeByUri?.template?.aboutUsSettings && (
          <>
          <AboutUsMastHead
            bgImg={data.nodeByUri?.template?.aboutUsSettings ? data.nodeByUri?.template?.aboutUsSettings.ourStoryImage.mediaItemUrl : ""}
            >
            <AboutUsOurStoryWrapper>
              <AboutUsOurStoryContainer>
                <AboutUsSection as="h2" font="md-300">
                  <span>{AboutUsOurStory}</span>
                </AboutUsSection>
                <AboutUsTitle as="h3" font="lg-line" color="white">
                  <span>{data.nodeByUri?.template?.aboutUsSettings.ourStoryHeading ?? ""}</span>
                </AboutUsTitle>
                <AboutUsContent
                    dangerouslySetInnerHTML={{
                      __html: data.nodeByUri?.template?.aboutUsSettings.ourStoryContent ?? "",
                    }}
                />
              </AboutUsOurStoryContainer>
            </AboutUsOurStoryWrapper>
            <AboutUsIndustriesTitle as="p">
              {AboutUsIndustriesIntro}
            </AboutUsIndustriesTitle>
            <AboutUsIndustriesContainer>
              <AboutUsIndustriesIconWrapper>
                {data.nodeByUri?.template?.aboutUsSettings.ourStoryIndustryIcons.map(icon => (
                  <AboutUsIndustriesIcon key={icon.id}>
                    <div>
                      <img src={icon.mediaItemUrl} alt={icon.altText} width={50}/>
                    </div>
                    <AboutUsIndustriesIconText>
                      {icon.title}
                    </AboutUsIndustriesIconText>
                  </AboutUsIndustriesIcon>
                ))}
              </AboutUsIndustriesIconWrapper>
              <AboutUsIndustriesDescription dangerouslySetInnerHTML={{
                __html: data.nodeByUri?.template?.aboutUsSettings.ourStoryIndustryDescriptions
              }}/>
            </AboutUsIndustriesContainer>
          </AboutUsMastHead>
          <InView triggerOnce>
            {({inView,ref}) => 
            <AboutUsCommonContainer ref={ref}>
              {inView && 
                <AboutUsOurValuesComponent
                heading={data.nodeByUri?.template?.aboutUsSettings.ourValuesHeading ?? ""}
                content={data.nodeByUri?.template?.aboutUsSettings.ourValuesContent ?? ""}
                mediaItemUrl={data.nodeByUri?.template?.aboutUsSettings.ourValuesImage.mediaItemUrl}
                altText={data.nodeByUri?.template?.aboutUsSettings.ourValuesImage.altText}
                />
              }
            </AboutUsCommonContainer>}
          </InView>
          <InView triggerOnce>
            {({inView,ref}) => 
            <AboutUsCommonContainer ref={ref} customStyle={tw`border-0`}>
              {inView && 
                <AboutUsOurHistoryComponent
                heading={data.nodeByUri?.template?.aboutUsSettings.ourHistoryHeading ?? ""}
                content={data.nodeByUri?.template?.aboutUsSettings.ourHistoryContent ?? ""}
                mediaItemUrl={data.nodeByUri?.template?.aboutUsSettings.ourHistoryImage.mediaItemUrl}
                altText={data.nodeByUri?.template?.aboutUsSettings.ourHistoryImage.altText}
                />
              }
            </AboutUsCommonContainer>
          }
          </InView>
          </>
        )}
      </AboutUsWrapper>
    </MainLayout>
  );
};

export default AboutUsScreen;
